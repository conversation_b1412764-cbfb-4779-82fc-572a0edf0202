--------------------------------------------------------
--  DDL for Table INSURANCE_PLAN
--------------------------------------------------------

  CREATE TABLE "LIBERTY_DATA"."INSURANCE_PLAN"
   (	"INS_PLAN_ID" NUMBER(10,0), 
	"MASTER_PLAN" NUMBER(10,0), 
	"CHILD_PLAN_ID" NUMBER(10,0), 
	"PARENT_PLAN_ID" NUMBER(10,0), 
	"INS_COMPANY_ID" NUMBER(10,0), 
	"PLAN_TYPE" VARCHAR2(2 BYTE), 
	"INSURANCE_PLAN" NUMBER(10,0), 
	"PLAN_NAME" VARCHAR2(50 BYTE), 
	"GENERATE_SCHEDULE" CHAR(1 BYTE), 
	"GENERATION_DAYS" NUMBER(5,0), 
	"CHILD_MAX_AGE" NUMBER(2,0), 
	"STUDENT_MAX_AGE" NUMBER(2,0), 
	"MAX_INDIVIDUAL" NUMBER(7,2), 
	"MAX_FAMILY" NUMBER(7,2), 
	"MAX_ORTHO" NUMBER(7,2), 
	"DEDUCTABLE_INDIVIDUAL" NUMBER(7,2), 
	"DEDUCTABLE_FAMILY" NUMBER(7,2), 
	"LIFE_TIME_MAX" NUMBER(7,2), 
	"LIFE_TIME_DEDUCTABLE" NUMBER(7,2), 
	"GROUP_NUMBER" VARCHAR2(20 BYTE), 
	"PLAN_START_MONTH" NUMBER(2,0), 
	"PLAN_STATUS" CHAR(1 BYTE), 
	"MASTER_PLAN_STATUS" CHAR(1 BYTE), 
	"VERSION_EFFECTIVE_DATE" DATE, 
	"VERSION_INACTIVE_DATE" DATE, 
	"TERM_DATE" DATE, 
	"NON_COVERED_DISCOUNT" NUMBER(5,4), 
	"USE_CDT_OVERRIDE" CHAR(1 BYTE), 
	"TIMELY_FILING_DAYS" NUMBER(5,0), 
	"ACCEPT_ELECTRONIC_CLAIMS" CHAR(1 BYTE), 
	"CLAIM_PRICE_OPTION" VARCHAR2(10 BYTE), 
	"ACCEPT_ELEC_ATTACH" CHAR(1 BYTE), 
	"ENCOUNTER_OPTION" VARCHAR2(10 BYTE), 
	"CONTRACTED_MEMBERSHIP" NUMBER(7,0), 
	"MC_PMPM" NUMBER(7,2), 
	"OFFICE_VISIT_CO_PAY" NUMBER(7,2), 
	"OFFICE_VISIT_INS_PAY" NUMBER(7,2), 
	"OFFICE_VISIT_CDT" VARCHAR2(5 BYTE), 
	"OFFICE_VISIT_PROD" NUMBER(7,0), 
	"COMPLEX_TREATMENT_CODE" VARCHAR2(3 BYTE), 
	"COMPLEX_TREAT_QTY" NUMBER(2,0), 
	"COMPLEX_FEE_PER_TOOTH" NUMBER(7,2), 
	"CREATE_EMPLOYEE" NUMBER(10,0), 
	"CREATE_DATETIME" TIMESTAMP (6), 
	"UPDATE_EMPLOYEE" NUMBER(10,0), 
	"UPDATE_DATETIME" TIMESTAMP (6)
   ) PCTFREE 10 PCTUSED 40 INITRANS 1 MAXTRANS 255 NOCOMPRESS LOGGING
  STORAGE(INITIAL 65536 NEXT 1048576 MINEXTENTS 1 MAXEXTENTS 2147483645
  PCTINCREASE 0 FREELISTS 1 FREELIST GROUPS 1 BUFFER_POOL DEFAULT)
  TABLESPACE "BNDNEW_DATA" ;
