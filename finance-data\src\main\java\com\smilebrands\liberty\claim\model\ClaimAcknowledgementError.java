package com.smilebrands.liberty.claim.model;

import com.smilebrands.liberty.model.BaseObject;
import com.smilebrands.liberty.utility.JsonDateDeSerializer;
import com.smilebrands.liberty.utility.JsonDateSerializer;
import org.codehaus.jackson.map.annotate.JsonDeserialize;
import org.codehaus.jackson.map.annotate.JsonSerialize;

import javax.persistence.*;
import java.util.Date;

/*
* <PERSON><PERSON> - Smile Brands Inc. 8/9/12
*
*/
@Entity
@Table(name = "CLAIM_ACK_ERROR")
@SuppressWarnings("serial")
public class ClaimAcknowledgementError extends BaseObject {

    @Id
    @Column(name = "CLAIM_ACK_ERROR_ID")
    @GeneratedValue(generator = "ClaimAckErrSeq")
    @SequenceGenerator(name = "ClaimAckErrSeq", sequenceName = "CLAIM_ACK_ERR_SEQ", allocationSize = 1)
    protected Long claimAcknowledgementErrorId;

    @Column(name = "CLAIM_ACK_ID")
    protected Long claimAcknowledgementId;

    @Column(name = "ERROR_TYPE")
    protected String errorType;

    @Column(name = "ERROR_CODE")
    protected String errorCode;

    @Column(name = "SEGMENT_NAME")
    protected String segmentName;

    @Column(name = "ELEMENT_NAME")
    protected String elementName;

    @Column(name = "ELEMENT_POSITION")
    protected Integer elementPosition;

    @Column(name = "ERROR_DATA")
    protected String errorData;

    @Temporal(TemporalType.DATE)
    @Column(name = "ERROR_DATETIME")
    private Date errorDatetime = new Date();


    public Long getClaimAcknowledgementErrorId() {
        return claimAcknowledgementErrorId;
    }

    public void setClaimAcknowledgementErrorId(Long claimAcknowledgementErrorId) {
        this.claimAcknowledgementErrorId = claimAcknowledgementErrorId;
    }

    public String getErrorType() {
        return errorType;
    }

    public void setErrorType(String errorType) {
        this.errorType = errorType;
    }

    public String getErrorCode() {
        return errorCode;
    }

    public void setErrorCode(String errorCode) {
        this.errorCode = errorCode;
    }

    public String getSegmentName() {
        return segmentName;
    }

    public void setSegmentName(String segmentName) {
        this.segmentName = segmentName;
    }

    public String getElementName() {
        return elementName;
    }

    public void setElementName(String elementName) {
        this.elementName = elementName;
    }

    public Integer getElementPosition() {
        return elementPosition;
    }

    public void setElementPosition(Integer elementPosition) {
        this.elementPosition = elementPosition;
    }

    public String getErrorData() {
        return errorData;
    }

    public void setErrorData(String errorData) {
        this.errorData = errorData;
    }

    @JsonSerialize(using = JsonDateSerializer.class)
    public Date getErrorDatetime() {
        return errorDatetime;
    }

    @JsonDeserialize(using = JsonDateDeSerializer.class)
    public void setErrorDatetime(Date errorDatetime) {
        this.errorDatetime = errorDatetime;
    }

    @Override
    public boolean equals(Object o) {
        if (this == o) return true;
        if (o == null || getClass() != o.getClass()) return false;

        ClaimAcknowledgementError that = (ClaimAcknowledgementError) o;

        if (claimAcknowledgementErrorId != null ? !claimAcknowledgementErrorId.equals(that.claimAcknowledgementErrorId) : that.claimAcknowledgementErrorId != null)
            return false;

        return true;
    }

    @Override
    public int hashCode() {
        return claimAcknowledgementErrorId != null ? claimAcknowledgementErrorId.hashCode() : 0;
    }

    public Long getClaimAcknowledgementId() {
        return claimAcknowledgementId;
    }

    public void setClaimAcknowledgementId(Long claimAcknowledgementId) {
        this.claimAcknowledgementId = claimAcknowledgementId;
    }
}