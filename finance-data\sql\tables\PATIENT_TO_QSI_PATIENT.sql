--------------------------------------------------------
--  DDL for Table PATIENT_TO_QSI_PATIENT
--------------------------------------------------------

  CREATE TABLE "LIBERTY_DATA"."PATIENT_TO_QSI_PATIENT"
   (	"PATIENT_TO_QSI_ID" NUMBER, 
	"PATIENT_ID" NUMBER, 
	"QSI_CLINIC_ID" NUMBER, 
	"QSI_UNIQUE_ID" NUMBER, 
	"QSI_PATIENT_ID" NUMBER, 
	"QSI_NAME_FIRST" VARCHAR2(24 BYTE), 
	"QSI_NAME_MID" VARCHAR2(16 BYTE), 
	"QSI_NAME_LAST" VARCHAR2(24 BYTE), 
	"LINK_DATETIME" TIMESTAMP (6), 
	"LINK_EMPLOYEE" NUMBER(6,0), 
	"IS_NEWEST_PATIENT" CHAR(1 BYTE), 
	"ZIPCODE" CHAR(10 BYTE), 
	"DOB" DATE, 
	"GENDER" CHAR(1 BYTE), 
	"DATE_LAST_VISIT" DATE
   ) PCTFREE 10 PCTUSED 40 INITRANS 1 MAXTRANS 255 NOCOMPRESS LOGGING
  STORAGE(INITIAL 65536 NEXT 1048576 MINEXTENTS 1 MAXEXTENTS **********
  PCTINCREASE 0 FREELISTS 1 FREELIST GROUPS 1 BUFFER_POOL DEFAULT)
  TABLESPACE "BNDNEW_DATA" ;
