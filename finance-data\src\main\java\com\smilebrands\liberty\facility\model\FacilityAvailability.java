package com.smilebrands.liberty.facility.model;

import java.io.Serializable;

import javax.persistence.Basic;
import javax.persistence.Column;
import javax.persistence.Entity;
import javax.persistence.GeneratedValue;
import javax.persistence.GenerationType;
import javax.persistence.Id;
import javax.persistence.NamedQueries;
import javax.persistence.NamedQuery;
import javax.persistence.SequenceGenerator;
import javax.persistence.Table;

/**
 *
 * <AUTHOR>
 */
@Entity
@Table(name = "FACILITY_AVAIL")
@NamedQueries({
    @NamedQuery(name = "FacilityAvailability.findAll", query = "SELECT f FROM FacilityAvailability f"),
    @NamedQuery(name = "FacilityAvailability.removeById", query = "DELETE FROM FacilityAvailability f WHERE f.configId = ?1")
})
public class FacilityAvailability implements Serializable {

    private static final long serialVersionUID = 1L;

    @Id
    @Basic(optional = false)
    @GeneratedValue(generator = "AvailabilitySeq")
    @SequenceGenerator(name = "AvailabilitySeq", sequenceName = "AVAIL_ID_SEQ", allocationSize = 1)
    @Column(name = "AVAIL_ID")
    private Long availabilityId;

    @Column(name = "CONFIG_ID")
    private Long configId;

    @Column(name = "FACILITY_ID")
    @Basic(optional = false)
    private Integer facilityId;

    @Column(name = "DAY")
    @Basic(optional = false)
    private Integer day;

    @Column(name = "MONTH")
    @Basic(optional = false)
    private Integer month;

    @Column(name = "YEAR")
    @Basic(optional = false)
    private Integer year;

    @Column(name = "START_HOUR")
    @Basic(optional = false)
    private Integer startHour;
    
    @Column(name = "END_HOUR")
    @Basic(optional = false)
    private Integer endHour;

    //@OneToMany(cascade = CascadeType.ALL, mappedBy = "facilityAvailability")
    //private Collection<FacilityAvailabilityService> facilityAvailabilityServiceCollection;

    /**
     * @return the availId
     */
    public Long getAvailabilityId() {
        return availabilityId;
    }

    /**
     * @param availId the availId to set
     */
    public void setAvailabilityId(Long availabilityId) {
        this.availabilityId = availabilityId;
    }

    /**
     * @return the configId
     */
    public Long getConfigId() {
        return configId;
    }

    /**
     * @param configId the configId to set
     */
    public void setConfigId(Long configId) {
        this.configId = configId;
    }
    
    /**
     * @return the facilityId
     */
    public Integer getFacilityId() {
        return facilityId;
    }

    /**
     * @param facilityId the facilityId to set
     */
    public void setFacilityId(Integer facilityId) {
        this.facilityId = facilityId;
    }

    /**
     * @return the day
     */
    public Integer getDay() {
        return day;
    }

    /**
     * @param day the day to set
     */
    public void setDay(Integer day) {
        this.day = day;
    }

    /**
     * @return the month
     */
    public Integer getMonth() {
        return month;
    }

    /**
     * @param month the month to set
     */
    public void setMonth(Integer month) {
        this.month = month;
    }

    /**
     * @return the year
     */
    public Integer getYear() {
        return year;
    }

    /**
     * @param year the year to set
     */
    public void setYear(Integer year) {
        this.year = year;
    }

    /**
     * @return the startHour
     */
    public Integer getStartHour() {
        return startHour;
    }

    /**
     * @param startHour the startHour to set
     */
    public void setStartHour(Integer startHour) {
        this.startHour = startHour;
    }

    /**
     * @return the endHour
     */
    public Integer getEndHour() {
        return endHour;
    }

    /**
     * @param endHour the endHour to set
     */
    public void setEndHour(Integer endHour) {
        this.endHour = endHour;
    }

    /*public Collection<FacilityAvailabilityService> getFacilityAvailabilityServiceCollection() {
        return facilityAvailabilityServiceCollection;
    }

    public void setFacilityAvailabilityServiceCollection(Collection<FacilityAvailabilityService> facilityAvailabilityServiceCollection) {
        this.setFacilityAvailabilityServiceCollection(facilityAvailabilityServiceCollection);
    }*/

    @Override
    public int hashCode() {
        int hash = 0;
        hash += (getAvailabilityId() != null ? getAvailabilityId().hashCode() : 0);
        return hash;
    }

    @Override
    public boolean equals(Object object) {
        // TODO: Warning - this method won't work in the case the id fields are not set
        if (!(object instanceof FacilityAvailability)) {
            return false;
        }
        FacilityAvailability other = (FacilityAvailability) object;
        if ((this.getAvailabilityId() == null && other.getAvailabilityId() != null) ||
                (this.getAvailabilityId() != null && !this.getAvailabilityId().equals(other.getAvailabilityId()))) {
            return false;
        }
        return true;
    }

    @Override
    public String toString() {
        return "com.smilebrands.cal.model.jpa.FacilityAvailability[availId=" + getAvailabilityId() + "]";
    }

}
