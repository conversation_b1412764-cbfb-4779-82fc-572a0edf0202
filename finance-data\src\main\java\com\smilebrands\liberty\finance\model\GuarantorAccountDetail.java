package com.smilebrands.liberty.finance.model;

import java.math.BigDecimal;
import java.util.Date;

import com.smilebrands.liberty.model.BaseObject;

/**
 * <AUTHOR> <PERSON><PERSON>
 *         Smile Brands Inc.
 *         Date: 04/02/14
 */
public class GuarantorAccountDetail extends BaseObject {

    private String accountNumber;
    private String loanNumber;
    private Long patientIdGeneral;
    private Long patientIdOrtho;
    private String activeChartNumber;
    private Date dateOfNextStmt;
    private Date lastpmtdate;
    private BigDecimal regularPaymentAmount;
    private BigDecimal totalPastDueAmount;
    private BigDecimal amtOfPayCurrDue;
    private BigDecimal currentBalance;
    private Integer accountType;
    private Integer paymentType;
    private Integer dentalOfficeSiteID;
    private Integer vantageScore;
    private Integer generalSBFScore;    // Credit Model 2019, Table 10 Score
    private Integer orthoSBFScore;      // Credit Model 2019, Table 10 Score
    private Integer generalSBFRescore;  // Credit Model 2019, Rescore general value
    private Integer orthoSBFRescore;    // Credit Model 2019, Rescore ortho value
    private Integer downPaymentTable;
    private String patientFirstName;
    private String patientLastName;
    private Integer fundedStatus;
    private String contractReference;
    private UnfundedContract unfundedContract;
    private Integer totalAttachments;
    private String token;

    public String getAccountNumber() {
        return accountNumber;
    }

    public void setAccountNumber(String accountNumber) {
        this.accountNumber = accountNumber;
    }

    public String getLoanNumber() {
        return loanNumber;
    }

    public Long getPatientIdGeneral() {
        return patientIdGeneral;
    }

    public void setPatientIdGeneral(Long patientIdGeneral) {
        this.patientIdGeneral = patientIdGeneral;
    }

    public Long getPatientIdOrtho() {
        return patientIdOrtho;
    }

    public void setPatientIdOrtho(Long patientIdOrtho) {
        this.patientIdOrtho = patientIdOrtho;
    }

    public void setLoanNumber(String loanNumber) {
        this.loanNumber = loanNumber;
    }

    public String getActiveChartNumber() {
        return activeChartNumber;
    }

    public void setActiveChartNumber(String activeChartNumber) {
        this.activeChartNumber = activeChartNumber;
    }

    public Date getDateOfNextStmt() {
        return dateOfNextStmt;
    }

    public void setDateOfNextStmt(Date dateOfNextStmt) {
        this.dateOfNextStmt = dateOfNextStmt;
    }

    public Date getLastpmtdate() {
        return lastpmtdate;
    }

    public void setLastpmtdate(Date lastpmtdate) {
        this.lastpmtdate = lastpmtdate;
    }

    public BigDecimal getRegularPaymentAmount() {
        return regularPaymentAmount;
    }

    public void setRegularPaymentAmount(BigDecimal regularPaymentAmount) {
        this.regularPaymentAmount = regularPaymentAmount;
    }

    public BigDecimal getTotalPastDueAmount() {
        return totalPastDueAmount;
    }

    public void setTotalPastDueAmount(BigDecimal totalPastDueAmount) {
        this.totalPastDueAmount = totalPastDueAmount;
    }

    public BigDecimal getAmtOfPayCurrDue() {
        return amtOfPayCurrDue;
    }

    public void setAmtOfPayCurrDue(BigDecimal amtOfPayCurrDue) {
        this.amtOfPayCurrDue = amtOfPayCurrDue;
    }

    public BigDecimal getCurrentBalance() {
        return currentBalance;
    }

    public void setCurrentBalance(BigDecimal currentBalance) {
        this.currentBalance = currentBalance;
    }

    public Integer getAccountType() {
        return accountType;
    }

    public void setAccountType(Integer accountType) {
        this.accountType = accountType;
    }

    public Integer getPaymentType() {
        return paymentType;
    }

    public void setPaymentType(Integer paymentType) {
        this.paymentType = paymentType;
    }

    public Integer getDentalOfficeSiteID() {
        return dentalOfficeSiteID;
    }

    public void setDentalOfficeSiteID(Integer dentalOfficeSiteID) {
        this.dentalOfficeSiteID = dentalOfficeSiteID;
    }

    public Integer getVantageScore() {
        return vantageScore;
    }

    public void setVantageScore(Integer vantageScore) {
        this.vantageScore = vantageScore;
    }

    public Integer getGeneralSBFScore() {
        return generalSBFScore;
    }

    public void setGeneralSBFScore(Integer generalSBFScore) {
        this.generalSBFScore = generalSBFScore;
    }

    public Integer getOrthoSBFScore() {
        return orthoSBFScore;
    }

    public void setOrthoSBFScore(Integer orthoSBFScore) {
        this.orthoSBFScore = orthoSBFScore;
    }

    public Integer getGeneralSBFRescore() {
        return generalSBFRescore;
    }

    public void setGeneralSBFRescore(Integer generalSBFRescore) {
        this.generalSBFRescore = generalSBFRescore;
    }

    public Integer getOrthoSBFRescore() {
        return orthoSBFRescore;
    }

    public void setOrthoSBFRescore(Integer orthoSBFRescore) {
        this.orthoSBFRescore = orthoSBFRescore;
    }

    public Integer getDownPaymentTable() {
        return downPaymentTable;
    }

    public void setDownPaymentTable(Integer downPaymentTable) {
        this.downPaymentTable = downPaymentTable;
    }

    public String getPatientFirstName() {
        return patientFirstName;
    }

    public void setPatientFirstName(String patientFirstName) {
        this.patientFirstName = patientFirstName;
    }

    public String getPatientLastName() {
        return patientLastName;
    }

    public void setPatientLastName(String patientLastName) {
        this.patientLastName = patientLastName;
    }

    public Integer getFundedStatus() {
        return fundedStatus;
    }

    public void setFundedStatus(Integer fundedStatus) {
        this.fundedStatus = fundedStatus;
    }

    public String getContractReference() {
        return contractReference;
    }

    public void setContractReference(String contractReference) {
        this.contractReference = contractReference;
    }

    public UnfundedContract getUnfundedContract() {
        return unfundedContract;
    }

    public void setUnfundedContract(UnfundedContract unfundedContract) {
        this.unfundedContract = unfundedContract;
    }

    public int getTotalAttachments() {
        return totalAttachments;
    }

    public void setTotalAttachments(Integer totalAttachments) {
        this.totalAttachments = totalAttachments;
    }

    public String getToken() {
        return token;
    }

    public void setToken(String token) {
        this.token = token;
    }

}