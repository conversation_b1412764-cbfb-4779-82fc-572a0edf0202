package com.smilebrands.liberty.constants;

/**
 * Java5 style enum class to represent the various states a schedule events.
 * 
 * <AUTHOR> Smile Brands Inc, 2011
 */
public enum ScheduleEventStatusType {

    RESERVED("reserved"){
        @Override
        public String getDescription() { return "Event is pending scheduling"; }
    },
   	SCHEDULED("scheduled"){
   		@Override
   		public String getDescription(){
   			return "The event has been scheduled.";
   		}
   	},
   	LEFT_MESSAGE("left message"){
   		@Override
   		public String getDescription(){
   			return "Voice mail left for patient.";
   		}
   	},
    CONFIRMED("confirmed"){
        @Override
        public String getDescription(){
            return "The patient has confirmed the event, insurance has been verified.";
        }
    },
   	ARRIVED("arrived"){
   		@Override
   		public String getDescription(){
   			return "The patient has arrived in the office.";
   		}
   	},
   	UNSCHEDULED("unscheduled service"){
   		@Override
   		public String getDescription(){
   			return "The patient received unscheduled treatment.";
   		}
   	},
    COMPLETED("completed"){
        @Override
        public String getDescription(){
            return "The patient treatment is completed.";
        }
    },
    DISMISSED("dismissed"){
        @Override
        public String getDescription(){
            return "The patient visit is completed.";
        }
    },
   	MISSED("missed"){
   		@Override
   		public String getDescription(){
   			return "The patient missed the appointment.";
   		}
   	},
   	CANCELLED("cancelled"){
   		@Override
   		public String getDescription(){
   			return "The appointment has been cancelled.";
   		}
   	},
    RESCHEDULED("rescheduled"){
        @Override
        public String getDescription(){
            return "The patient has rescheduled the appointment.";
        }
    };
   	private final String status;
   	private ScheduleEventStatusType(String in){
   		this.status = in;
   	}
   	
   	public String getDescription(){
   		return "";
   	}
   	
   	@Override
   	public String toString(){
   		return this.status;
   	}
   	
   	

	
}
