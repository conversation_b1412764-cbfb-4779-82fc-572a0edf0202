--------------------------------------------------------
--  DDL for Table CHECKOUT_INVOICE
--------------------------------------------------------

  CREATE TABLE "LIBERTY_DATA"."CHECKOUT_INVOICE"
   (	"CHECKOUT_INVOICE_ID" NUMBER(10,0), 
	"DATE_OF_SERVICE" TIMESTAMP (6), 
	"FACILITY_ID" NUMBER(10,0), 
	"PATIENT_ID" NUMBER(10,0), 
	"ACTIVE" CHAR(1 BYTE), 
	"CREATE_EMPLOYEE" NUMBER(6,0), 
	"CREATE_DATETIME" TIMESTAMP (6), 
	"UPDATE_EMPLOYEE" NUMBER(6,0), 
	"UPDATE_DATETIME" TIMESTAMP (6)
   ) PCTFREE 10 PCTUSED 40 INITRANS 1 MAXTRANS 255 NOCOMPRESS LOGGING
  STORAGE(INITIAL 65536 NEXT 1048576 MINEXTENTS 1 MAXEXTENTS **********
  PCTINCREASE 0 FREELISTS 1 FREELIST GROUPS 1 BUFFER_POOL DEFAULT)
  TABLESPACE "BNDNEW_DATA" ;
