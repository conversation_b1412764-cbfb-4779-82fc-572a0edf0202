--------------------------------------------------------
--  DDL for Table FEE_SCHEDULE_DETAIL
--------------------------------------------------------

  CREATE TABLE "LIBERTY_DATA"."FEE_SCHEDULE_DETAIL"
   (	"FEE_SCH_DETAIL_ID" NUMBER(10,0), 
	"FEE_SCHEDULE_ID" NUMBER(10,0), 
	"CDT_CODE" VARCHAR2(5 BYTE), 
	"FEE_SCHEDULE" NUMBER(10,0), 
	"IS_PERCENT_UCR" CHAR(1 BYTE), 
	"DEFAULT_AMT" NUMBER(9,2), 
	"GENERAL_AMT" NUMBER(9,2), 
	"OS_AMT" NUMBER(9,2), 
	"ORTHO_AMT" NUMBER(9,2), 
	"PEDO_AMT" NUMBER(9,2), 
	"ENDO_AMT" NUMBER(9,2), 
	"PERIO_AMT" NUMBER(9,2), 
	"DETAIL_STATUS" CHAR(1 BYTE), 
	"CREATE_EMPLOYEE" NUMBER(10,0), 
	"CREATE_DATETIME" TIMESTAMP (6), 
	"UPDATE_EMPLOYEE" NUMBER(10,0), 
	"UPDATE_DATETIME" TIMESTAMP (6)
   ) PCTFREE 10 PCTUSED 40 INITRANS 1 MAXTRANS 255 NOCOMPRESS LOGGING
  STORAGE(INITIAL 65536 NEXT 1048576 MINEXTENTS 1 MAXEXTENTS 2147483645
  PCTINCREASE 0 FREELISTS 1 FREELIST GROUPS 1 BUFFER_POOL DEFAULT)
  TABLESPACE "BNDNEW_DATA" ;
