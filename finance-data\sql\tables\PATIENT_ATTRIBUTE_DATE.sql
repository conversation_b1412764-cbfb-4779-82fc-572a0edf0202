--------------------------------------------------------
--  DDL for Table PATIENT_ATTRIBUTE_DATE
--------------------------------------------------------

  CREATE TABLE "LIBERTY_DATA"."PATIENT_ATTRIBUTE_DATE"
   (	"PATIENT_ATTRIBUTE_DATE_ID" NUMBER(15,0), 
	"PATIENT_ATTRIBUTE_ID" NUMBER(5,0), 
	"PATIENT_ID" NUMBER(15,0), 
	"PATIENT_DATE" DATE, 
	"IS_ACTIVE" CHAR(1 BYTE), 
	"CREATE_EMPLOYEE_NUMBER" NUMBER(6,0), 
	"CREATE_DATETIME" TIMESTAMP (6), 
	"UPDATE_EMPLOYEE_NUMBER" NUMBER(6,0), 
	"UPDATE_DATETIME" TIMESTAMP (6)
   ) PCTFREE 10 PCTUSED 40 INITRANS 1 MAXTRANS 255 NOCOMPRESS LOGGING
  STORAGE(INITIAL 65536 NEXT 1048576 MINEXTENTS 1 MAXEXTENTS **********
  PCTINCREASE 0 FREELISTS 1 FREELIST GROUPS 1 BUFFER_POOL DEFAULT)
  TABLESPACE "BNDNEW_DATA" ;
