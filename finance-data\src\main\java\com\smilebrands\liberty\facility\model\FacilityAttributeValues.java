package com.smilebrands.liberty.facility.model;

import com.smilebrands.liberty.employee.model.EmployeeBase;
import com.smilebrands.liberty.model.BaseObject;
import com.smilebrands.liberty.serializer.DateIsoDeSerializer;
import com.smilebrands.liberty.serializer.DateIsoSerializer;
import org.codehaus.jackson.map.annotate.JsonDeserialize;
import org.codehaus.jackson.map.annotate.JsonSerialize;

import javax.persistence.*;
import java.util.Date;

/**
 * <AUTHOR> Alban <PERSON>au
 *         Smile Brands Inc.
 *         Date: 09/03/13
 */
@Entity
@Table(name = "FACILITY_ATTR_VALUES")
@NamedQueries({
        @NamedQuery(name = "FacilityAttributeValues.findByFacilityIdAndFacilityAttributeId",
                    query = "select v from FacilityAttributeValues v " +
                            "where v.facilityId = ?1 " +
                            "and v.facilityAttributeId = ?2 " +
                            "and v.inactivationDatetime IS NULL")
})
public class FacilityAttributeValues extends BaseObject {

    @Id
    @Column(name = "FAC_ATTR_VALUE_ID")
    @GeneratedValue(generator = "FacilityAttributeValueIdSeq")
    @SequenceGenerator(name = "FacilityAttributeValueIdSeq", sequenceName = "FAC_ATTR_VAL_SEQ", allocationSize = 1)
    private Integer facilityAttributeValueId;

    @Column(name = "FACILITY_ID")
    private Integer facilityId;

    @Column(name = "FAC_ATTRIBUTE_ID")
    private Integer facilityAttributeId;

    @Column(name = "FAC_ATTR_VALUE")
    private String facilityAttributeValue;

    @Column(name = "CREATE_DATETIME")
    private Date createDatetime;

    @Column(name = "CREATE_EMPLOYEE")
    private Integer creationEmployee;

    @Column(name = "INACTIVE_DATETIME")
    private Date inactivationDatetime;

    @Column(name = "INACTIVE_EMPLOYEE")
    private Integer inactiveEmployee;

    @OneToOne
    @JoinColumn(name = "CREATE_EMPLOYEE", referencedColumnName = "EMPLOYEE_NUMBER", insertable = false, updatable = false)
    private EmployeeBase employeeCreate;

    @OneToOne(fetch = FetchType.EAGER)
    @JoinColumn(name = "FACILITY_ID", referencedColumnName = "FACILITY_ID", insertable = false, updatable = false)
    private Facility facility;

    public Integer getFacilityAttributeValueId() {
        return facilityAttributeValueId;
    }

    public void setFacilityAttributeValueId(Integer facilityAttributeValueId) {
        this.facilityAttributeValueId = facilityAttributeValueId;
    }

    public Integer getFacilityId() {
        return facilityId;
    }

    public void setFacilityId(Integer facilityId) {
        this.facilityId = facilityId;
    }

    public Integer getFacilityAttributeId() {
        return facilityAttributeId;
    }

    public void setFacilityAttributeId(Integer facilityAttributeId) {
        this.facilityAttributeId = facilityAttributeId;
    }

    public String getFacilityAttributeValue() {
        return facilityAttributeValue;
    }

    public void setFacilityAttributeValue(String facilityAttributeValue) {
        this.facilityAttributeValue = facilityAttributeValue;
    }

    @JsonSerialize(using= DateIsoSerializer.class)
    public Date getCreateDatetime() {
        return createDatetime;
    }

    @JsonDeserialize(using= DateIsoDeSerializer.class)
    public void setCreateDatetime(Date createDatetime) {
        this.createDatetime = createDatetime;
    }

    public Integer getCreationEmployee() {
        return creationEmployee;
    }

    public void setCreationEmployee(Integer creationEmployee) {
        this.creationEmployee = creationEmployee;
    }

    @JsonSerialize(using= DateIsoSerializer.class)
    public Date getInactivationDatetime() {
        return inactivationDatetime;
    }

    @JsonDeserialize(using= DateIsoDeSerializer.class)
    public void setInactivationDatetime(Date inactivationDatetime) {
        this.inactivationDatetime = inactivationDatetime;
    }

    public Integer getInactiveEmployee() {
        return inactiveEmployee;
    }

    public void setInactiveEmployee(Integer inactiveEmployee) {
        this.inactiveEmployee = inactiveEmployee;
    }

    public EmployeeBase getEmployeeCreate() {
        return employeeCreate;
    }

    public void setEmployeeCreate(EmployeeBase employeeCreate) {
        this.employeeCreate = employeeCreate;
    }

    public Facility getFacility() {
        return facility;
    }

    public void setFacility(Facility facility) {
        this.facility = facility;
    }
}
