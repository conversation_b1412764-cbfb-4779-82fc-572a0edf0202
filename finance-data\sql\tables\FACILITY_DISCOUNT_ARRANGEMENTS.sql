--------------------------------------------------------
--  DDL for Table FACILITY_DISCOUNT_ARRANGEMENTS
--------------------------------------------------------

  CREATE TABLE "LIBERTY_DATA"."FACILITY_DISCOUNT_ARRANGEMENTS"
   (	"FACILITY_ARRANGEMENT_ID" NUMBER(11,0), 
	"FACILITY_ID" NUMBER(10,0), 
	"ARRANGEMENT_ID" NUMBER(10,0), 
	"EFFECTIVE_DATE" DATE, 
	"INACTIVE_DATE" DATE, 
	"CREATE_EMPLOYEE" NUMBER(10,0), 
	"CREATE_DATETIME" TIMESTAMP (6), 
	"UPDATE_EMPLOYEE" NUMBER(10,0), 
	"UPDATE_DATETIME" TIMESTAMP (6)
   ) PCTFREE 10 PCTUSED 40 INITRANS 1 MAXTRANS 255 NOCOMPRESS LOGGING
  STORAGE(INITIAL 65536 NEXT 1048576 MINEXTENTS 1 MAXEXTENTS 2147483645
  PCTINCREASE 0 FREELISTS 1 FREELIST GROUPS 1 BUFFER_POOL DEFAULT)
  TABLESPACE "BNDNEW_DATA" ;
