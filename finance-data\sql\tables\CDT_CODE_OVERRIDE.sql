--------------------------------------------------------
--  DDL for Table CDT_CODE_OVERRIDE
--------------------------------------------------------

  CREATE TABLE "LIBERTY_DATA"."CDT_CODE_OVERRIDE"
   (	"INS_COMPANY_ID" NUMBER(10,0), 
	"CDT_CODE" VARCHAR2(5 BYTE), 
	"OVERRIDE_CDT_CODE" VARCHAR2(5 BYTE), 
	"CREATE_EMPLOYEE" NUMBER(10,0), 
	"CREATE_DATETIME" TIMESTAMP (6), 
	"UPDATE_EMPLOYEE" NUMBER(10,0), 
	"UPDATE_TIMESTAMP" TIMESTAMP (6), 
	"IS_ACTIVE" CHAR(1 BYTE)
   ) PCTFREE 10 PCTUSED 40 INITRANS 1 MAXTRANS 255 NOCOMPRESS LOGGING
  STORAGE(INITIAL 65536 NEXT 1048576 MINEXTENTS 1 MAXEXTENTS 2147483645
  PCTINCREASE 0 FREELISTS 1 FREELIST GROUPS 1 BUFFER_POOL DEFAULT)
  TABLESPACE "BNDNEW_DATA" ;
