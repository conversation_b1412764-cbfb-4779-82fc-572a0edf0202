package com.smilebrands.liberty.claim.model;

import javax.persistence.DiscriminatorValue;
import javax.persistence.Entity;

/*
* <PERSON><PERSON> - Smile Brands Inc. 10/23/12
*
*/
@SuppressWarnings("serial")
@DiscriminatorValue("PRIMARY")
@Entity
public class ClaimPrimaryInsurance extends ClaimInsurance {
  
  public transient String subscriberMemberType = "SUBSCRIBER";

  /**
   * @return the subscriberMemberType
   */
  public String getSubscriberMemberType() {
    return subscriberMemberType;
  }

  /**
   * @param subscriberMemberType the subscriberMemberType to set
   */
  public void setSubscriberMemberType(String subscriberMemberType) {
    this.subscriberMemberType = subscriberMemberType;
  }

}