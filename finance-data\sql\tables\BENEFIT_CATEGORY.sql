--------------------------------------------------------
--  DDL for Table BENEFIT_CATEGORY
--------------------------------------------------------

  CREATE TABLE "LIBERTY_DATA"."BENEFIT_CATEGORY"
   (	"BENEFIT_CATEGORY_ID" NUMBER(5,0), 
	"CATEGORY_DESCRIPTION" VARCHAR2(40 BYTE), 
	"BENEFIT_CATEGORY_DESCRIPTION" VARCHAR2(20 BYTE), 
	"CDT_CATEGORY" NUMBER(10,0), 
	"DEFAULT_COVERAGE_PERCENT" NUMBER(5,4), 
	"CREATE_EMPLOYEE" NUMBER(10,0), 
	"CREATE_TIMESTAMP" TIMESTAMP (6), 
	"UPDATE_EMPLOYEE" NUMBER(10,0), 
	"UPDATE_TIMESTAMP" TIMESTAMP (6)
   ) PCTFREE 10 PCTUSED 40 INITRANS 1 MAXTRANS 255 NOCOMPRESS LOGGING
  STORAGE(INITIAL 65536 NEXT 1048576 MINEXTENTS 1 MAXEXTENTS 2147483645
  PCTINCREASE 0 FREELISTS 1 FREELIST GROUPS 1 BUFFER_POOL DEFAULT)
  TABLESPACE "BNDNEW_DATA" ;
