package com.smilebrands.liberty.employee.dao.impl;

import com.mysema.query.jpa.JPQLQuery;
import com.mysema.query.jpa.impl.JPAQuery;
import com.smilebrands.liberty.dao.SimpleAbstractDao;
import com.smilebrands.liberty.employee.dao.EmployeeDao;
import com.smilebrands.liberty.employee.model.Employee;
import com.smilebrands.liberty.employee.model.EmployeeBase;
import com.smilebrands.liberty.employee.model.QEmployee;
import com.smilebrands.liberty.employee.model.QEmployeeBase;
import com.smilebrands.liberty.security.model.QLibertyRoleEmployee;
import org.springframework.stereotype.Repository;

import java.util.List;

/**
 *
 * <AUTHOR> May 6, 2011
 */
@Repository(value = "employeeDao")
public class EmployeeDaoImpl extends SimpleAbstractDao implements EmployeeDao {

    @Override
    public Employee getEmployee(Integer employeeId) {
        return em.find(Employee.class, employeeId);
    }

    @Override
    public List<Employee> getEmployees(Integer facilityId) {
        JPQLQuery queryDsl = new JPAQuery(em);
        QEmployee employee = QEmployee.employee;
        List<Employee> employees = queryDsl.from(employee)
                .where(employee.facility.facilityId.eq(facilityId),
                       employee.employmentStatus.equalsIgnoreCase("Active"))
                .list(QEmployee.employee);

        return employees;
    }

    @Override
    public List<Employee> getEmployees(String title) {
        JPQLQuery queryDsl = new JPAQuery(em);
        QEmployee employee = QEmployee.employee;
        List<Employee> employees = queryDsl.from(employee)
                .where(employee.title.equalsIgnoreCase(title),
                       employee.employmentStatus.equalsIgnoreCase("Active"))
                .list(QEmployee.employee);

        return employees;
    }

    @Override
    public EmployeeBase getBaseEmployee(Integer employeeId) {
        return em.find(EmployeeBase.class, employeeId);
    }

    @Override
    public List<EmployeeBase> getBaseEmployees(Integer facilityId) {
        JPQLQuery queryDsl = new JPAQuery(em);
        QEmployeeBase employee = QEmployeeBase.employeeBase;
        List<EmployeeBase> employees = queryDsl.from(employee)
                .where(employee.facility.eq(facilityId),
                       employee.employmentStatus.equalsIgnoreCase("Active"))
                .list(QEmployeeBase.employeeBase);

        return employees;
    }

    @Override
    public List<EmployeeBase> getBaseEmployees(String title) {
        JPQLQuery queryDsl = new JPAQuery(em);
        QEmployeeBase employee = QEmployeeBase.employeeBase;
        List<EmployeeBase> employees = queryDsl.from(employee)
                .where(employee.title.equalsIgnoreCase(title),
                       employee.employmentStatus.equalsIgnoreCase("Active"))
                .list(QEmployeeBase.employeeBase);

        return employees;
    }

    @Override
    public List<Employee> getEmployeesByManager(Integer managerEmployeeNumber) {
        JPQLQuery queryDsl = new JPAQuery(em);
        QEmployee employee = QEmployee.employee;
        List<Employee> employees = queryDsl.from(employee)
                .where(employee.employmentStatus.equalsIgnoreCase("Active"),
                        employee.manager.employeeNumber.eq(managerEmployeeNumber))
                .list(QEmployee.employee);

        return employees;
    }

    @Override
    public List<EmployeeBase> getBaseEmployeeUsingPricing() {
        JPQLQuery queryDsl = new JPAQuery(em);
        QEmployeeBase employee = QEmployeeBase.employeeBase;
        QLibertyRoleEmployee lre = QLibertyRoleEmployee.libertyRoleEmployee;

        queryDsl.from(employee)
                .from(lre)
                .where(employee.employeeNumber.eq(lre.employeeNumber.intValue()))
                .where(lre.libertyRoleId.eq(10L));

        return queryDsl.list(employee);
    }
}
