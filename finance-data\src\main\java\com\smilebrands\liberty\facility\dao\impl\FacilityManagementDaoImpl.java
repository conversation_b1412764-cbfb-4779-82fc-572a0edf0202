package com.smilebrands.liberty.facility.dao.impl;

import com.smilebrands.liberty.cache.Cacheable;
import com.smilebrands.liberty.dao.SimpleAbstractDao;
import com.smilebrands.liberty.facility.dao.FacilityManagmentDao;
import com.smilebrands.liberty.facility.model.vo.FacilityManagementVo;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Qualifier;
import org.springframework.jdbc.core.RowMapper;
import org.springframework.jdbc.core.namedparam.MapSqlParameterSource;
import org.springframework.jdbc.core.namedparam.NamedParameterJdbcTemplate;
import org.springframework.stereotype.Repository;

import java.sql.ResultSet;
import java.sql.SQLException;
import java.util.List;

/**
 * Created using IntelliJ IDEA.
 * User: m<PERSON><PERSON>
 * Date: 1/25/12
 */
@Repository
public class FacilityManagementDaoImpl extends SimpleAbstractDao implements FacilityManagmentDao {

    final Logger logger = LoggerFactory.getLogger(FacilityManagementDaoImpl.class);

	@Autowired
	@Qualifier("namedParameterJdbcTemplate")
	protected NamedParameterJdbcTemplate namedParameterJdbcTemplate;

    @Autowired
    @Qualifier("facilityAvpDmobyFacility")
    protected String facilityAvpDmobyFacility;

    @Autowired
    @Qualifier("facilityOfficeManagerbyFacility")
    protected String facilityOfficeManagerbyFacility;

    @Autowired
    @Qualifier("facilityManagementByFacility")
    protected String facilityManagementByFacility;

    @Override
    @Cacheable(cacheName="FacilityManagementCollections")
    public List<FacilityManagementVo> getFacilityManagement(Integer facilityId){
        MapSqlParameterSource parameters = new MapSqlParameterSource();
        parameters.addValue("facilityId", facilityId);

        return namedParameterJdbcTemplate.query(facilityManagementByFacility,
                parameters,
                new RowMapper() {

                    @Override
                    public Object mapRow(ResultSet rs, int i) throws SQLException {
                        FacilityManagementVo vo = new FacilityManagementVo();
                        vo.empId = rs.getInt("EMPLOYEE_NUMBER");
                        vo.firstName = rs.getString("FIRST_NAME");
                        vo.lastName = rs.getString("LAST_NAME");
                        vo.title = rs.getString("TITLE");
                        vo.emailAddress = rs.getString("EMAIL_ADDRESS");

                        return vo;
                    }
                });

    }

}
