package com.smilebrands.liberty.billing.dao.impl;

import com.mysema.query.jpa.JPQLQuery;
import com.mysema.query.jpa.impl.JPAQuery;
import com.smilebrands.liberty.billing.dao.PaymentAdjustmentCategoryCustom;
import com.smilebrands.liberty.billing.model.PaymentAdjustmentCategory;
import com.smilebrands.liberty.billing.model.QPaymentAdjustmentCategory;
import com.smilebrands.liberty.cache.Cacheable;
import com.smilebrands.liberty.dao.SimpleAbstractDao;

import java.util.List;

/**
 * User: <PERSON><PERSON>
 * Date: 5/10/13
 */
public class PaymentAdjustmentCategoryRepositoryImpl extends SimpleAbstractDao implements PaymentAdjustmentCategoryCustom {

    @Override
    @Cacheable(cacheName="CodeCollections")
    public List<PaymentAdjustmentCategory> getPaymentAdjustmentCategories() {
        JPQLQuery queryDsl = new JPAQuery(em);
        QPaymentAdjustmentCategory category = QPaymentAdjustmentCategory.paymentAdjustmentCategory1;
        return queryDsl.from(category)
                       .list(category);
    }
}
