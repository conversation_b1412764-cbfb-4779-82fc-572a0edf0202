------------------------------------------------------------------------------------------
-- Author: <PERSON><PERSON>
-- Date Created: 08/13/2013
--
-- Purpose: <PERSON><PERSON><PERSON> to create table DAILY_RECONCILIATION_DETAIL.
--    Table where the daily reconciliation calculation is stored.
--
------------------------------------------------------------------------------------------

--------------------------------------------------------
-- Table creation
--------------------------------------------------------
CREATE TABLE DAILY_RECONCILIATION_DETAIL
  (
    DAILY_RECONCILIATION_DETAIL_ID NUMBER(10,0) NOT NULL ENABLE,
    DAILY_RECONCILIATION_ID        NUMBER(10,0) NOT NULL ENABLE,
    FUNDING_SOURCE_TYPE            VARCHAR2(20 BYTE) NOT NULL ENABLE,
    AUDIT_AMOUNT                   NUMBER(9,2),
    SYSTEM_AMOUNT                  NUMBER(9,2),
    EXPLANATION                    VARCHAR2(2000 BYTE),
    CREATE_EMPLOYEE_NUMBER         NUMBER(6,0) NOT NULL ENABLE,
    CREATE_DATETIME                TIMESTAMP(6) NOT NULL ENABLE,
    UPDATE_EMPLOYEE_NUMBER         NUMBER(6,0),
    UPDATE_DATETIME                TIMESTAMP(6),
    CONSTRAINT PK_D_R_DETAIL_ID PRIMARY KEY (DAILY_RECONCILIATION_DETAIL_ID),
    CONSTRAINT FK_DAILY_RECONCILIATION_ID FOREIGN KEY (DAILY_RECONCILIATION_ID) REFERENCES DAILY_RECONCILIATION (DAILY_RECONCILIATION_ID)
  );

--------------------------------------------------------
-- Add comments on DAILY_RECONCILIATION_DETAIL.
--------------------------------------------------------
COMMENT ON TABLE DAILY_RECONCILIATION_DETAIL IS 'Table where the daily reconciliation calcution are stored.';
COMMENT ON COLUMN DAILY_RECONCILIATION_DETAIL.DAILY_RECONCILIATION_DETAIL_ID IS 'Identification number of the record, generated by DAILY_REC_DETAIL_ID_SEQ.';
COMMENT ON COLUMN DAILY_RECONCILIATION_DETAIL.DAILY_RECONCILIATION_ID IS 'Reference to the daily reconciliation record';
COMMENT ON COLUMN DAILY_RECONCILIATION_DETAIL.FUNDING_SOURCE_TYPE IS 'Enumeration of funding source type.';
COMMENT ON COLUMN DAILY_RECONCILIATION_DETAIL.AUDIT_AMOUNT IS 'Amount entered by the user.';
COMMENT ON COLUMN DAILY_RECONCILIATION_DETAIL.SYSTEM_AMOUNT IS 'Amount calculated with payment adjustment batch.';
COMMENT ON COLUMN DAILY_RECONCILIATION_DETAIL.EXPLANATION IS 'Explanation of the record.';
COMMENT ON COLUMN DAILY_RECONCILIATION_DETAIL.CREATE_EMPLOYEE_NUMBER IS 'Employee number who created the record.';
COMMENT ON COLUMN DAILY_RECONCILIATION_DETAIL.CREATE_DATETIME IS 'When the record has been created.';
COMMENT ON COLUMN DAILY_RECONCILIATION_DETAIL.UPDATE_EMPLOYEE_NUMBER IS 'Employee number who updated the record.';
COMMENT ON COLUMN DAILY_RECONCILIATION_DETAIL.UPDATE_DATETIME IS 'When the record has been updated.';

--------------------------------------------------------
-- Drop scripts:
--------------------------------------------------------
--drop table DAILY_RECONCILIATION_DETAIL;

--------------------------------------------------------
--  DDL for Table DAILY_RECONCILIATION
--------------------------------------------------------
--  CREATE TABLE "LIBERTY"."DAILY_RECONCILIATION_DETAIL"
--   (	"DAILY_RECONCILIATION_DETAIL_ID" NUMBER(10,0) NOT NULL ENABLE,
--	"DAILY_RECONCILIATION_ID" NUMBER(10,0) NOT NULL ENABLE,
--	"FUNDING_SOURCE_TYPE" VARCHAR2(20 BYTE) NOT NULL ENABLE,
--	"AUDIT_AMOUNT" NUMBER(9,2),
--	"SYSTEM_AMOUNT" NUMBER(9,2),
--	"EXPLANATION" VARCHAR2(2000 BYTE),
--	"CREATE_EMPLOYEE_NUMBER" NUMBER(6,0) NOT NULL ENABLE,
--	"CREATE_DATETIME" TIMESTAMP (6) NOT NULL ENABLE,
--	"UPDATE_EMPLOYEE_NUMBER" NUMBER(6,0),
--	"UPDATE_DATETIME" TIMESTAMP (6),
--	 CONSTRAINT "PK_D_R_DETAIL_ID" PRIMARY KEY ("DAILY_RECONCILIATION_DETAIL_ID")
--  USING INDEX PCTFREE 10 INITRANS 2 MAXTRANS 255
--  STORAGE(INITIAL 65536 NEXT 1048576 MINEXTENTS 1 MAXEXTENTS 2147483645
--  PCTINCREASE 0 FREELISTS 1 FREELIST GROUPS 1 BUFFER_POOL DEFAULT)
--  TABLESPACE "BNDNEW_DATA"  ENABLE,
--	 CONSTRAINT "FK_DAILY_RECONCILIATION_ID" FOREIGN KEY ("DAILY_RECONCILIATION_ID")
--	  REFERENCES "LIBERTY"."DAILY_RECONCILIATION" ("DAILY_RECONCILIATION_ID") ENABLE
--   ) PCTFREE 10 PCTUSED 40 INITRANS 1 MAXTRANS 255 NOCOMPRESS LOGGING
--  STORAGE(INITIAL 65536 NEXT 1048576 MINEXTENTS 1 MAXEXTENTS 2147483645
--  PCTINCREASE 0 FREELISTS 1 FREELIST GROUPS 1 BUFFER_POOL DEFAULT)
--  TABLESPACE "BNDNEW_DATA" ;