package com.smilebrands.liberty.billing.dao;

import com.smilebrands.liberty.billing.model.PendingItem;

import java.util.Date;
import java.util.List;

/**
 * Created by phongpham on 1/13/14.
 */
public interface PendingItemRepository {

    List<PendingItem> getPendingItem(Integer facilityId, Long patientId);
    List<PendingItem> getPendingItemByFacilityDates(String type, Integer facilityId, Date startDate, Date endDate);
}
