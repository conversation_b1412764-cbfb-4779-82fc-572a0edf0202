package com.smilebrands.liberty.facility.model;

import com.smilebrands.liberty.model.BaseObject;

import java.math.BigDecimal;

/**
 * <AUTHOR>
 * @version 01/06/2014
 */
public class FacilityFinanceProviderRevenue extends BaseObject {

    private Integer providerId;
    private String firstName;
    private String lastName;
    private String taxonomyCode;
    private String providerType;
    private Integer daysWorked;
    private BigDecimal dailyProduction;
    private BigDecimal dailyAdjustments;
    private BigDecimal dailyTotal;
    private BigDecimal averageDailyTotal;
    private BigDecimal monthToDateTotal;

    public Integer getProviderId() {
        return providerId;
    }

    public void setProviderId(Integer providerId) {
        this.providerId = providerId;
    }

    public String getFirstName() {
        return firstName;
    }

    public void setFirstName(String firstName) {
        this.firstName = firstName;
    }

    public String getLastName() {
        return lastName;
    }

    public void setLastName(String lastName) {
        this.lastName = lastName;
    }

    public String getTaxonomyCode() {
        return taxonomyCode;
    }

    public void setTaxonomyCode(String taxonomyCode) {
        this.taxonomyCode = taxonomyCode;
    }

    public String getProviderType() {
        return providerType;
    }

    public void setProviderType(String providerType) {
        this.providerType = providerType;
    }

    public BigDecimal getDailyTotal() {
        return dailyTotal;
    }

    public void setDailyTotal(BigDecimal dailyTotal) {
        this.dailyTotal = dailyTotal;
    }

    public BigDecimal getMonthToDateTotal() {
        return monthToDateTotal;
    }

    public void setMonthToDateTotal(BigDecimal monthToDateTotal) {
        this.monthToDateTotal = monthToDateTotal;
    }

    public BigDecimal getDailyProduction () { return dailyProduction; }
    public void setDailyProduction (BigDecimal dailyProduction) { this.dailyProduction = dailyProduction; }

    public BigDecimal getDailyAdjustments () { return dailyAdjustments; }
    public void setDailyAdjustments (BigDecimal dailyAdjustments) { this.dailyAdjustments = dailyAdjustments; }

    public BigDecimal getAverageDailyTotal () { return averageDailyTotal; }
    public void setAverageDailyTotal (BigDecimal averageDailyTotal) { this.averageDailyTotal = averageDailyTotal; }

    public Integer getDaysWorked () { return daysWorked; }
    public void setDaysWorked (Integer daysWorked) { this.daysWorked = daysWorked; }

}