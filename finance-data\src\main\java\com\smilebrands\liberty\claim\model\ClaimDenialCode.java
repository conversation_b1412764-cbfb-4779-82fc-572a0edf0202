package com.smilebrands.liberty.claim.model;

import com.smilebrands.liberty.charting.model.DateJsonDeserializer;
import com.smilebrands.liberty.charting.model.DateJsonSerializer;
import com.smilebrands.liberty.model.BaseObject;
import org.codehaus.jackson.map.annotate.JsonDeserialize;
import org.codehaus.jackson.map.annotate.JsonSerialize;

import javax.persistence.*;
import java.util.Date;

/**
 * Created with IntelliJ IDEA.
 * User: kevintran
 * Date: 8/1/13
 * Time: 12:58 PM
 * To change this template use File | Settings | File Templates.
 */
@Entity
@Table(name = "CLAIM_DENIAL_CODE")
public class ClaimDenialCode extends BaseObject {

    @Id
    @GeneratedValue(generator = "ClaimDenialCodeSeq")
    @SequenceGenerator(name = "ClaimDenialCodeSeq", sequenceName = "CLAIM_DENIAL_CODE_SEQ", allocationSize = 1)
    @Column(name = "CLAIM_DENIAL_CODE")
    private Long claimDenialCode;

    @Column(name = "PARENT_GROUP")
    private String parentGroup;

    @Column(name = "DENIAL_CODE")
    private String denialCode;

    @Column(name = "HIPPA_GROUP_CODE")
    private String hippaGroupCode;

    @Column(name = "HIPPA_REASON_CODE")
    private String hippaReasonCode;

    @Column(name = "CREATE_EMPLOYEE")
    private Integer createEmployeeNumber;

    @Temporal(TemporalType.TIMESTAMP)
    @Column(name = "CREATE_DATETIME")
    private Date createDateTime;

    // This is generated via trigger and needs to be detached/flushed
    @Column(name = "GROUP_SEQUENCE")
    private Long groupSequence;

    @Column(name = "PREFERRED_ORDER")
    private Long preferredOrder;

    @Column(name = "TOOLTIP")
    private String tooltip;

    @Column(name = "UPDATE_EMPLOYEE")
    private Integer updateEmployeeNumber;

    @Temporal(TemporalType.TIMESTAMP)
    @Column(name = "UPDATE_DATETIME")
    private Date updateDateTime;

    public Long getClaimDenialCode() {
        return claimDenialCode;
    }

    public void setClaimDenialCode(Long claimDenialCode) {
        this.claimDenialCode = claimDenialCode;
    }

    public String getParentGroup() {
        return parentGroup;
    }

    public void setParentGroup(String parentGroup) {
        this.parentGroup = parentGroup;
    }

    public String getDenialCode() {
        return denialCode;
    }

    public void setDenialCode(String denialCode) {
        this.denialCode = denialCode;
    }

    public String getHippaGroupCode() {
        return hippaGroupCode;
    }

    public void setHippaGroupCode(String hippaGroupCode) {
        this.hippaGroupCode = hippaGroupCode;
    }

    public String getHippaReasonCode() {
        return hippaReasonCode;
    }

    public void setHippaReasonCode(String hippaReasonCode) {
        this.hippaReasonCode = hippaReasonCode;
    }

    public Integer getCreateEmployeeNumber() {
        return createEmployeeNumber;
    }

    public void setCreateEmployeeNumber(Integer createEmployeeNumber) {
        this.createEmployeeNumber = createEmployeeNumber;
    }

    @JsonSerialize(using = DateJsonSerializer.class)
    public Date getCreateDateTime() {
        return createDateTime;
    }

    @JsonDeserialize(using = DateJsonDeserializer.class)
    public void setCreateDateTime(Date createDateTime) {
        this.createDateTime = createDateTime;
    }

    public Long getGroupSequence() {
        return groupSequence;
    }

    public void setGroupSequence(Long groupSequence) {
        this.groupSequence = groupSequence;
    }

    public Long getPreferredOrder() {
        return preferredOrder;
    }

    public void setPreferredOrder(Long preferredOrder) {
        this.preferredOrder = preferredOrder;
    }

    public String getTooltip() {
        return tooltip;
    }

    public void setTooltip(String tooltip) {
        this.tooltip = tooltip;
    }

    public Integer getUpdateEmployeeNumber() {
        return updateEmployeeNumber;
    }

    public void setUpdateEmployeeNumber(Integer updateEmployeeNumber) {
        this.updateEmployeeNumber = updateEmployeeNumber;
    }

    @JsonSerialize(using = DateJsonSerializer.class)
    public Date getUpdateDateTime() {
        return updateDateTime;
    }

    @JsonDeserialize(using = DateJsonDeserializer.class)
    public void setUpdateDateTime(Date updateDateTime) {
        this.updateDateTime = updateDateTime;
    }

    @Override
    public boolean equals(Object o) {
        if (this == o) return true;
        if (o == null || getClass() != o.getClass()) return false;

        ClaimDenialCode that = (ClaimDenialCode) o;

        if (claimDenialCode != null ? !claimDenialCode.equals(that.claimDenialCode) : that.claimDenialCode != null)
            return false;

        return true;
    }

    @Override
    public int hashCode() {
        return claimDenialCode != null ? claimDenialCode.hashCode() : 0;
    }

}
