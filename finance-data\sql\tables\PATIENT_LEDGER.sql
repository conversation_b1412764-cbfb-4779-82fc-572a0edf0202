--------------------------------------------------------
--  DDL for Table PATIENT_LEDGER
--------------------------------------------------------

  CREATE TABLE "LIBERTY_DATA"."PATIENT_LEDGER"
   (	"PATIENT_LEDGER_ID" NUMBER(11,0), 
	"FACILITY_ID" NUMBER(10,0), 
	"PATIENT_ID" NUMBER(10,0), 
	"PRODUCT_SERVICE_CODE" NUMBER(7,0), 
	"DATE_POSTED" DATE, 
	"DATE_OF_SERVICE" DATE, 
	"PATIENT_AMOUNT" NUMBER(9,2), 
	"INSURANCE_PRIMARY" NUMBER(9,2), 
	"INSURANCE_SECONDARY" NUMBER(9,2), 
	"ORIG_PATIENT_AMOUNT" NUMBER(9,2), 
	"ORIG_INS_PRIMARY" NUMBER(9,2), 
	"ORIG_INS_SECONDARY" NUMBER(9,2), 
	"UCR_AMOUNT" NUMBER(9,2), 
	"STATISTIC_AMOUNT" NUMBER(9,2), 
	"TOOTH_NUMBER" VARCHAR2(2 BYTE), 
	"SURFACE" VARCHAR2(10 BYTE), 
	"QUADRANT" VARCHAR2(2 BYTE), 
	"ARRANGEMENT_ID" NUMBER(10,0), 
	"ARRANGEMENT_CORELATION_ID" NUMBER(11,0), 
	"SERVICE_PROVIDER_ID" NUMBER(10,0), 
	"PRI_INSURANCE_PLAN" NUMBER(10,0), 
	"TREATMENT_ID" NUMBER(15,5), 
	"REFERRING_PROVIDER_ID" NUMBER(10,0), 
	"REFERRING_FACILITY_ID" NUMBER(10,0), 
	"CLINIC_ID" NUMBER(10,0), 
	"PLAN_ID" NUMBER(10,0), 
	"TR_ID" NUMBER(10,0), 
	"IS_CONVERSION" CHAR(1 BYTE), 
	"GL_AUDIT_CODE" NUMBER(11,0), 
	"CREATE_EMPLOYEE" NUMBER(10,0), 
	"CREATE_DATETIME" TIMESTAMP (6), 
	"UPDATE_EMPLOYEE" NUMBER(10,0), 
	"UPDATE_DATETIME" TIMESTAMP (6), 
	"PRI_INVOICE_ID" NUMBER(15,0), 
	"PRI_INVOICE_DATETIME" TIMESTAMP (6), 
	"SEC_INVOICE_ID" NUMBER(15,0), 
	"SEC_INVOICE_DATETIME" TIMESTAMP (6), 
	"SEC_INSURANCE_PLAN" NUMBER(10,0), 
	"CHECKOUT_INVOICE_ID" NUMBER(15,0)
   ) PCTFREE 10 PCTUSED 40 INITRANS 1 MAXTRANS 255 NOCOMPRESS LOGGING
  STORAGE(INITIAL 65536 NEXT 1048576 MINEXTENTS 1 MAXEXTENTS **********
  PCTINCREASE 0 FREELISTS 1 FREELIST GROUPS 1 BUFFER_POOL DEFAULT)
  TABLESPACE "BNDNEW_DATA" ;
 

   COMMENT ON COLUMN "LIBERTY_DATA"."PATIENT_LEDGER"."TREATMENT_ID" IS 'Treatment record that created this patient ledger';
