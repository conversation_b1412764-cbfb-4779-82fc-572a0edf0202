--------------------------------------------------------
--  DDL for Table INSURANCE_INVOICE
--------------------------------------------------------

  CREATE TABLE "LIBERTY_DATA"."INSURANCE_INVOICE"
   (	"INS_INVOICE_ID" NUMBER(15,0), 
	"FACILITY_ID" NUMBER(5,0), 
	"SERVICE_PROVIDER_ID" NUMBER(5,0), 
	"SERVICE_DATE_START" DATE, 
	"SERVICE_DATE_END" DATE, 
	"PATIENT_ID" NUMBER(15,0), 
	"INSURANCE_PLAN" NUMBER(10,0), 
	"INS_UCR_TOTAL" NUMBER(10,2), 
	"INS_EXP_TOTAL" NUMBER(10,2), 
	"PATIENT_TOTAL" NUMBER(10,2), 
	"CREATE_EMPLOYEE_NUMBER" NUMBER(6,0), 
	"CREATE_DATETIME" TIMESTAMP (6), 
	"CLAIM_CREATE_DATETIME" TIMESTAMP (6), 
	"CLAIM_ID" NUMBER(15,0), 
	"INVOICE_TYPE" VARCHAR2(25 BYTE)
   ) PCTFREE 10 PCTUSED 40 INITRANS 1 MAXTRANS 255 NOCOMPRESS LOGGING
  STORAGE(INITIAL 65536 NEXT 1048576 MINEXTENTS 1 MAXEXTENTS **********
  PCTINCREASE 0 FREELISTS 1 FREELIST GROUPS 1 BUFFER_POOL DEFAULT)
  TABLESPACE "BNDNEW_DATA" ;
 

   COMMENT ON COLUMN "LIBERTY_DATA"."INSURANCE_INVOICE"."SERVICE_DATE_START" IS 'earliest date of service associated with the patient_ledgers tied to this record';
 
   COMMENT ON COLUMN "LIBERTY_DATA"."INSURANCE_INVOICE"."SERVICE_DATE_END" IS 'The date of the last service rendered associated with the patient_ledger record';
 
   COMMENT ON COLUMN "LIBERTY_DATA"."INSURANCE_INVOICE"."INS_UCR_TOTAL" IS 'sum of UCR amount from the patient ledgers associated with this record';
 
   COMMENT ON COLUMN "LIBERTY_DATA"."INSURANCE_INVOICE"."INS_EXP_TOTAL" IS 'sum of the correct (primary/secondary/tertiary) insurance amounts for the associated patient_ledger records';
 
   COMMENT ON COLUMN "LIBERTY_DATA"."INSURANCE_INVOICE"."INVOICE_TYPE" IS 'enum type can only be PRIMARY|SECONDARY|TERTIARY';
