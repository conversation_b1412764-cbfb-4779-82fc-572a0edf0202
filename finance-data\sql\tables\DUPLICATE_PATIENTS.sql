--------------------------------------------------------
--  DDL for Table DUPLICATE_PATIENTS
--------------------------------------------------------

  CREATE TABLE "LIBERTY_DATA"."DUPLICATE_PATIENTS"
   (	"CLINIC_ID" NUMBER, 
	"UNIQUE_ID" NUMBER, 
	"NAME_LAST" CHAR(24 BYTE), 
	"ZIPCODE" CHAR(10 BYTE), 
	"DATE_OF_BIRTH" DATE, 
	"DATE_LAST_VISIT" DATE, 
	"GENDER" CHAR(1 BYTE), 
	"NAME_FIRST" CHAR(24 BYTE), 
	"NAME_MIDDLE" CHAR(16 BYTE), 
	"PATIENT_ID" NUMBER, 
	"ACCOUNT_ID" NUMBER, 
	"LIBERTY_PATIENT" NUMBER(10,0), 
	"IS_FAMILY_ACCOUNT" NUMBER(1,0), 
	"IS_MVP_PATIENT" NUMBER(1,0)
   ) PCTFREE 10 PCTUSED 40 INITRANS 1 MAXTRANS 255 NOCOMPRESS LOGGING
  STORAGE(INITIAL 65536 NEXT 1048576 MINEXTENTS 1 MAXEXTENTS **********
  PCTINCREASE 0 FREELISTS 1 FREELIST GROUPS 1 BUFFER_POOL DEFAULT)
  TABLESPACE "BNDNEW_DATA" ;
