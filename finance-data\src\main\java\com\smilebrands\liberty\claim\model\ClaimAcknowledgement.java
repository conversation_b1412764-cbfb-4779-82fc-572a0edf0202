package com.smilebrands.liberty.claim.model;

import com.smilebrands.liberty.model.BaseObject;
import com.smilebrands.liberty.utility.JsonDateDeSerializer;
import com.smilebrands.liberty.utility.JsonDateSerializer;
import org.codehaus.jackson.map.annotate.JsonDeserialize;
import org.codehaus.jackson.map.annotate.JsonSerialize;

import javax.persistence.*;
import java.util.ArrayList;
import java.util.Date;
import java.util.List;

/*
* <PERSON><PERSON> - Smile Brands Inc. 8/9/12
*/
@Entity
@Table(name = "CLAIM_ACK")
@SuppressWarnings("serial")
public class ClaimAcknowledgement extends BaseObject {

    @Id
    @Column(name = "CLAIM_ACK_ID")
    @GeneratedValue(generator = "ClaimAckSeq")
    @SequenceGenerator(name = "ClaimAckSeq", sequenceName = "CLAIM_ACK_SEQ", allocationSize = 1)
    protected Long claimAcknowledgementId;

    @Column(name = "CLAIM_ID")
    protected Long claimId;

    @Column(name = "ACK_CODE")
    protected String acknowledgementCode;

    @Temporal(TemporalType.DATE)
    @Column(name = "ACK_DATETIME")
    private Date acknowledgementDatetime = new Date();

    @OneToMany
    private List<ClaimAcknowledgementError> errors = new ArrayList<ClaimAcknowledgementError>();


    public Long getClaimAcknowledgementId() {
        return claimAcknowledgementId;
    }

    public void setClaimAcknowledgementId(Long claimAcknowledgementId) {
        this.claimAcknowledgementId = claimAcknowledgementId;
    }

    public Long getClaimId() {
        return claimId;
    }

    public void setClaimId(Long claimId) {
        this.claimId = claimId;
    }

    public String getAcknowledgementCode() {
        return acknowledgementCode;
    }

    public void setAcknowledgementCode(String acknowledgementCode) {
        this.acknowledgementCode = acknowledgementCode;
    }

    @JsonSerialize(using = JsonDateSerializer.class)
    public Date getAcknowledgementDatetime() {
        return acknowledgementDatetime;
    }

    @JsonDeserialize(using = JsonDateDeSerializer.class)
    public void setAcknowledgementDatetime(Date acknowledgementDatetime) {
        this.acknowledgementDatetime = acknowledgementDatetime;
    }

    public List<ClaimAcknowledgementError> getErrors() {
        return errors;
    }

    public void setErrors(List<ClaimAcknowledgementError> errors) {
        this.errors = errors;
    }

    @Override
    public boolean equals(Object o) {
        if (this == o) return true;
        if (o == null || getClass() != o.getClass()) return false;

        ClaimAcknowledgement that = (ClaimAcknowledgement) o;

        if (!claimAcknowledgementId.equals(that.claimAcknowledgementId)) return false;

        return true;
    }

    @Override
    public int hashCode() {
        return claimAcknowledgementId.hashCode();
    }
}