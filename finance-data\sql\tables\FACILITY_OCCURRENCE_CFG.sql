--------------------------------------------------------
--  DDL for Table FACILITY_OCCURRENCE_CFG
--------------------------------------------------------

  CREATE TABLE "LIBERTY_DATA"."FACILITY_OCCURRENCE_CFG"
   (	"CONFIG_ID" NUMBER(15,5), 
	"FACILITY_ID" NUMBER(15,5), 
	"OCCURRENCE_TYPE" NUMBER(1,0), 
	"OCCURRENCE_FREQUENCY" NUMBER(15,5), 
	"DAY_OF_WEEK" NUMBER(15,5), 
	"DURATION" NUMBER(15,5), 
	"START_HOUR" NUMBER(15,5), 
	"END_HOUR" NUMBER(15,5), 
	"ACTIVATION_DATETIME" TIMESTAMP (6), 
	"INACTIVATION_DATETIME" TIMESTAMP (6), 
	"CREATE_DATETIME" TIMESTAMP (6), 
	"CREATE_EMPLOYEE_NUMBER" NUMBER(6,0), 
	"UPDATE_DATETIME" TIMESTAMP (6), 
	"UPDATE_EMPLOYEE_NUMBER" NUMBER(6,0), 
	"TITLE" VARCHAR2(50 BYTE), 
	"EVENT_TYPE" VARCHAR2(25 BYTE)
   ) PCTFREE 10 PCTUSED 40 INITRANS 1 MAXTRANS 255 NOCOMPRESS LOGGING
  STORAGE(INITIAL 65536 NEXT 1048576 MINEXTENTS 1 MAXEXTENTS 2147483645
  PCTINCREASE 0 FREELISTS 1 FREELIST GROUPS 1 BUFFER_POOL DEFAULT)
  TABLESPACE "BNDNEW_DATA" ;
