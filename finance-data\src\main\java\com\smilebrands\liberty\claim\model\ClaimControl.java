package com.smilebrands.liberty.claim.model;

import com.smilebrands.liberty.model.BaseObject;
import com.smilebrands.liberty.utility.JsonDateDeSerializer;
import com.smilebrands.liberty.utility.JsonDateSerializer;
import org.apache.commons.lang.StringUtils;
import org.codehaus.jackson.map.annotate.JsonDeserialize;
import org.codehaus.jackson.map.annotate.JsonSerialize;

import javax.persistence.*;
import java.util.Date;

/*
* <PERSON><PERSON> - Smile Brands Inc. 8/8/12
*/
@Entity
@Table(name = "CLAIM_CONTROL")
@SuppressWarnings("serial")
public class ClaimControl extends BaseObject {

    @Id
    @Column(name = "CLAIM_CONTROL_ID")
    @GeneratedValue(generator = "ClaimControlSeq")
    @SequenceGenerator(name = "ClaimControlSeq", sequenceName = "CLAIM_CONTROL_SEQ", allocationSize = 1)
    protected Long claimControlId;

    @Column(name = "FACILITY_ID")
    protected Integer facilityId;

    @Column(name = "CLINIC_ID")
    protected Integer qsiClinicId;

    @Column(name = "UNIQUE_ID")
    protected Integer qsiUniquePatientId;

    @Column(name = "PATIENT_ID")
    protected Integer qsiPatientId;

    @Column(name = "LIBERTY_PATIENT_ID")
    protected Long libertyPatientId;

    @Column(name = "QSI_CLAIM_NUMBER")
    protected Integer qsiClaimNumber;

    @Column(name = "QSI_CLAIM_FILE_ID")
    protected Integer qsiClaimFileId;

    @Temporal(TemporalType.DATE)
    @Column(name = "CREATE_DATETIME")
    private Date claimCreateDate = new Date();

    @Temporal(TemporalType.DATE)
    @Column(name = "SERVICE_DATE")
    private Date serviceDate;

    public Long getClaimControlId() {
        return claimControlId;
    }

    public void setClaimControlId(Long claimControlId) {
        this.claimControlId = claimControlId;
    }

    public Integer getFacilityId() {
        return facilityId;
    }

    public void setFacilityId(Integer facilityId) {
        this.facilityId = facilityId;
    }

    public Integer getQsiClinicId() {
        return qsiClinicId;
    }

    public void setQsiClinicId(Integer qsiClinicId) {
        this.qsiClinicId = qsiClinicId;
    }

    public Integer getQsiUniquePatientId() {
        return qsiUniquePatientId;
    }

    public void setQsiUniquePatientId(Integer qsiUniquePatientId) {
        this.qsiUniquePatientId = qsiUniquePatientId;
    }

    public Integer getQsiPatientId() {
        return qsiPatientId;
    }

    public void setQsiPatientId(Integer qsiPatientId) {
        this.qsiPatientId = qsiPatientId;
    }

    public Long getLibertyPatientId() {
        return libertyPatientId;
    }

    public void setLibertyPatientId(Long libertyPatientId) {
        this.libertyPatientId = libertyPatientId;
    }

    public Integer getQsiClaimNumber() {
        return qsiClaimNumber;
    }

    public void setQsiClaimNumber(Integer qsiClaimNumber) {
        this.qsiClaimNumber = qsiClaimNumber;
    }

    public Integer getQsiClaimFileId() {
        return qsiClaimFileId;
    }

    public void setQsiClaimFileId(Integer qsiClaimFileId) {
        this.qsiClaimFileId = qsiClaimFileId;
    }

    @JsonSerialize(using=JsonDateSerializer.class)
    public Date getClaimCreateDate() {
        return claimCreateDate;
    }

    @JsonDeserialize(using = JsonDateDeSerializer.class)
    public void setClaimCreateDate(Date claimCreateDate) {
        this.claimCreateDate = claimCreateDate;
    }

    @JsonSerialize(using = JsonDateSerializer.class)
    public Date getServiceDate() {
        return serviceDate;
    }

    @JsonDeserialize(using = JsonDateDeSerializer.class)
    public void setServiceDate(Date serviceDate) {
        this.serviceDate = serviceDate;
    }

    @Override
    public boolean equals(Object o) {
        if (this == o) return true;
        if (o == null || getClass() != o.getClass()) return false;

        ClaimControl that = (ClaimControl) o;

        if (claimControlId != null ? !claimControlId.equals(that.claimControlId) : that.claimControlId != null)
            return false;

        return true;
    }

    @Override
    public int hashCode() {
        return claimControlId != null ? claimControlId.hashCode() : 0;
    }

    public String getPatientId(){
        if(this.getLibertyPatientId() != null){
            return this.getLibertyPatientId().toString();
        }else if(this.getQsiUniquePatientId() != null && this.getQsiClinicId() != null){
            return "9" + StringUtils.leftPad(this.getQsiClinicId().toString(), 4, "0") + this.getQsiUniquePatientId();
        }else if(this.getQsiClinicId() != null){
            return "9" + StringUtils.leftPad(this.getQsiClinicId().toString(), 4, "0") + this.getQsiPatientId();
        }else{
            return "";
        }
    }
}