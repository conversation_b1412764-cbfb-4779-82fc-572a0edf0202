--------------------------------------------------------
--  DDL for Table TREATMENT
--------------------------------------------------------

  CREATE TABLE "LIBERTY_DATA"."TREATMENT"
   (	"TREATMENT_ID" NUMBER(15,0), 
	"PRODUCT_SERVICE_CODE" NUMBER(15,0), 
	"TOOTH_ID" NUMBER(15,0), 
	"PATIENT_ID" NUMBER(15,0), 
	"TREATMENT_PLAN_ID" NUMBER(15,5), 
	"ALTERNATE_TO_TREATMENT_ID" NUMBER(15,0), 
	"ACCEPTED" CHAR(1 BYTE), 
	"ACCEPTED_DATETIME" TIMESTAMP (6), 
	"ACCEPTED_SERVICE_PROVIDER_ID" NUMBER(5,0), 
	"COMPLETE" CHAR(1 BYTE), 
	"COMPLETED_DATETIME" TIMESTAMP (6), 
	"COMPLETED_SERVICE_PROVIDER_ID" NUMBER(5,0), 
	"FACILITY_ID" NUMBER(15,5), 
	"CREATE_EMPLOYEE_NUMBER" NUMBER(6,0), 
	"CREATE_DATETIME" TIMESTAMP (6), 
	"UPDATE_EMPLOYEE_NUMBER" NUMBER(6,0), 
	"UPDATE_DATETIME" TIMESTAMP (6), 
	"TEETH" VARCHAR2(75 BYTE) DEFAULT NULL, 
	"SURFACE" VARCHAR2(10 BYTE) DEFAULT NULL, 
	"QUAD" VARCHAR2(15 BYTE) DEFAULT NULL, 
	"INACTIVATION_DATETIME" TIMESTAMP (6), 
	"INACTIVATION_EMPLOYEE_NUMBER" NUMBER(6,0), 
	"GROUP_ID" VARCHAR2(50 BYTE), 
	"REFERRAL_ID" NUMBER(10,0) DEFAULT NULL, 
	"SERVICE_PROVIDER_ID" NUMBER(5,0), 
	"UCR_AMOUNT" NUMBER(9,2), 
	"PATIENT_AMOUNT" NUMBER(9,2), 
	"INSURANCE_PRIMARY" NUMBER(9,2), 
	"INSURANCE_SECONDARY" NUMBER(9,2)
   ) PCTFREE 10 PCTUSED 40 INITRANS 1 MAXTRANS 255 NOCOMPRESS LOGGING
  STORAGE(INITIAL 65536 NEXT 1048576 MINEXTENTS 1 MAXEXTENTS **********
  PCTINCREASE 0 FREELISTS 1 FREELIST GROUPS 1 BUFFER_POOL DEFAULT)
  TABLESPACE "BNDNEW_DATA" ;
