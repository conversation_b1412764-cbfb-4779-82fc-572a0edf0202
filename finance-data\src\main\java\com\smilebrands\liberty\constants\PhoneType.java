package com.smilebrands.liberty.constants;

/**
 * Java 5 enum type.
 * <AUTHOR> Smile Brands Inc., 2011
 */
public enum PhoneType {
    APPOINTMENT("Appointment"){
        @Override
        public String getDescription() {
            return "Appointment phone";
        }
    },
    HOME("Home") {
        @Override
        public String getDescription() {
            return "Home phone type";
        }
    },
    MOBILE("Mobile") {
        @Override
        public String getDescription() {
            return "Mobile phone type";
        }
    },
    WORK("Work") {
        @Override
        public String getDescription() {
            return "Work phone type";
        }
    },
    OFFICE("Office") {
        @Override
        public String getDescription() {
            return "Office phone type";
        }
    };
    private final String strValue;

    private PhoneType(final String strValue) {
        this.strValue = strValue;
    }

    public String getDescription() {
        return "";
    }

    @Override
    public String toString() {
        return this.strValue;
    }
}
