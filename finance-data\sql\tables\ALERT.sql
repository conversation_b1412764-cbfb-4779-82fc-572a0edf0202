--------------------------------------------------------
--  DDL for Table ALERT
--------------------------------------------------------

  CREATE TABLE "LIBERTY_DATA"."ALERT"
   (	"ALERT_ID" NUMBER(15,0), 
	"ALERT_TYPE" VARCHAR2(35 BYTE), 
	"ALERT_GROUP" VARCHAR2(35 BYTE), 
	"CREATE_DATETIME" TIMESTAMP (6), 
	"ACKNOWLEDGED" CHAR(1 BYTE) DEFAULT '0', 
	"ACKNOWLEDGED_EMPLOYEE_NUMBER" NUMBER(15,5), 
	"ACKNOWLEDGED_DATETIME" TIMESTAMP (6), 
	"PATIENT_ID" NUMBER(15,0), 
	"FACILITY_ID" NUMBER(6,0), 
	"EXPIRATION_DATETIME" TIMESTAMP (6), 
	"ACTION_OPTION" VARCHAR2(150 BYTE), 
	"ALERT_MESSAGE" VARCHAR2(250 BYTE), 
	"ALERT_SEVERITY" VARCHAR2(35 BYTE), 
	"ALERT_RESOLUTION" VARCHAR2(250 BYTE)
   ) PCTFREE 10 PCTUSED 40 INITRANS 1 MAXTRANS 255 NOCOMPRESS LOGGING
  STORAGE(INITIAL 65536 NEXT 1048576 MINEXTENTS 1 MAXEXTENTS **********
  PCTINCREASE 0 FREELISTS 1 FREELIST GROUPS 1 BUFFER_POOL DEFAULT)
  TABLESPACE "BNDNEW_DATA" ;
