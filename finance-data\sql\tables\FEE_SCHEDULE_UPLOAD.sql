--------------------------------------------------------
--  DDL for Table FEE_SCHEDULE_UPLOAD
--------------------------------------------------------

  CREATE TABLE "LIBERTY_DATA"."FEE_SCHEDULE_UPLOAD"
   (	"FEE_SCHED_UPLOAD_ID" NUMBER(10,0), 
	"FEE_SCHEDULE_TYPE" VARCHAR2(25 BYTE), 
	"FILE_NAME" VARCHAR2(50 BYTE), 
	"FILE_DESCRIPTION" VARCHAR2(50 BYTE), 
	"FILE_LOCATION" VARCHAR2(150 BYTE), 
	"CREATE_EMPLOYEE_NUMBER" NUMBER(6,0), 
	"CREATE_DATETIME" TIMESTAMP (6), 
	"LAST_LINKED_EMPLOYEE_NUMBER" NUMBER(6,0), 
	"LAST_LINKED_DATETIME" TIMESTAMP (6), 
	"LAST_LINKED_FEE_SCHEDULE" NUMBER(10,0), 
	"HIDDEN_EMPLOYEE_NUMBER" NUMBER(6,0), 
	"HIDDEN_DATETIME" TIMESTAMP (6), 
	"LINE_OF_BUSINESS" VARCHAR2(25 BYTE)
   ) PCTFREE 10 PCTUSED 40 INITRANS 1 MAXTRANS 255 NOCOMPRESS LOGGING
  STORAGE(INITIAL 65536 NEXT 1048576 MINEXTENTS 1 MAXEXTENTS 2147483645
  PCTINCREASE 0 FREELISTS 1 FREELIST GROUPS 1 BUFFER_POOL DEFAULT)
  TABLESPACE "BNDNEW_DATA" ;
