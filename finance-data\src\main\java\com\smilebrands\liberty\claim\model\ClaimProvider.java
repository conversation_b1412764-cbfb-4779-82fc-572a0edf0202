package com.smilebrands.liberty.claim.model;

import com.smilebrands.liberty.model.BaseObject;

import javax.persistence.*;

/**
 * <AUTHOR> Smile Brands Inc., 2011
 */
@Entity
@Table(name = "CLAIM_PROVIDER")
@Inheritance(strategy = InheritanceType.SINGLE_TABLE)
@DiscriminatorColumn(name="PROVIDER_TYPE", discriminatorType=DiscriminatorType.STRING)
@SuppressWarnings("serial")
public abstract class ClaimProvider extends BaseObject {

    
    @Column(name = "CLAIM_PROVIDER_ID", updatable=false, nullable=false)
    @GeneratedValue(generator = "CLAIM_PROVIDER_SEQ")
    @SequenceGenerator(name="CLAIM_PROVIDER_SEQ", sequenceName = "CLAIM_PROVIDER_SEQ", allocationSize = 1)
    @Id protected Long id;

    @Column(name = "PRACTICE_NAME")
    private String practiceName;

    @Column(name = "PROVIDER_FIRST_NAME")
    private String providerFirstName;

    @Column(name = "PROVIDER_LAST_NAME")
    private String providerLastName;

    @Column(name = "FACILITY_ID")
    private Integer facilityId;

    @Column(name = "TAX_ID_NUMBER")
    private String taxIdNumber;

    @Column(name = "ADDRESS_STREET")
    private String addressStreet;

    @Column(name = "ADDRESS_CITY")
    private String addressCity;

    @Column(name = "ADDRESS_STATE")
    private String addressState;

    @Column(name = "ZIP_CODE")
    private String addressZipCode;

    @Column(name = "PHONE_NUMBER")
    private Long phoneNumber;

    @Column(name = "PROVIDER_NPI")
    private Long providerNPI;

    @Column(name = "FACILITY_NPI")
    private Long facilityNPI;

    @Column(name = "PROVIDER_LICENSE")
    private String providerLicense;

    @Column(name = "ADDITIONAL_ID")
    private String additionalId;

    @Column(name = "SPECIALTY_TYPE")
    private String specialtyType;

    @Column(name = "SERVICE_PROVIDER_ID")
    private Integer serviceProviderId;
    

    public Long getId() {
        return id;
    }

    public void setId(Long claimProviderId) {
        this.id = claimProviderId;
    }

    public String getPracticeName() {
        return practiceName;
    }

    public void setPracticeName(String practiceName) {
        this.practiceName = practiceName;
    }

    public String getProviderFirstName() {
        return providerFirstName;
    }

    public void setProviderFirstName(String providerFirstName) {
        this.providerFirstName = providerFirstName;
    }

    public String getProviderLastName() {
        return providerLastName;
    }

    public void setProviderLastName(String providerLastName) {
        this.providerLastName = providerLastName;
    }

    public Integer getFacilityId() {
        return facilityId;
    }

    public void setFacilityId(Integer facilityId) {
        this.facilityId = facilityId;
    }

    public String getTaxIdNumber() {
        return taxIdNumber;
    }

    public void setTaxIdNumber(String taxIdNumber) {
        this.taxIdNumber = taxIdNumber;
    }

    public String getAddressStreet() {
        return addressStreet;
    }

    public void setAddressStreet(String addressStreet) {
        this.addressStreet = addressStreet;
    }

    public String getAddressCity() {
        return addressCity;
    }

    public void setAddressCity(String addressCity) {
        this.addressCity = addressCity;
    }

    public String getAddressState() {
        return addressState;
    }

    public void setAddressState(String addressState) {
        this.addressState = addressState;
    }

    public String getAddressZipCode() {
        return addressZipCode;
    }

    public void setAddressZipCode(String addressZipCode) {
        this.addressZipCode = addressZipCode;
    }

    public Long getPhoneNumber() {
        return phoneNumber;
    }

    public void setPhoneNumber(Long phoneNumber) {
        this.phoneNumber = phoneNumber;
    }

    public Long getProviderNPI() {
        return providerNPI;
    }

    public void setProviderNPI(Long providerNPI) {
        this.providerNPI = providerNPI;
    }

    public String getProviderLicense() {
        return providerLicense;
    }

    public void setProviderLicense(String providerLicense) {
        this.providerLicense = providerLicense;
    }

    public String getAdditionalId() {
        return additionalId;
    }

    public void setAdditionalId(String additionalId) {
        this.additionalId = additionalId;
    }

    public String getSpecialtyType() {
        return specialtyType;
    }

    public void setSpecialtyType(String specialtyType) {
        this.specialtyType = specialtyType;
    }

    public Integer getServiceProviderId() {
        return serviceProviderId;
    }

    public void setServiceProviderId(Integer serviceProviderId) {
        this.serviceProviderId = serviceProviderId;
    }

    @Override
    public boolean equals(Object o) {
        if (this == o) return true;
        if (o == null || getClass() != o.getClass()) return false;

        ClaimProvider that = (ClaimProvider) o;

        if (id != null ? !id.equals(that.id) : that.id != null)
            return false;

        return true;
    }

    @Override
    public int hashCode() {
        return id != null ? id.hashCode() : 0;
    }

    public String getProviderCSZ(){
        String result = "";
        if(this.getAddressCity() != null && this.getAddressCity().trim().length() > 0){
            result += this.getAddressCity().trim();
        }
        if(this.getAddressState() != null && this.getAddressState().trim().length() > 0){
            result += ", " + this.getAddressState().trim();
        }
        if(this.getAddressZipCode() != null && this.getAddressZipCode().trim().length() > 0){
            result += " " + this.getAddressZipCode().trim();
        }
        return result;
    }

    public String getProviderFullName(){
        String result = "";
        if(this.getProviderFirstName() != null){
            result += this.getProviderFirstName().trim() + " ";
        }
        if(this.getProviderLastName() != null){
            result += this.getProviderLastName().trim();
        }
        return result;
    }

    public String getPhoneNumberBy(int type){
        if(this.getPhoneNumber() != null){
            String phoneValue = this.getPhoneNumber().toString();
            if(type == 0 && phoneValue.length() == 10){
                return phoneValue.substring(0, 3);
            }else if(type == 1){
                if(phoneValue.length() == 10){
                    return phoneValue.substring(3,6);
                }else if(phoneValue.length() == 7){
                    return phoneValue.substring(0,3);
                }else{
                    return  "";
                }
            }else if(type == 2){
                if(phoneValue.length() == 10){
                    return phoneValue.substring(6,10);
                }else if(phoneValue.length() == 7){
                    return phoneValue.substring(3,7);
                }else{
                    return "";
                }
            }else{
                return "";
            }
        }else{
            return "";
        }
    }

    public Long getFacilityNPI() {
        return facilityNPI;
    }

    public void setFacilityNPI(Long facilityNPI) {
        this.facilityNPI = facilityNPI;
    }
}
