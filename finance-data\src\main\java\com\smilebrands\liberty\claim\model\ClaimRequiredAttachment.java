package com.smilebrands.liberty.claim.model;

import com.smilebrands.liberty.constants.VendorType;
import com.smilebrands.liberty.model.BaseObject;
import com.smilebrands.liberty.utility.JsonDateDeSerializer;
import com.smilebrands.liberty.utility.JsonDateSerializer;
import org.codehaus.jackson.map.annotate.JsonDeserialize;
import org.codehaus.jackson.map.annotate.JsonSerialize;
import org.hibernate.annotations.NotFound;
import org.hibernate.annotations.NotFoundAction;

import javax.persistence.*;
import java.util.Date;

/**
 * User: <PERSON><PERSON>
 * Date: 8/15/12
 */
//
@Entity
@NamedQueries({
        @NamedQuery(name = "ClaimRequiredAttachment.getClaimRequiredAttachments",
                    query = "select r from ClaimRequiredAttachment r " +
                            "  left join fetch r.completionEmployee " +
                            "  left join fetch r.requirement " +
                            "  left join fetch r.createEmployee " +
                            "where r.claimId = ?"),
        @NamedQuery(name = "ClaimRequiredAttachment.getClaimRequiredAttachmentsByClaimIds",
                query = "select r from ClaimRequiredAttachment r " +
                        "  left join fetch r.completionEmployee " +
                        "  left join fetch r.requirement " +
                        "  left join fetch r.createEmployee " +
                        "where r.claimId in ?1"),
        @NamedQuery(name = "ClaimRequiredAttachment.getClaimRequiredAttachmentByType",
                    query = "select r from ClaimRequiredAttachment r " +
                            "  left join fetch r.completionEmployee " +
                            "  left join fetch r.requirement " +
                            "  left join fetch r.createEmployee " +
                            "where r.claimId = ? " +
                            "and r.attachmentType = ?")
})
@Table(name = "CLAIM_REQ_ATTACHMENT")
@SuppressWarnings("serial")
public class ClaimRequiredAttachment extends BaseObject {

    @Id
    @Column(name = "REQ_ATTACHMENT_ID")
    @GeneratedValue(generator = "AttachmentSeq")
    @SequenceGenerator(name = "AttachmentSeq", sequenceName = "ATTACHMENT_ID_SEQ", allocationSize = 1)
    private Long requiredAttachmentId;

    @Column(name = "CLAIM_ID")
    protected Long claimId;

    @Column(name = "ATTACHMENT_TYPE")
    @Enumerated(EnumType.STRING)
    protected AttachmentType attachmentType;

    @Temporal(TemporalType.TIMESTAMP)
    @Column(name = "CREATION_DATE")
    private Date creationDatetime;

    @Temporal(TemporalType.TIMESTAMP)
    @Column(name = "NEA_CREATION_DATE")
    private Date neaCreationDateTime;

    @Temporal(TemporalType.TIMESTAMP)
    @Column(name = "NEA_TRANSMIT_DATE")
    private Date neaTransmitDateTime;

    @Column(name = "COMPLETION_EMPLOYEE_NUMBER")
    private Integer completionEmployeeNumber;

    @NotFound(action= NotFoundAction.IGNORE)
    @ManyToOne(fetch=FetchType.LAZY)
    @JoinColumn(name="COMPLETION_EMPLOYEE_NUMBER", referencedColumnName = "EMPLOYEE_NUMBER", insertable=false, updatable=false)
    private ClaimEmployee completionEmployee;

    @Temporal(TemporalType.TIMESTAMP)
    @Column(name = "COMPLETION_DATE")
    private Date completedDatetime;

    @Column(name = "VENDOR_TYPE")
    @Enumerated(EnumType.STRING)
    private VendorType vendorType = VendorType.NEA;

    @Column(name = "REQUIREMENT_ID")
    private Integer requirementId;

    @OneToOne
    @JoinColumn(name = "REQUIREMENT_ID", insertable = false, updatable = false)
    private Requirement requirement;

    public Long getRequiredAttachmentId() {
        return requiredAttachmentId;
    }

    @Column(name = "CREATE_EMPLOYEE_NUMBER")
    private Integer createEmployeeNumber;

    @NotFound(action= NotFoundAction.IGNORE)
    @ManyToOne(fetch=FetchType.LAZY)
    @JoinColumn(name="CREATE_EMPLOYEE_NUMBER", referencedColumnName = "EMPLOYEE_NUMBER", insertable=false, updatable=false)
    private ClaimEmployee createEmployee;

    @Column(name = "IS_REMOVED")
    private Boolean removed;

    @Temporal(TemporalType.TIMESTAMP)
    @Column(name = "REMOVED_DATETIME")
    private Date removedDateTime;

    @Column(name = "REMOVED_EMPLOYEE_NUMBER")
    private Integer removedEmployeeNumber;

    public void setRequiredAttachmentId(Long requiredAttachmentId) {
        this.requiredAttachmentId = requiredAttachmentId;
    }

    public AttachmentType getAttachmentType() {
        return attachmentType;
    }

    public void setAttachmentType(AttachmentType attachmentType) {
        this.attachmentType = attachmentType;
    }

    @JsonSerialize(using=JsonDateSerializer.class)
    public Date getCreationDatetime() {
        return creationDatetime;
    }

    @JsonDeserialize(using = JsonDateDeSerializer.class)
    public void setCreationDatetime(Date creationDatetime) {
        this.creationDatetime = creationDatetime;
    }

    @JsonSerialize(using=JsonDateSerializer.class)
    public Date getNeaCreationDateTime() {
        return neaCreationDateTime;
    }

    @JsonDeserialize(using = JsonDateDeSerializer.class)
    public void setNeaCreationDateTime(Date neaCreationDateTime) {
        this.neaCreationDateTime = neaCreationDateTime;
    }

    @JsonSerialize(using=JsonDateSerializer.class)
    public Date getNeaTransmitDateTime() {
        return neaTransmitDateTime;
    }

    @JsonDeserialize(using = JsonDateDeSerializer.class)
    public void setNeaTransmitDateTime(Date neaTransmitDateTime) {
        this.neaTransmitDateTime = neaTransmitDateTime;
    }

    public Integer getCompletionEmployeeNumber() {
        return completionEmployeeNumber;
    }

    public void setCompletionEmployeeNumber(Integer completionEmployeeNumber) {
        this.completionEmployeeNumber = completionEmployeeNumber;
    }

    @JsonSerialize(using=JsonDateSerializer.class)
    public Date getCompletedDatetime() {
        return completedDatetime;
    }

    @JsonDeserialize(using = JsonDateDeSerializer.class)
    public void setCompletedDatetime(Date completedDatetime) {
        this.completedDatetime = completedDatetime;
    }

    public VendorType getVendorType() {
        return vendorType;
    }

    public void setVendorType(VendorType vendorType) {
        this.vendorType = vendorType;
    }

    @Override
    public boolean equals(Object o) {
        if (this == o) return true;
        if (o == null || getClass() != o.getClass()) return false;

        ClaimRequiredAttachment that = (ClaimRequiredAttachment) o;

        if (requiredAttachmentId != null ? !requiredAttachmentId.equals(that.requiredAttachmentId) : that.requiredAttachmentId != null)
            return false;

        return true;
    }

    @Override
    public int hashCode() {
        return requiredAttachmentId != null ? requiredAttachmentId.hashCode() : 0;
    }

    public Long getClaimId() {
        return claimId;
    }

    public void setClaimId(Long claimId) {
        this.claimId = claimId;
    }

    public ClaimEmployee getCompletionEmployee() {
        return completionEmployee;
    }

    public void setCompletionEmployee(ClaimEmployee completionEmployee) {
        this.completionEmployee = completionEmployee;
    }

    public Integer getRequirementId() {
        return requirementId;
    }

    public void setRequirementId(Integer requirementId) {
        this.requirementId = requirementId;
    }

    public Requirement getRequirement() {
        return requirement;
    }

    public void setRequirement(Requirement requirement) {
        this.requirement = requirement;
    }

    public Integer getCreateEmployeeNumber() {
        return createEmployeeNumber;
    }

    public void setCreateEmployeeNumber(Integer createEmployeeNumber) {
        this.createEmployeeNumber = createEmployeeNumber;
    }

    public ClaimEmployee getCreateEmployee() {
        return createEmployee;
    }

    public void setCreateEmployee(ClaimEmployee createEmployee) {
        this.createEmployee = createEmployee;
    }

    public Boolean getRemoved() {
        return removed;
    }

    public void setRemoved(Boolean removed) {
        this.removed = removed;
    }

    @JsonSerialize(using = JsonDateSerializer.class)
    public Date getRemovedDateTime() {
        return removedDateTime;
    }

    @JsonDeserialize(using = JsonDateDeSerializer.class)
    public void setRemovedDateTime(Date removedDateTime) {
        this.removedDateTime = removedDateTime;
    }

    public Integer getRemovedEmployeeNumber() {
        return removedEmployeeNumber;
    }

    public void setRemovedEmployeeNumber(Integer removedEmployeeNumber) {
        this.removedEmployeeNumber = removedEmployeeNumber;
    }
}
