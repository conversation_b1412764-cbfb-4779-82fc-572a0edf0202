--------------------------------------------------------
--  DDL for Table INSURANCE_ATTRIBUTE
--------------------------------------------------------

  CREATE TABLE "LIBERTY_DATA"."INSURANCE_ATTRIBUTE"
   (	"INS_ATTRIBUTE_ID" NUMBER(15,0), 
	"INS_ATTRIBUTE_TYPE" VARCHAR2(1 BYTE), 
	"INS_ATTRIBUTE" VARCHAR2(35 BYTE), 
	"INS_ATTRIBUTE_DESC" VARCHAR2(250 BYTE), 
	"IS_ACTIVE" CHAR(1 BYTE), 
	"CREATE_EMPLOYEE_NUMBER" NUMBER(6,0), 
	"CREATE_DATETIME" TIMESTAMP (6), 
	"UPDATE_EMPLOYEE_NUMBER" NUMBER(6,0), 
	"UPDATE_DATETIME" TIMESTAMP (6)
   ) PCTFREE 10 PCTUSED 40 INITRANS 1 MAXTRANS 255 NOCOMPRESS LOGGING
  STORAGE(INITIAL 65536 NEXT 1048576 MINEXTENTS 1 MAXEXTENTS 2147483645
  PCTINCREASE 0 FREELISTS 1 FREELIST GROUPS 1 BUFFER_POOL DEFAULT)
  TABLESPACE "BNDNEW_DATA" ;
