package com.smilebrands.liberty.claim.model;

/**
 * User: <PERSON><PERSON>
 * Date: 8/15/12
 */
public enum AttachmentType {

    XRAY("Single bitewing x-ray"){
        public Long getNeaImageCode() {
            return 1L;
        }
        public Boolean isFilm() {
            return true;
        }
    },
    PA("Single periapical x-ray"){
        public Long getNeaImageCode() {
            return 2L;
        }
        public Boolean isFilm() {
            return true;
        }
    },
    PARTIAL("Partial Mouth series"){
        public Long getNeaImageCode() {
            return 3L;
        }
        public Boolean isFilm() {
            return true;
        }
    },
    FMX("Full Mouth series of x-rays"){
        public Long getNeaImageCode() {
            return 4L;
        }
        public Boolean isFilm() {
            return true;
        }
    },
    PANO("Panoramic x-ray"){
        public Long getNeaImageCode() {
            return 5L;
        }
        public Boolean isFilm() {
            return true;
        }
    },
    CEPH("Cephalometric x-ray"){
        public Long getNeaImageCode() {
            return 11L;
        }
        public Boolean isFilm() {
            return true;
        }
    },
    PERIO_CHART("Periodontal Chart"){
        public Long getNeaImageCode() {
            return 6L;
        }
        public Boolean isFilm() {
            return false;
        }
    },
    EOB("EOB"){
        public Long getNeaImageCode() {
            return 7L;
        }
        public Boolean isFilm() {
            return false;
        }
    },
    INTRA_ORAL_PHOTO("Intra Oral Photo"){
        public Long getNeaImageCode() {
            return 8L;
        }
        public Boolean isFilm() {
            return false;
        }
    },
    STUDENT_VERIFICATION("Student Verification"){
        public Long getNeaImageCode() {
            return 9L;
        }
        public Boolean isFilm() {
            return false;
        }
    },
    NARRATIVE("Clinical narrative"){
        public Long getNeaImageCode() {
            return 361L;
        }
        public Boolean isFilm() {
            return false;
        }
    },
    REPORT("Report"){
        public Long getNeaImageCode() {
            return 362L;
        }
        public Boolean isFilm() {
            return false;
        }
    },
    DIAGNOSIS("Diagnosis"){
        public Long getNeaImageCode() {
            return 363L;
        }
        public Boolean isFilm() {
            return false;
        }
    };

    private final String strValue;

    private AttachmentType(final String strValue) {
        this.strValue = strValue;
    }

    public Long getNeaImageCode() {
        return 0L;
    }

    public Boolean isFilm() {
        return true;
    }

    @Override
    public String toString() {
        return this.strValue;
    }
}
