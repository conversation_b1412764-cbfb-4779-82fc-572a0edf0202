--------------------------------------------------------
--  DDL for Table INS_CO_TO_FEE_SCHED
--------------------------------------------------------

  CREATE TABLE "LIBERTY_DATA"."INS_CO_TO_FEE_SCHED"
   (	"INS_COMPANY_ID" NUMBER(10,0), 
	"FEE_SCHEDULE" NUMBER(10,0), 
	"CREATE_EMPLOYEE" NUMBER(10,0), 
	"CREATE_TIMESTAMP" TIMESTAMP (6), 
	"UPDATE_EMPLOYEE" NUMBER(10,0), 
	"UPDATE_TIMESTAMP" TIMESTAMP (6)
   ) PCTFREE 10 PCTUSED 40 INITRANS 1 MAXTRANS 255 NOCOMPRESS LOGGING
  STORAGE(INITIAL 65536 NEXT 1048576 MINEXTENTS 1 MAXEXTENTS 2147483645
  PCTINCREASE 0 FREELISTS 1 FREELIST GROUPS 1 BUFFER_POOL DEFAULT)
  TABLESPACE "BNDNEW_DATA" ;
