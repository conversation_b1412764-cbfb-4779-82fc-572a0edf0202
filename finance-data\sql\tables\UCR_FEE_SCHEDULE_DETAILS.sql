--------------------------------------------------------
--  DDL for Table UCR_FEE_SCHEDULE_DETAILS
--------------------------------------------------------

  CREATE TABLE "LIBERTY_DATA"."UCR_FEE_SCHEDULE_DETAILS"
   (	"UCR_DETAIL_ID" NUMBER(10,0), 
	"UCR_FEE_SCHED_VERSION_ID" NUMBER(10,0), 
	"UCR_FEE_SCHEDULE" NUMBER(10,0), 
	"CDT_CODE" VARCHAR2(5 BYTE), 
	"PRODUCT_SERVICE_CODE" NUMBER(7,0), 
	"GENERAL_AMT" NUMBER(7,2), 
	"OS_AMT" NUMBER(7,2), 
	"ORTHO_AMT" NUMBER(7,2), 
	"PEDO_AMT" NUMBER(7,2), 
	"ENDO_AMT" NUMBER(7,2), 
	"PERIO_AMT" NUMBER(7,2), 
	"EFFECTIVE_DATE" DATE, 
	"INACTIVE_DATE" DATE, 
	"IS_ACTIVE" CHAR(1 BYTE), 
	"CREATE_EMPLOYEE" NUMBER(10,0), 
	"CREATE_TIMESTAMP" TIMESTAMP (6), 
	"UPDATE_EMPLOYEE" NUMBER(10,0), 
	"UPDATE_TIMESTAMP" TIMESTAMP (6)
   ) PCTFREE 10 PCTUSED 40 INITRANS 1 MAXTRANS 255 NOCOMPRESS LOGGING
  STORAGE(INITIAL 65536 NEXT 1048576 MINEXTENTS 1 MAXEXTENTS 2147483645
  PCTINCREASE 0 FREELISTS 1 FREELIST GROUPS 1 BUFFER_POOL DEFAULT)
  TABLESPACE "BNDNEW_DATA" ;
