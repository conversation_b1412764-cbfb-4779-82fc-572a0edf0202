--------------------------------------------------------
--  DDL for Table INSURANCE_VERIFICATION
--------------------------------------------------------

  CREATE TABLE "LIBERTY_DATA"."INSURANCE_VERIFICATION"
   (	"VERIFICATION_ID" NUMBER(10,0), 
	"INSURANCE_SUBSCRIBER_ID" NUMBER(10,0), 
	"VERIFICATION_DATETIME" TIMESTAMP (6), 
	"VERIFICATION_EMPLOYEE" NUMBER(6,0), 
	"VERIFICATION_EXPIRATION" TIMESTAMP (6), 
	"DEDUCTIBLE_AMOUNT" NUMBER(10,2) DEFAULT 0, 
	"REMAINING_DEDUCTIBLE" NUMBER(10,2) DEFAULT 0
   ) PCTFREE 10 PCTUSED 40 INITRANS 1 MAXTRANS 255 NOCOMPRESS LOGGING
  STORAGE(INITIAL 65536 NEXT 1048576 MINEXTENTS 1 MAXEXTENTS 2147483645
  PCTINCREASE 0 FREELISTS 1 FREELIST GROUPS 1 BUFFER_POOL DEFAULT)
  TABLESPACE "BNDNEW_DATA" ;
