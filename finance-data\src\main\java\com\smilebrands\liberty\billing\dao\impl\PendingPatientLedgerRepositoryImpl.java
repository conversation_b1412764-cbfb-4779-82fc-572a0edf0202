package com.smilebrands.liberty.billing.dao.impl;

import com.smilebrands.liberty.billing.dao.PendingPatientLedgerRepository;
import com.smilebrands.liberty.billing.model.PendingPatientLedger;
import com.smilebrands.liberty.dao.SimpleAbstractDao;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Qualifier;
import org.springframework.jdbc.core.JdbcTemplate;
import org.springframework.jdbc.core.RowMapper;
import org.springframework.stereotype.Repository;

import java.sql.ResultSet;
import java.sql.SQLException;
import java.util.List;

/**
 * Created with IntelliJ IDEA.
 * User: phongpham
 * Date: 12/6/13
 * Time: 2:55 PM
 * To change this template use File | Settings | File Templates.
 */
@Repository
public class PendingPatientLedgerRepositoryImpl extends SimpleAbstractDao implements PendingPatientLedgerRepository {

    @Autowired
    @Qualifier("libertyJdbcTemplate")
    private JdbcTemplate jdbcTemplate;

    @Autowired
    @Qualifier(value = "tempUnBilledTreatmentSql")
    private String tempUnBilledTreatmentSql;

    @Override
    public List<PendingPatientLedger> getTransactionValidationByFacility(Integer facilityId) {

        return jdbcTemplate.query(tempUnBilledTreatmentSql, new Object[]{ facilityId, facilityId }, new  PatientTransactionValidationRowMapper());
    }

    class PatientTransactionValidationRowMapper implements RowMapper {

        @Override
        public Object mapRow(ResultSet resultSet, int i) throws SQLException {
            PendingPatientLedger ptv = new PendingPatientLedger();
            ptv.setType(resultSet.getString("TYPE"));
            ptv.setPatientId(resultSet.getLong("PATIENT_ID"));
            ptv.setFirstName(resultSet.getString("FIRST_NAME"));
            ptv.setLastName(resultSet.getString("LAST_NAME"));
            ptv.setDateOfService(resultSet.getDate("DATE_OF_SERVICE"));
            ptv.setInsurancePlan(resultSet.getInt("INSURANCE_PLAN"));
            ptv.setPlanName(resultSet.getString("PLAN_NAME"));
            ptv.setTimelyFilingDays(resultSet.getInt("TIMELY_FILING_DAYS"));
            ptv.setDaysOld(resultSet.getInt("DAYS_OLD"));
            ptv.setPrimaryInsuranceAmount(resultSet.getDouble("PRIMARY_EXPECT"));
            ptv.setSecondaryInsuranceAmount(resultSet.getDouble("SECONDARY_EXPECT"));
            ptv.setPatientAmount(resultSet.getDouble("PATIENT_EXPECT"));
            ptv.setServiceCount(resultSet.getInt("SERVICE_COUNT"));
            return ptv;
        }
    }
}
