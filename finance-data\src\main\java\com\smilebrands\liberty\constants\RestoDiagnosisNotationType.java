package com.smilebrands.liberty.constants;

/*
* <PERSON><PERSON> - Smile Brands Inc. 7/30/12
*
*/
public enum RestoDiagnosisNotationType {

    PENDING("Diagnosis Note Pending") {

        @Override
        public String getDescription() {
            return "Diagnosis note is pending creation of a Clinical note.";
        }
    },
    RECORDED("Diagnosis Note Recorded") {

        @Override
        public String getDescription() {
            return "Diagnosis note has creation a Clinical note.";
        }
    },
    IGNORED("Diagnosis Note Ignored") {

        @Override
        public String getDescription() {
            return "Diagnosis note was ignored by the user.";
        }
    };

    private final String strValue;

    private RestoDiagnosisNotationType(final String strValue) {
        this.strValue = strValue;
    }

    public String getDescription() {
        return "";
    }

    @Override
    public String toString() {
        return this.strValue;
    }
}