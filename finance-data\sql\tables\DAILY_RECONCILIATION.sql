------------------------------------------------------------------------------------------
-- Author: <PERSON><PERSON>
-- Date Created: 08/13/2013
--
-- Purpose: <PERSON><PERSON><PERSON> to create table DAILY_RECONCILIATION.
--    Table where the daily reconciliation is stored.
--
------------------------------------------------------------------------------------------

--------------------------------------------------------
-- Table creation
--------------------------------------------------------
CREATE TABLE DAILY_RECONCILIATION
  (
    DAILY_RECONCILIATION_ID NUMBER(10,0)  NOT NULL ENABLE,
    TRANSACTION_DATE        TIMESTAMP(6)  NOT NULL ENABLE,
    FACILITY_ID             NUMBER(15,5)  NOT NULL ENABLE,
    COMPLETED               CHAR(1 BYTE)  DEFAULT 0 NOT NULL ENABLE,
    CREATE_EMPLOYEE_NUMBER  NUMBER(6,0)   NOT NULL ENABLE,
    CREATE_DATETIME         TIMESTAMP(6)  NOT NULL ENABLE,
    UPDATE_EMPLOYEE_NUMBER  NUMBER(6,0),
    UPDATE_DATETIME         TIMESTAMP(6),
    CONSTRAINT PK_DAILY_RECONCILIATION_ID PRIMARY KEY (DAILY_RECONCILIATION_ID)
  );

--------------------------------------------------------
-- Add comments on DAILY_RECONCILIATION.
--------------------------------------------------------
COMMENT ON TABLE DAILY_RECONCILIATION IS 'Table where the daily reconciliation report is stored.';
COMMENT ON COLUMN DAILY_RECONCILIATION.DAILY_RECONCILIATION_ID IS 'Identification number of the record, generated by DAILY_RECONCILIATION_ID_SEQ.';
COMMENT ON COLUMN DAILY_RECONCILIATION.TRANSACTION_DATE IS 'When the reconciliation is calculated for.';
COMMENT ON COLUMN DAILY_RECONCILIATION.FACILITY_ID IS 'Facility identification number for which the reconciliation is calculated.';
COMMENT ON COLUMN DAILY_RECONCILIATION.COMPLETED IS 'When completed=1 (true), then the record is read only.';
COMMENT ON COLUMN DAILY_RECONCILIATION.CREATE_EMPLOYEE_NUMBER IS 'Employee number who created the record.';
COMMENT ON COLUMN DAILY_RECONCILIATION.CREATE_DATETIME IS 'When the record has been created.';
COMMENT ON COLUMN DAILY_RECONCILIATION.UPDATE_EMPLOYEE_NUMBER IS 'Employee number who updated the record.';
COMMENT ON COLUMN DAILY_RECONCILIATION.UPDATE_DATETIME IS 'When the record has been updated.';

--------------------------------------------------------
-- Drop scripts:
--------------------------------------------------------
--drop table DAILY_RECONCILIATION;

--------------------------------------------------------
--  DDL for Table DAILY_RECONCILIATION
--------------------------------------------------------
-- CREATE TABLE "LIBERTY"."DAILY_RECONCILIATION"
--  (	"DAILY_RECONCILIATION_ID" NUMBER(10,0) NOT NULL ENABLE,
--"TRANSACTION_DATE" TIMESTAMP (6) NOT NULL ENABLE,
--"FACILITY_ID" NUMBER(15,5) NOT NULL ENABLE,
--"COMPLETED" CHAR(1 BYTE) DEFAULT 0 NOT NULL ENABLE,
--"CREATE_EMPLOYEE_NUMBER" NUMBER(6,0) NOT NULL ENABLE,
--"CREATE_DATETIME" TIMESTAMP (6) NOT NULL ENABLE,
--"UPDATE_EMPLOYEE_NUMBER" NUMBER(6,0),
--"UPDATE_DATETIME" TIMESTAMP (6),
-- CONSTRAINT "PK_DAILY_RECONCILIATION_ID" PRIMARY KEY ("DAILY_RECONCILIATION_ID")
-- USING INDEX PCTFREE 10 INITRANS 2 MAXTRANS 255
-- STORAGE(INITIAL 65536 NEXT 1048576 MINEXTENTS 1 MAXEXTENTS 2147483645
-- PCTINCREASE 0 FREELISTS 1 FREELIST GROUPS 1 BUFFER_POOL DEFAULT)
-- TABLESPACE "BNDNEW_DATA"  ENABLE
--  ) PCTFREE 10 PCTUSED 40 INITRANS 1 MAXTRANS 255 NOCOMPRESS LOGGING
-- STORAGE(INITIAL 65536 NEXT 1048576 MINEXTENTS 1 MAXEXTENTS 2147483645
-- PCTINCREASE 0 FREELISTS 1 FREELIST GROUPS 1 BUFFER_POOL DEFAULT)
-- TABLESPACE "BNDNEW_DATA" ;