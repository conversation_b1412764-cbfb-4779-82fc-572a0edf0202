--------------------------------------------------------
--  DDL for Table INSURANCE_COMPANY_ADDRESS
--------------------------------------------------------

  CREATE TABLE "LIBERTY_DATA"."INSURANCE_COMPANY_ADDRESS"
   (	"INS_CO_ADDRESS_ID" NUMBER(10,0), 
	"LINE_ONE" VARCHAR2(50 BYTE), 
	"LINE_TWO" VARCHAR2(50 BYTE), 
	"IN_CARE_OF" VARCHAR2(50 BYTE), 
	"CITY" VARCHAR2(35 BYTE), 
	"STATE" CHAR(2 BYTE), 
	"ZIPCODE" VARCHAR2(5 BYTE), 
	"ZIPCODE_4" VARCHAR2(4 BYTE), 
	"ADDRESS_DESCRIPTION" VARCHAR2(75 BYTE), 
	"CREATE_EMPLOYEE" NUMBER(10,0), 
	"CREATE_DATETIME" TIMESTAMP (6), 
	"UPDATE_EMPLOYEE" NUMBER(10,0), 
	"UPDATE_DATETIME" TIMESTAMP (6)
   ) PCTFREE 10 PCTUSED 40 INITRANS 1 MAXTRANS 255 NOCOMPRESS LOGGING
  STORAGE(INITIAL 65536 NEXT 1048576 MINEXTENTS 1 MAXEXTENTS 2147483645
  PCTINCREASE 0 FREELISTS 1 FREELIST GROUPS 1 BUFFER_POOL DEFAULT)
  TABLESPACE "BNDNEW_DATA" ;
