package com.smilebrands.liberty.claim.model;

import com.smilebrands.liberty.model.BaseObject;
import com.smilebrands.liberty.serializer.DateIsoDeSerializer;
import com.smilebrands.liberty.serializer.DateIsoSerializer;
import org.codehaus.jackson.map.annotate.JsonDeserialize;
import org.codehaus.jackson.map.annotate.JsonSerialize;
import org.hibernate.annotations.NotFound;
import org.hibernate.annotations.NotFoundAction;

import javax.persistence.*;
import java.util.Date;

/**
 * Created by phongpham on 3/3/14.
 */
@Entity
@NamedQueries({
        @NamedQuery(
                name = "AttachmentTransmitHistory.getByRequiredAttachmentId",
                query = "select t from AttachmentTransmitHistory t " +
                        "   left join fetch t.requiredAttachment r " +
                        "   left join fetch r.completionEmployee " +
                        "   left join fetch r.requirement " +
                        "   left join fetch r.createEmployee " +
                        "   left join fetch t.attachmentResolution " +
                        "where t.claimRequiredAttachmentId = ?"
        )
})

@Table(name = "ATTACHMENT_TRANSMIT_HISTORY")
public class AttachmentTransmitHistory extends BaseObject{

    @Id
    @Column(name = "ATTACHMENT_TRANSMIT_HISTORY_ID")
    @GeneratedValue(generator = "AttachmentTxHistorySeq")
    @SequenceGenerator(name = "AttachmentTxHistorySeq", sequenceName = "ATTACHMENT_TX_HISTORY_ID_SEQ", allocationSize = 1)
    private Long attachmentTransmitHistoryId;

    @Column(name = "ATTACHMENT_TRANSMIT_REQUEST_ID")
    private Long attachmentTransmitRequestId;

    @Column(name = "CLAIM_ID")
    private Long claimId;

    @Column(name = "REQ_ATTACHMENT_ID")
    private Long claimRequiredAttachmentId;

    @ManyToOne(optional=true,fetch=FetchType.EAGER)
    @NotFound(action = NotFoundAction.IGNORE)
    @JoinColumn(name="REQ_ATTACHMENT_ID",referencedColumnName="REQ_ATTACHMENT_ID",insertable=false, updatable=false, nullable = true)
    private ClaimRequiredAttachment requiredAttachment;

    @Column(name = "ATTACHMENT_RESOLUTION_ID")
    private Long attachmentResolutionId;

    @ManyToOne(optional=true,fetch=FetchType.EAGER)
    @NotFound(action = NotFoundAction.IGNORE)
    @JoinColumn(name="ATTACHMENT_RESOLUTION_ID",referencedColumnName="ATTACHMENT_RESOLUTION_ID",insertable=false, updatable=false, nullable = true)
    private AttachmentResolution attachmentResolution;

    @Column(name = "START_DATETIME", insertable = true, updatable = false)
    @Temporal(TemporalType.TIMESTAMP)
    private Date startDatetime = new Date();

    @Column(name = "TRANSMIT_DATETIME")
    @Temporal(TemporalType.TIMESTAMP)
    private Date transmitDatetime;

    @Column(name = "ACKNOWLEDGE_DATETIME")
    @Temporal(TemporalType.TIMESTAMP)
    private Date acknowledgeDatetime;

    @Column(name = "TRANSMIT_CODE")
    private Integer transmitCode;

    @Column(name = "TRANSMIT_MESSAGE")
    private String transmitMessage;

    @Column(name = "NEA_NUMBER")
    private String neaNumber;

    public Long getAttachmentTransmitHistoryId() {
        return attachmentTransmitHistoryId;
    }

    public void setAttachmentTransmitHistoryId(Long attachmentTransmitHistoryId) {
        this.attachmentTransmitHistoryId = attachmentTransmitHistoryId;
    }

    public Long getAttachmentTransmitRequestId() {
        return attachmentTransmitRequestId;
    }

    public void setAttachmentTransmitRequestId(Long attachmentTransmitRequestId) {
        this.attachmentTransmitRequestId = attachmentTransmitRequestId;
    }

    public Long getClaimId() {
        return claimId;
    }

    public void setClaimId(Long claimId) {
        this.claimId = claimId;
    }

    public Long getClaimRequiredAttachmentId() {
        return claimRequiredAttachmentId;
    }

    public void setClaimRequiredAttachmentId(Long claimRequiredAttachmentId) {
        this.claimRequiredAttachmentId = claimRequiredAttachmentId;
    }

    public ClaimRequiredAttachment getAttachmentRequirement() {
        return requiredAttachment;
    }

    public void setAttachmentRequirement(ClaimRequiredAttachment attachmentRequirement) {
        this.requiredAttachment = attachmentRequirement;
    }

    public Long getAttachmentResolutionId() {
        return attachmentResolutionId;
    }

    public void setAttachmentResolutionId(Long attachmentResolutionId) {
        this.attachmentResolutionId = attachmentResolutionId;
    }

    public AttachmentResolution getAttachmentResolution() {
        return attachmentResolution;
    }

    public void setAttachmentResolution(AttachmentResolution attachmentResolution) {
        this.attachmentResolution = attachmentResolution;
    }

    @JsonSerialize(using= DateIsoSerializer.class)
    public Date getStartDatetime() {
        return startDatetime;
    }

    @JsonDeserialize(using=DateIsoDeSerializer.class)
    public void setStartDatetime(Date startDatetime) {
        this.startDatetime = startDatetime;
    }

    @JsonSerialize(using= DateIsoSerializer.class)
    public Date getTransmitDatetime() {
        return transmitDatetime;
    }

    @JsonDeserialize(using=DateIsoDeSerializer.class)
    public void setTransmitDatetime(Date transmitDatetime) {
        this.transmitDatetime = transmitDatetime;
    }

    @JsonSerialize(using= DateIsoSerializer.class)
    public Date getAcknowledgeDatetime() {
        return acknowledgeDatetime;
    }

    @JsonDeserialize(using=DateIsoDeSerializer.class)
    public void setAcknowledgeDatetime(Date acknowledgeDatetime) {
        this.acknowledgeDatetime = acknowledgeDatetime;
    }

    public Integer getTransmitCode() {
        return transmitCode;
    }

    public void setTransmitCode(Integer transmitCode) {
        this.transmitCode = transmitCode;
    }

    public String getTransmitMessage() {
        return transmitMessage;
    }

    public void setTransmitMessage(String transmitMessage) {
        this.transmitMessage = transmitMessage;
    }

    public String getNeaNumber() { return neaNumber; }

    public void setNeaNumber(String neaNumber) { this.neaNumber = neaNumber; }

    public ClaimRequiredAttachment getRequiredAttachment() { return requiredAttachment; };

    public void setRequiredAttachment(ClaimRequiredAttachment requiredAttachment) { this.requiredAttachment = requiredAttachment; }
}
