DROP TABLE ALERT;
CREATE TABLE ALERT  (
	ALERT_ID                    	NUMBER(15) NOT NULL,
	ALERT_GROUP                 	VARCHAR2(35) NOT NULL,
	ALERT_MESSAGE               	VARCHAR2(4000) NULL,
	ALERT_SEVERITY              	VARCHAR2(35) NULL,
    PATIENT_ID                  	NUMBER(15) NULL,
	FACILITY_ID                 	NUMBER(6) NULL,
    CREATE_DATETIME             	TIMESTAMP(6) NOT NULL,
	EXPIRATION_DATETIME         	TIMESTAMP(6) NULL,
    <PERSON><PERSON><PERSON>OWLEDGEABLE                	CHAR(1) DEFAULT '0' NULL,
	<PERSON><PERSON><PERSON><PERSON>LEDGED                	CHAR(1) DEFAULT '0' NULL,
	ACKNOWLEDGED_EMPLOYEE_NUMBER	NUMBER(15,5) NULL,
	ACKNOWLEDGED_DATETIME       	TIMESTAMP(6) NULL,
	CONSTRAINT ALERT_PK PRIMARY KEY(ALERT_ID)
	NOT DEF<PERSON>RABLE
	 VALIDATE
);

CREATE SEQUENCE ALERT_USER_SEQ INCREMENT BY 1 START WITH 1;
CREATE TABLE ALERT_TARGET_USER  (
	ALERT_USER_ID                	NUMBER(15) NOT NULL,
	ALERT_ID                       	NUMBER(15) NOT NULL,
	EMPLOYEE_NUMBER               	NUMBER(6) NULL,
	EMPLOYEE_TITLE              	VARCHAR2(35) NULL,
	CONSTRAINT ALERT_TARGET_PK PRIMARY KEY(ALERT_USER_ID)
	NOT DEFERRABLE
	 VALIDATE
);

DROP TABLE ALERT_VIEW;
CREATE TABLE ALERT_VIEW  (
	ALERT_VIEW_ID         	NUMBER(15) NOT NULL,
	ALERT_ID              	NUMBER(15) NOT NULL,
    VIEWED_EMPLOYEE_NUMBER	NUMBER(6) NOT NULL,
	VIEWED_DATETIME       	TIMESTAMP(6) NOT NULL,
    SHOW_AGAIN_DATETIME     TIMESTAMP(6) NULL,
    ALERT_RESOLUTION        VARCHAR2(250) NULL,
	CONSTRAINT ALERT_VIEW_PK PRIMARY KEY(ALERT_VIEW_ID)
	NOT DEFERRABLE
	 VALIDATE
);

CREATE SEQUENCE ALERT_MODULE_SEQ INCREMENT BY 1 START WITH 1;
CREATE TABLE ALERT_TARGET_MODULE  (
	ALERT_MODULE_ID        	NUMBER(15) NOT NULL,
	ALERT_ID              	NUMBER(15) NOT NULL,
    MODULE_TYPE             VARCHAR2(35) NULL,
	CONSTRAINT ALERT_MODULE_PK PRIMARY KEY(ALERT_MODULE_ID)
	NOT DEFERRABLE
	 VALIDATE
);

DROP TABLE REQUIREMENT_TYPE;
CREATE TABLE REQUIREMENT_TYPE  (
	REQUIREMENT_ID           	NUMBER(4) NOT NULL,
	REQUIREMENT_DISPLAY      	VARCHAR2(35) NOT NULL,
	REQUIREMENT_DESCRIPTION  	VARCHAR2(300) NOT NULL,
	REQUIREMENT_GROUP        	VARCHAR2(10) NOT NULL,
	ATTACH_WITH_CLAIM        	CHAR(1) NOT NULL,
	IS_FILM                  	CHAR(1) NOT NULL,
	NEA_IMAGE_CODE           	NUMBER(8) NOT NULL,
	SELECTION_USER_TYPE      	VARCHAR2(10) NOT NULL,
	CREATE_DATETIME          	TIMESTAMP(6) NOT NULL,
	CREATE_EMPLOYEE_NUMBER   	NUMBER(6) NOT NULL,
	INACTIVE_DATETIME        	TIMESTAMP(6) NULL,
	INACTIVE_EMPLOYEE_NUMBER 	NUMBER(6) NULL,
	REQUIREMENT_DISPLAY_GROUP	VARCHAR2(25) NULL,
	CONSTRAINT REQ_ID_PK PRIMARY KEY(REQUIREMENT_ID)
	NOT DEFERRABLE
	 VALIDATE
);

/*** Insert new Requirement Types from Test. ***/
CREATE TABLE requirement_type  (
	REQUIREMENT_ID           	NUMBER(5) NOT NULL,
	REQUIREMENT_DISPLAY      	VARCHAR2(35) NOT NULL,
	REQUIREMENT_DESCRIPTION  	VARCHAR2(300) NOT NULL,
	REQUIREMENT_GROUP        	VARCHAR2(10) NOT NULL,
	ATTACH_WITH_CLAIM        	CHAR(1) NOT NULL,
	IS_FILM                  	CHAR(1) NOT NULL,
	NEA_IMAGE_CODE           	NUMBER(9) NOT NULL,
	SELECTION_USER_TYPE      	VARCHAR2(10) NOT NULL,
	CREATE_DATETIME          	TIMESTAMP NOT NULL,
	CREATE_EMPLOYEE_NUMBER   	NUMBER(7) NOT NULL,
	INACTIVE_DATETIME        	TIMESTAMP NULL,
	INACTIVE_EMPLOYEE_NUMBER 	NUMBER(7) NULL,
	REQUIREMENT_DISPLAY_GROUP	VARCHAR2(25) NULL
	);
INSERT INTO requirement_type(REQUIREMENT_ID, REQUIREMENT_DISPLAY, REQUIREMENT_DESCRIPTION, REQUIREMENT_GROUP, ATTACH_WITH_CLAIM, IS_FILM, NEA_IMAGE_CODE, SELECTION_USER_TYPE, CREATE_DATETIME, CREATE_EMPLOYEE_NUMBER, INACTIVE_DATETIME, INACTIVE_EMPLOYEE_NUMBER, REQUIREMENT_DISPLAY_GROUP)
  VALUES(1, 'Perio Chart', 'Perio Chart', 'ASSET', '1', '0', 6, 'CBO', TO_TIMESTAMP('2013-07-09 09:56:00:0','YYYY-MM-DD HH24:MI:SS:FF'), 110840, NULL, NULL, 'FORMS');
INSERT INTO requirement_type(REQUIREMENT_ID, REQUIREMENT_DISPLAY, REQUIREMENT_DESCRIPTION, REQUIREMENT_GROUP, ATTACH_WITH_CLAIM, IS_FILM, NEA_IMAGE_CODE, SELECTION_USER_TYPE, CREATE_DATETIME, CREATE_EMPLOYEE_NUMBER, INACTIVE_DATETIME, INACTIVE_EMPLOYEE_NUMBER, REQUIREMENT_DISPLAY_GROUP)
  VALUES(2, 'Single Bitewing', 'Bitewing Xrays', 'ASSET', '1', '1', 1, 'CBO', TO_TIMESTAMP('2013-07-09 09:56:00:0','YYYY-MM-DD HH24:MI:SS:FF'), 110840, NULL, NULL, 'IMAGES');
INSERT INTO requirement_type(REQUIREMENT_ID, REQUIREMENT_DISPLAY, REQUIREMENT_DESCRIPTION, REQUIREMENT_GROUP, ATTACH_WITH_CLAIM, IS_FILM, NEA_IMAGE_CODE, SELECTION_USER_TYPE, CREATE_DATETIME, CREATE_EMPLOYEE_NUMBER, INACTIVE_DATETIME, INACTIVE_EMPLOYEE_NUMBER, REQUIREMENT_DISPLAY_GROUP)
  VALUES(3, 'Date of Birth', 'Date of Birth', 'DATA', '0', '0', 0, 'CBO', TO_TIMESTAMP('2013-07-09 10:14:48:0','YYYY-MM-DD HH24:MI:SS:FF'), 110840, NULL, NULL, 'INFO');
INSERT INTO requirement_type(REQUIREMENT_ID, REQUIREMENT_DISPLAY, REQUIREMENT_DESCRIPTION, REQUIREMENT_GROUP, ATTACH_WITH_CLAIM, IS_FILM, NEA_IMAGE_CODE, SELECTION_USER_TYPE, CREATE_DATETIME, CREATE_EMPLOYEE_NUMBER, INACTIVE_DATETIME, INACTIVE_EMPLOYEE_NUMBER, REQUIREMENT_DISPLAY_GROUP)
  VALUES(4, 'Missing SSN', 'Missing Social Security Number', 'DATA', '0', '0', 0, 'CBO', TO_TIMESTAMP('2013-07-09 10:14:48:0','YYYY-MM-DD HH24:MI:SS:FF'), 110840, NULL, NULL, 'INFO');
INSERT INTO requirement_type(REQUIREMENT_ID, REQUIREMENT_DISPLAY, REQUIREMENT_DESCRIPTION, REQUIREMENT_GROUP, ATTACH_WITH_CLAIM, IS_FILM, NEA_IMAGE_CODE, SELECTION_USER_TYPE, CREATE_DATETIME, CREATE_EMPLOYEE_NUMBER, INACTIVE_DATETIME, INACTIVE_EMPLOYEE_NUMBER, REQUIREMENT_DISPLAY_GROUP)
  VALUES(9, 'Authorization Number', 'Authorization Number', 'DATA', '0', '0', 0, 'CBO', TO_TIMESTAMP('2013-07-09 10:14:48:0','YYYY-MM-DD HH24:MI:SS:FF'), 110840, NULL, NULL, 'INFO');
INSERT INTO requirement_type(REQUIREMENT_ID, REQUIREMENT_DISPLAY, REQUIREMENT_DESCRIPTION, REQUIREMENT_GROUP, ATTACH_WITH_CLAIM, IS_FILM, NEA_IMAGE_CODE, SELECTION_USER_TYPE, CREATE_DATETIME, CREATE_EMPLOYEE_NUMBER, INACTIVE_DATETIME, INACTIVE_EMPLOYEE_NUMBER, REQUIREMENT_DISPLAY_GROUP)
  VALUES(10, 'Complete treatment on NOA', 'Additional treatment to be completed on NOA before claims can be billed', 'DATA', '0', '0', 0, 'CBO', TO_TIMESTAMP('2013-07-09 10:14:48:0','YYYY-MM-DD HH24:MI:SS:FF'), 110840, NULL, NULL, 'INFO');
INSERT INTO requirement_type(REQUIREMENT_ID, REQUIREMENT_DISPLAY, REQUIREMENT_DESCRIPTION, REQUIREMENT_GROUP, ATTACH_WITH_CLAIM, IS_FILM, NEA_IMAGE_CODE, SELECTION_USER_TYPE, CREATE_DATETIME, CREATE_EMPLOYEE_NUMBER, INACTIVE_DATETIME, INACTIVE_EMPLOYEE_NUMBER, REQUIREMENT_DISPLAY_GROUP)
  VALUES(11, 'Insurance Card', 'Insurance Card', 'ASSET', '0', '0', 0, 'CBO', TO_TIMESTAMP('2013-07-09 10:03:42:0','YYYY-MM-DD HH24:MI:SS:FF'), 110840, NULL, NULL, 'INFO');
INSERT INTO requirement_type(REQUIREMENT_ID, REQUIREMENT_DISPLAY, REQUIREMENT_DESCRIPTION, REQUIREMENT_GROUP, ATTACH_WITH_CLAIM, IS_FILM, NEA_IMAGE_CODE, SELECTION_USER_TYPE, CREATE_DATETIME, CREATE_EMPLOYEE_NUMBER, INACTIVE_DATETIME, INACTIVE_EMPLOYEE_NUMBER, REQUIREMENT_DISPLAY_GROUP)
  VALUES(12, 'Delivery Date', 'Date prosthesis was delivered', 'DATA', '0', '0', 0, 'CBO', TO_TIMESTAMP('2013-07-09 10:14:48:0','YYYY-MM-DD HH24:MI:SS:FF'), 110840, NULL, NULL, 'HISTORY');
INSERT INTO requirement_type(REQUIREMENT_ID, REQUIREMENT_DISPLAY, REQUIREMENT_DESCRIPTION, REQUIREMENT_GROUP, ATTACH_WITH_CLAIM, IS_FILM, NEA_IMAGE_CODE, SELECTION_USER_TYPE, CREATE_DATETIME, CREATE_EMPLOYEE_NUMBER, INACTIVE_DATETIME, INACTIVE_EMPLOYEE_NUMBER, REQUIREMENT_DISPLAY_GROUP)
  VALUES(17, 'Final PA', 'Final Periapical Xray from Root Canal Procedure', 'ASSET', '1', '1', 2, 'CBO', TO_TIMESTAMP('2013-07-09 10:03:42:0','YYYY-MM-DD HH24:MI:SS:FF'), 110840, NULL, NULL, 'IMAGES');
INSERT INTO requirement_type(REQUIREMENT_ID, REQUIREMENT_DISPLAY, REQUIREMENT_DESCRIPTION, REQUIREMENT_GROUP, ATTACH_WITH_CLAIM, IS_FILM, NEA_IMAGE_CODE, SELECTION_USER_TYPE, CREATE_DATETIME, CREATE_EMPLOYEE_NUMBER, INACTIVE_DATETIME, INACTIVE_EMPLOYEE_NUMBER, REQUIREMENT_DISPLAY_GROUP)
  VALUES(18, 'Full Mouth Xrays', 'Full Mouth Xrays', 'ASSET', '1', '1', 4, 'CBO', TO_TIMESTAMP('2013-07-09 09:56:00:0','YYYY-MM-DD HH24:MI:SS:FF'), 110840, NULL, NULL, 'IMAGES');
INSERT INTO requirement_type(REQUIREMENT_ID, REQUIREMENT_DISPLAY, REQUIREMENT_DESCRIPTION, REQUIREMENT_GROUP, ATTACH_WITH_CLAIM, IS_FILM, NEA_IMAGE_CODE, SELECTION_USER_TYPE, CREATE_DATETIME, CREATE_EMPLOYEE_NUMBER, INACTIVE_DATETIME, INACTIVE_EMPLOYEE_NUMBER, REQUIREMENT_DISPLAY_GROUP)
  VALUES(19, 'History', 'History of prosthesis - date/age', 'DATA', '0', '0', 0, 'CBO', TO_TIMESTAMP('2013-07-09 10:14:48:0','YYYY-MM-DD HH24:MI:SS:FF'), 110840, NULL, NULL, 'HISTORY');
INSERT INTO requirement_type(REQUIREMENT_ID, REQUIREMENT_DISPLAY, REQUIREMENT_DESCRIPTION, REQUIREMENT_GROUP, ATTACH_WITH_CLAIM, IS_FILM, NEA_IMAGE_CODE, SELECTION_USER_TYPE, CREATE_DATETIME, CREATE_EMPLOYEE_NUMBER, INACTIVE_DATETIME, INACTIVE_EMPLOYEE_NUMBER, REQUIREMENT_DISPLAY_GROUP)
  VALUES(20, 'Initial/Replacement', 'Information regarding if prosthesis is initial or replacement', 'DATA', '0', '0', 0, 'CBO', TO_TIMESTAMP('2013-07-09 10:14:48:0','YYYY-MM-DD HH24:MI:SS:FF'), 110840, NULL, NULL, 'HISTORY');
INSERT INTO requirement_type(REQUIREMENT_ID, REQUIREMENT_DISPLAY, REQUIREMENT_DESCRIPTION, REQUIREMENT_GROUP, ATTACH_WITH_CLAIM, IS_FILM, NEA_IMAGE_CODE, SELECTION_USER_TYPE, CREATE_DATETIME, CREATE_EMPLOYEE_NUMBER, INACTIVE_DATETIME, INACTIVE_EMPLOYEE_NUMBER, REQUIREMENT_DISPLAY_GROUP)
  VALUES(21, 'IV sedation Records', 'IV sedation Records', 'ASSET', '0', '0', 0, 'CBO', TO_TIMESTAMP('2013-07-09 10:03:42:0','YYYY-MM-DD HH24:MI:SS:FF'), 110840, NULL, NULL, 'FORMS');
INSERT INTO requirement_type(REQUIREMENT_ID, REQUIREMENT_DISPLAY, REQUIREMENT_DESCRIPTION, REQUIREMENT_GROUP, ATTACH_WITH_CLAIM, IS_FILM, NEA_IMAGE_CODE, SELECTION_USER_TYPE, CREATE_DATETIME, CREATE_EMPLOYEE_NUMBER, INACTIVE_DATETIME, INACTIVE_EMPLOYEE_NUMBER, REQUIREMENT_DISPLAY_GROUP)
  VALUES(22, 'Justification for Prosthesis', 'Form for Dentures', 'ASSET', '0', '0', 0, 'CBO', TO_TIMESTAMP('2013-07-09 10:03:42:0','YYYY-MM-DD HH24:MI:SS:FF'), 110840, NULL, NULL, 'FORMS');
INSERT INTO requirement_type(REQUIREMENT_ID, REQUIREMENT_DISPLAY, REQUIREMENT_DESCRIPTION, REQUIREMENT_GROUP, ATTACH_WITH_CLAIM, IS_FILM, NEA_IMAGE_CODE, SELECTION_USER_TYPE, CREATE_DATETIME, CREATE_EMPLOYEE_NUMBER, INACTIVE_DATETIME, INACTIVE_EMPLOYEE_NUMBER, REQUIREMENT_DISPLAY_GROUP)
  VALUES(23, 'Lab invoice', 'Lab invoice', 'ASSET', '0', '0', 0, 'CBO', TO_TIMESTAMP('2013-07-09 10:03:42:0','YYYY-MM-DD HH24:MI:SS:FF'), 110840, NULL, NULL, 'FORMS');
INSERT INTO requirement_type(REQUIREMENT_ID, REQUIREMENT_DISPLAY, REQUIREMENT_DESCRIPTION, REQUIREMENT_GROUP, ATTACH_WITH_CLAIM, IS_FILM, NEA_IMAGE_CODE, SELECTION_USER_TYPE, CREATE_DATETIME, CREATE_EMPLOYEE_NUMBER, INACTIVE_DATETIME, INACTIVE_EMPLOYEE_NUMBER, REQUIREMENT_DISPLAY_GROUP)
  VALUES(24, 'Missing Group #', 'Missing Group #', 'DATA', '0', '0', 0, 'CBO', TO_TIMESTAMP('2013-07-09 10:14:48:0','YYYY-MM-DD HH24:MI:SS:FF'), 110840, NULL, NULL, 'INFO');
INSERT INTO requirement_type(REQUIREMENT_ID, REQUIREMENT_DISPLAY, REQUIREMENT_DESCRIPTION, REQUIREMENT_GROUP, ATTACH_WITH_CLAIM, IS_FILM, NEA_IMAGE_CODE, SELECTION_USER_TYPE, CREATE_DATETIME, CREATE_EMPLOYEE_NUMBER, INACTIVE_DATETIME, INACTIVE_EMPLOYEE_NUMBER, REQUIREMENT_DISPLAY_GROUP)
  VALUES(25, 'Missing relationship', 'Missing relationship', 'DATA', '0', '0', 0, 'CBO', TO_TIMESTAMP('2013-07-09 10:14:48:0','YYYY-MM-DD HH24:MI:SS:FF'), 110840, NULL, NULL, 'INFO');
INSERT INTO requirement_type(REQUIREMENT_ID, REQUIREMENT_DISPLAY, REQUIREMENT_DESCRIPTION, REQUIREMENT_GROUP, ATTACH_WITH_CLAIM, IS_FILM, NEA_IMAGE_CODE, SELECTION_USER_TYPE, CREATE_DATETIME, CREATE_EMPLOYEE_NUMBER, INACTIVE_DATETIME, INACTIVE_EMPLOYEE_NUMBER, REQUIREMENT_DISPLAY_GROUP)
  VALUES(26, 'Narrative', 'Information regarding reason procedure was done/ Treatment notes', 'ASSET', '1', '0', 361, 'CBO', TO_TIMESTAMP('2013-07-09 09:56:00:0','YYYY-MM-DD HH24:MI:SS:FF'), 110840, NULL, NULL, 'NARRATIVE');
INSERT INTO requirement_type(REQUIREMENT_ID, REQUIREMENT_DISPLAY, REQUIREMENT_DESCRIPTION, REQUIREMENT_GROUP, ATTACH_WITH_CLAIM, IS_FILM, NEA_IMAGE_CODE, SELECTION_USER_TYPE, CREATE_DATETIME, CREATE_EMPLOYEE_NUMBER, INACTIVE_DATETIME, INACTIVE_EMPLOYEE_NUMBER, REQUIREMENT_DISPLAY_GROUP)
  VALUES(27, 'NOA', 'Notification of Authorization', 'ASSET', '0', '0', 0, 'CBO', TO_TIMESTAMP('2013-07-09 10:03:42:0','YYYY-MM-DD HH24:MI:SS:FF'), 110840, NULL, NULL, 'INFO');
INSERT INTO requirement_type(REQUIREMENT_ID, REQUIREMENT_DISPLAY, REQUIREMENT_DESCRIPTION, REQUIREMENT_GROUP, ATTACH_WITH_CLAIM, IS_FILM, NEA_IMAGE_CODE, SELECTION_USER_TYPE, CREATE_DATETIME, CREATE_EMPLOYEE_NUMBER, INACTIVE_DATETIME, INACTIVE_EMPLOYEE_NUMBER, REQUIREMENT_DISPLAY_GROUP)
  VALUES(28, 'Ortho Diagnosis Sheet', 'Diagnosis sheet with phase of treatment, length, etc for Ortho', 'ASSET', '0', '0', 0, 'CBO', TO_TIMESTAMP('2013-07-09 10:03:42:0','YYYY-MM-DD HH24:MI:SS:FF'), 110840, NULL, NULL, 'FORMS');
INSERT INTO requirement_type(REQUIREMENT_ID, REQUIREMENT_DISPLAY, REQUIREMENT_DESCRIPTION, REQUIREMENT_GROUP, ATTACH_WITH_CLAIM, IS_FILM, NEA_IMAGE_CODE, SELECTION_USER_TYPE, CREATE_DATETIME, CREATE_EMPLOYEE_NUMBER, INACTIVE_DATETIME, INACTIVE_EMPLOYEE_NUMBER, REQUIREMENT_DISPLAY_GROUP)
  VALUES(29, 'Ortho Patient Info Sheet', 'Patient information sheet', 'ASSET', '0', '0', 0, 'CBO', TO_TIMESTAMP('2013-07-09 10:03:42:0','YYYY-MM-DD HH24:MI:SS:FF'), 110840, NULL, NULL, 'FORMS');
INSERT INTO requirement_type(REQUIREMENT_ID, REQUIREMENT_DISPLAY, REQUIREMENT_DESCRIPTION, REQUIREMENT_GROUP, ATTACH_WITH_CLAIM, IS_FILM, NEA_IMAGE_CODE, SELECTION_USER_TYPE, CREATE_DATETIME, CREATE_EMPLOYEE_NUMBER, INACTIVE_DATETIME, INACTIVE_EMPLOYEE_NUMBER, REQUIREMENT_DISPLAY_GROUP)
  VALUES(30, 'Ortho Records', 'Ortho Record packet', 'ASSET', '0', '0', 0, 'CBO', TO_TIMESTAMP('2013-07-09 10:03:42:0','YYYY-MM-DD HH24:MI:SS:FF'), 110840, NULL, NULL, 'FORMS');
INSERT INTO requirement_type(REQUIREMENT_ID, REQUIREMENT_DISPLAY, REQUIREMENT_DESCRIPTION, REQUIREMENT_GROUP, ATTACH_WITH_CLAIM, IS_FILM, NEA_IMAGE_CODE, SELECTION_USER_TYPE, CREATE_DATETIME, CREATE_EMPLOYEE_NUMBER, INACTIVE_DATETIME, INACTIVE_EMPLOYEE_NUMBER, REQUIREMENT_DISPLAY_GROUP)
  VALUES(31, 'PA', 'Periapical Xrays', 'ASSET', '1', '1', 2, 'CBO', TO_TIMESTAMP('2013-07-09 09:56:00:0','YYYY-MM-DD HH24:MI:SS:FF'), 110840, NULL, NULL, 'IMAGES');
INSERT INTO requirement_type(REQUIREMENT_ID, REQUIREMENT_DISPLAY, REQUIREMENT_DESCRIPTION, REQUIREMENT_GROUP, ATTACH_WITH_CLAIM, IS_FILM, NEA_IMAGE_CODE, SELECTION_USER_TYPE, CREATE_DATETIME, CREATE_EMPLOYEE_NUMBER, INACTIVE_DATETIME, INACTIVE_EMPLOYEE_NUMBER, REQUIREMENT_DISPLAY_GROUP)
  VALUES(32, 'Pano', 'Panoramic Xray', 'ASSET', '1', '1', 5, 'CBO', TO_TIMESTAMP('2013-07-09 09:56:00:0','YYYY-MM-DD HH24:MI:SS:FF'), 110840, NULL, NULL, 'IMAGES');
INSERT INTO requirement_type(REQUIREMENT_ID, REQUIREMENT_DISPLAY, REQUIREMENT_DESCRIPTION, REQUIREMENT_GROUP, ATTACH_WITH_CLAIM, IS_FILM, NEA_IMAGE_CODE, SELECTION_USER_TYPE, CREATE_DATETIME, CREATE_EMPLOYEE_NUMBER, INACTIVE_DATETIME, INACTIVE_EMPLOYEE_NUMBER, REQUIREMENT_DISPLAY_GROUP)
  VALUES(33, 'Photos', 'Intraoral Photos', 'ASSET', '1', '0', 8, 'CBO', TO_TIMESTAMP('2013-07-09 09:56:00:0','YYYY-MM-DD HH24:MI:SS:FF'), 110840, NULL, NULL, 'IMAGES');
INSERT INTO requirement_type(REQUIREMENT_ID, REQUIREMENT_DISPLAY, REQUIREMENT_DESCRIPTION, REQUIREMENT_GROUP, ATTACH_WITH_CLAIM, IS_FILM, NEA_IMAGE_CODE, SELECTION_USER_TYPE, CREATE_DATETIME, CREATE_EMPLOYEE_NUMBER, INACTIVE_DATETIME, INACTIVE_EMPLOYEE_NUMBER, REQUIREMENT_DISPLAY_GROUP)
  VALUES(34, 'Pre Op xrays', 'Pre Op xrays', 'ASSET', '0', '1', 0, 'CBO', TO_TIMESTAMP('2013-07-09 10:03:42:0','YYYY-MM-DD HH24:MI:SS:FF'), 110840, NULL, NULL, 'IMAGES');
INSERT INTO requirement_type(REQUIREMENT_ID, REQUIREMENT_DISPLAY, REQUIREMENT_DESCRIPTION, REQUIREMENT_GROUP, ATTACH_WITH_CLAIM, IS_FILM, NEA_IMAGE_CODE, SELECTION_USER_TYPE, CREATE_DATETIME, CREATE_EMPLOYEE_NUMBER, INACTIVE_DATETIME, INACTIVE_EMPLOYEE_NUMBER, REQUIREMENT_DISPLAY_GROUP)
  VALUES(35, 'Referral', 'Payor Referral Sheet', 'ASSET', '0', '0', 0, 'CBO', TO_TIMESTAMP('2013-07-09 10:03:42:0','YYYY-MM-DD HH24:MI:SS:FF'), 110840, NULL, NULL, 'FORMS');
INSERT INTO requirement_type(REQUIREMENT_ID, REQUIREMENT_DISPLAY, REQUIREMENT_DESCRIPTION, REQUIREMENT_GROUP, ATTACH_WITH_CLAIM, IS_FILM, NEA_IMAGE_CODE, SELECTION_USER_TYPE, CREATE_DATETIME, CREATE_EMPLOYEE_NUMBER, INACTIVE_DATETIME, INACTIVE_EMPLOYEE_NUMBER, REQUIREMENT_DISPLAY_GROUP)
  VALUES(36, 'Signature Strips', 'Insured signature', 'ASSET', '0', '0', 0, 'CBO', TO_TIMESTAMP('2013-07-09 10:03:42:0','YYYY-MM-DD HH24:MI:SS:FF'), 110840, NULL, NULL, 'INFO');
INSERT INTO requirement_type(REQUIREMENT_ID, REQUIREMENT_DISPLAY, REQUIREMENT_DESCRIPTION, REQUIREMENT_GROUP, ATTACH_WITH_CLAIM, IS_FILM, NEA_IMAGE_CODE, SELECTION_USER_TYPE, CREATE_DATETIME, CREATE_EMPLOYEE_NUMBER, INACTIVE_DATETIME, INACTIVE_EMPLOYEE_NUMBER, REQUIREMENT_DISPLAY_GROUP)
  VALUES(37, 'Student Status', 'Student Status information', 'ASSET', '1', '0', 9, 'CBO', TO_TIMESTAMP('2013-07-09 10:14:48:0','YYYY-MM-DD HH24:MI:SS:FF'), 110840, NULL, NULL, 'INFO');
INSERT INTO requirement_type(REQUIREMENT_ID, REQUIREMENT_DISPLAY, REQUIREMENT_DESCRIPTION, REQUIREMENT_GROUP, ATTACH_WITH_CLAIM, IS_FILM, NEA_IMAGE_CODE, SELECTION_USER_TYPE, CREATE_DATETIME, CREATE_EMPLOYEE_NUMBER, INACTIVE_DATETIME, INACTIVE_EMPLOYEE_NUMBER, REQUIREMENT_DISPLAY_GROUP)
  VALUES(38, 'Study Models', 'Study Models', 'ASSET', '0', '0', 0, 'CBO', TO_TIMESTAMP('2013-07-09 10:03:42:0','YYYY-MM-DD HH24:MI:SS:FF'), 110840, NULL, NULL, 'HISTORY');
INSERT INTO requirement_type(REQUIREMENT_ID, REQUIREMENT_DISPLAY, REQUIREMENT_DESCRIPTION, REQUIREMENT_GROUP, ATTACH_WITH_CLAIM, IS_FILM, NEA_IMAGE_CODE, SELECTION_USER_TYPE, CREATE_DATETIME, CREATE_EMPLOYEE_NUMBER, INACTIVE_DATETIME, INACTIVE_EMPLOYEE_NUMBER, REQUIREMENT_DISPLAY_GROUP)
  VALUES(39, 'Teeth/Clasp Info', 'Teeth/Clasp info for partials', 'DATA', '0', '0', 0, 'CBO', TO_TIMESTAMP('2013-07-09 10:14:48:0','YYYY-MM-DD HH24:MI:SS:FF'), 110840, NULL, NULL, 'HISTORY');
INSERT INTO requirement_type(REQUIREMENT_ID, REQUIREMENT_DISPLAY, REQUIREMENT_DESCRIPTION, REQUIREMENT_GROUP, ATTACH_WITH_CLAIM, IS_FILM, NEA_IMAGE_CODE, SELECTION_USER_TYPE, CREATE_DATETIME, CREATE_EMPLOYEE_NUMBER, INACTIVE_DATETIME, INACTIVE_EMPLOYEE_NUMBER, REQUIREMENT_DISPLAY_GROUP)
  VALUES(40, 'Teeth/Quad Info', 'Teeth/Quad info for claim', 'DATA', '0', '0', 0, 'CBO', TO_TIMESTAMP('2013-07-09 10:14:48:0','YYYY-MM-DD HH24:MI:SS:FF'), 110840, NULL, NULL, 'HISTORY');
INSERT INTO requirement_type(REQUIREMENT_ID, REQUIREMENT_DISPLAY, REQUIREMENT_DESCRIPTION, REQUIREMENT_GROUP, ATTACH_WITH_CLAIM, IS_FILM, NEA_IMAGE_CODE, SELECTION_USER_TYPE, CREATE_DATETIME, CREATE_EMPLOYEE_NUMBER, INACTIVE_DATETIME, INACTIVE_EMPLOYEE_NUMBER, REQUIREMENT_DISPLAY_GROUP)
  VALUES(42, 'Member Id', 'Member Id', 'DATA', '0', '0', 0, 'CBO', TO_TIMESTAMP('2013-07-09 10:14:48:0','YYYY-MM-DD HH24:MI:SS:FF'), 110840, NULL, NULL, 'INFO');
INSERT INTO requirement_type(REQUIREMENT_ID, REQUIREMENT_DISPLAY, REQUIREMENT_DESCRIPTION, REQUIREMENT_GROUP, ATTACH_WITH_CLAIM, IS_FILM, NEA_IMAGE_CODE, SELECTION_USER_TYPE, CREATE_DATETIME, CREATE_EMPLOYEE_NUMBER, INACTIVE_DATETIME, INACTIVE_EMPLOYEE_NUMBER, REQUIREMENT_DISPLAY_GROUP)
  VALUES(43, 'Pathology Report', 'Pathology Report', 'ASSET', '1', '0', 362, 'CBO', TO_TIMESTAMP('2013-07-09 10:03:42:0','YYYY-MM-DD HH24:MI:SS:FF'), 110840, NULL, NULL, 'FORMS');
INSERT INTO requirement_type(REQUIREMENT_ID, REQUIREMENT_DISPLAY, REQUIREMENT_DESCRIPTION, REQUIREMENT_GROUP, ATTACH_WITH_CLAIM, IS_FILM, NEA_IMAGE_CODE, SELECTION_USER_TYPE, CREATE_DATETIME, CREATE_EMPLOYEE_NUMBER, INACTIVE_DATETIME, INACTIVE_EMPLOYEE_NUMBER, REQUIREMENT_DISPLAY_GROUP)
  VALUES(44, 'Ortho Account Review', 'Ortho Account Review Required', 'DATA', '0', '0', 0, 'CBO', TO_TIMESTAMP('2013-07-09 10:14:48:0','YYYY-MM-DD HH24:MI:SS:FF'), 110840, NULL, NULL, 'FORMS');
INSERT INTO requirement_type(REQUIREMENT_ID, REQUIREMENT_DISPLAY, REQUIREMENT_DESCRIPTION, REQUIREMENT_GROUP, ATTACH_WITH_CLAIM, IS_FILM, NEA_IMAGE_CODE, SELECTION_USER_TYPE, CREATE_DATETIME, CREATE_EMPLOYEE_NUMBER, INACTIVE_DATETIME, INACTIVE_EMPLOYEE_NUMBER, REQUIREMENT_DISPLAY_GROUP)
  VALUES(45, 'Perio History', 'Perio History Required', 'DATA', '1', '0', 361, 'CBO', TO_TIMESTAMP('2013-07-09 10:14:48:0','YYYY-MM-DD HH24:MI:SS:FF'), 110840, NULL, NULL, 'HISTORY');
INSERT INTO requirement_type(REQUIREMENT_ID, REQUIREMENT_DISPLAY, REQUIREMENT_DESCRIPTION, REQUIREMENT_GROUP, ATTACH_WITH_CLAIM, IS_FILM, NEA_IMAGE_CODE, SELECTION_USER_TYPE, CREATE_DATETIME, CREATE_EMPLOYEE_NUMBER, INACTIVE_DATETIME, INACTIVE_EMPLOYEE_NUMBER, REQUIREMENT_DISPLAY_GROUP)
  VALUES(46, 'Pano/Ceph', 'Cephalometric Xray', 'ASSET', '1', '1', 11, 'CBO', TO_TIMESTAMP('2013-07-09 09:56:00:0','YYYY-MM-DD HH24:MI:SS:FF'), 110840, NULL, NULL, 'IMAGES');
INSERT INTO requirement_type(REQUIREMENT_ID, REQUIREMENT_DISPLAY, REQUIREMENT_DESCRIPTION, REQUIREMENT_GROUP, ATTACH_WITH_CLAIM, IS_FILM, NEA_IMAGE_CODE, SELECTION_USER_TYPE, CREATE_DATETIME, CREATE_EMPLOYEE_NUMBER, INACTIVE_DATETIME, INACTIVE_EMPLOYEE_NUMBER, REQUIREMENT_DISPLAY_GROUP)
  VALUES(47, 'Update Ortho Contract Info', 'Ortho Contract Info', 'DATA', '0', '0', 0, 'CBO', TO_TIMESTAMP('2013-07-09 10:14:48:0','YYYY-MM-DD HH24:MI:SS:FF'), 110840, NULL, NULL, 'INFO');
INSERT INTO requirement_type(REQUIREMENT_ID, REQUIREMENT_DISPLAY, REQUIREMENT_DESCRIPTION, REQUIREMENT_GROUP, ATTACH_WITH_CLAIM, IS_FILM, NEA_IMAGE_CODE, SELECTION_USER_TYPE, CREATE_DATETIME, CREATE_EMPLOYEE_NUMBER, INACTIVE_DATETIME, INACTIVE_EMPLOYEE_NUMBER, REQUIREMENT_DISPLAY_GROUP)
  VALUES(49, 'Missing Pat Address', 'Missing patient address and/or demographics', 'DATA', '0', '0', 0, 'CBO', TO_TIMESTAMP('2013-07-09 10:14:48:0','YYYY-MM-DD HH24:MI:SS:FF'), 110840, NULL, NULL, 'INFO');
INSERT INTO requirement_type(REQUIREMENT_ID, REQUIREMENT_DISPLAY, REQUIREMENT_DESCRIPTION, REQUIREMENT_GROUP, ATTACH_WITH_CLAIM, IS_FILM, NEA_IMAGE_CODE, SELECTION_USER_TYPE, CREATE_DATETIME, CREATE_EMPLOYEE_NUMBER, INACTIVE_DATETIME, INACTIVE_EMPLOYEE_NUMBER, REQUIREMENT_DISPLAY_GROUP)
  VALUES(50, 'Ins Request Info From Pat', 'Insurance denied until patient info is received', 'DATA', '0', '0', 0, 'CBO', TO_TIMESTAMP('2013-07-09 10:14:48:0','YYYY-MM-DD HH24:MI:SS:FF'), 110840, NULL, NULL, 'INFO');
INSERT INTO requirement_type(REQUIREMENT_ID, REQUIREMENT_DISPLAY, REQUIREMENT_DESCRIPTION, REQUIREMENT_GROUP, ATTACH_WITH_CLAIM, IS_FILM, NEA_IMAGE_CODE, SELECTION_USER_TYPE, CREATE_DATETIME, CREATE_EMPLOYEE_NUMBER, INACTIVE_DATETIME, INACTIVE_EMPLOYEE_NUMBER, REQUIREMENT_DISPLAY_GROUP)
  VALUES(52, 'Insurance Termed/Cancelled', 'Insurance has been termed or cancelled', 'DATA', '0', '0', 0, 'CBO', TO_TIMESTAMP('2013-07-09 10:14:48:0','YYYY-MM-DD HH24:MI:SS:FF'), 110840, NULL, NULL, 'INFO');
INSERT INTO requirement_type(REQUIREMENT_ID, REQUIREMENT_DISPLAY, REQUIREMENT_DESCRIPTION, REQUIREMENT_GROUP, ATTACH_WITH_CLAIM, IS_FILM, NEA_IMAGE_CODE, SELECTION_USER_TYPE, CREATE_DATETIME, CREATE_EMPLOYEE_NUMBER, INACTIVE_DATETIME, INACTIVE_EMPLOYEE_NUMBER, REQUIREMENT_DISPLAY_GROUP)
  VALUES(53, 'Partial Mouth Series', 'Partial Mouth Series', 'ASSET', '1', '1', 3, 'CBO', TO_TIMESTAMP('2013-07-09 10:14:48:0','YYYY-MM-DD HH24:MI:SS:FF'), 110840, NULL, NULL, 'IMAGES');
INSERT INTO requirement_type(REQUIREMENT_ID, REQUIREMENT_DISPLAY, REQUIREMENT_DESCRIPTION, REQUIREMENT_GROUP, ATTACH_WITH_CLAIM, IS_FILM, NEA_IMAGE_CODE, SELECTION_USER_TYPE, CREATE_DATETIME, CREATE_EMPLOYEE_NUMBER, INACTIVE_DATETIME, INACTIVE_EMPLOYEE_NUMBER, REQUIREMENT_DISPLAY_GROUP)
  VALUES(54, 'EOB', 'Explanation of Benefits', 'ASSET', '1', '0', 0, 'CBO', TO_TIMESTAMP('2013-07-09 10:14:48:0','YYYY-MM-DD HH24:MI:SS:FF'), 110840, NULL, NULL, 'INFO');
INSERT INTO requirement_type(REQUIREMENT_ID, REQUIREMENT_DISPLAY, REQUIREMENT_DESCRIPTION, REQUIREMENT_GROUP, ATTACH_WITH_CLAIM, IS_FILM, NEA_IMAGE_CODE, SELECTION_USER_TYPE, CREATE_DATETIME, CREATE_EMPLOYEE_NUMBER, INACTIVE_DATETIME, INACTIVE_EMPLOYEE_NUMBER, REQUIREMENT_DISPLAY_GROUP)
  VALUES(55, 'EHG Error', 'EHG Error', 'DATA', '0', '0', 0, 'CAMEL', TO_TIMESTAMP('2013-07-23 14:51:26:0','YYYY-MM-DD HH24:MI:SS:FF'), 103208, NULL, NULL, 'INFO');
INSERT INTO requirement_type(REQUIREMENT_ID, REQUIREMENT_DISPLAY, REQUIREMENT_DESCRIPTION, REQUIREMENT_GROUP, ATTACH_WITH_CLAIM, IS_FILM, NEA_IMAGE_CODE, SELECTION_USER_TYPE, CREATE_DATETIME, CREATE_EMPLOYEE_NUMBER, INACTIVE_DATETIME, INACTIVE_EMPLOYEE_NUMBER, REQUIREMENT_DISPLAY_GROUP)
  VALUES(56, 'NEA Error', 'NEA Error', 'DATA', '0', '0', 0, 'CAMEL', TO_TIMESTAMP('2013-07-23 14:51:44:0','YYYY-MM-DD HH24:MI:SS:FF'), 103208, NULL, NULL, 'INFO');
INSERT INTO requirement_type(REQUIREMENT_ID, REQUIREMENT_DISPLAY, REQUIREMENT_DESCRIPTION, REQUIREMENT_GROUP, ATTACH_WITH_CLAIM, IS_FILM, NEA_IMAGE_CODE, SELECTION_USER_TYPE, CREATE_DATETIME, CREATE_EMPLOYEE_NUMBER, INACTIVE_DATETIME, INACTIVE_EMPLOYEE_NUMBER, REQUIREMENT_DISPLAY_GROUP)
  VALUES(57, 'Invalid Data', 'Invalid Data', 'DATA', '0', '0', 0, 'CAMEL', TO_TIMESTAMP('2013-07-23 14:52:00:0','YYYY-MM-DD HH24:MI:SS:FF'), 103208, NULL, NULL, 'INFO');
INSERT INTO requirement_type(REQUIREMENT_ID, REQUIREMENT_DISPLAY, REQUIREMENT_DESCRIPTION, REQUIREMENT_GROUP, ATTACH_WITH_CLAIM, IS_FILM, NEA_IMAGE_CODE, SELECTION_USER_TYPE, CREATE_DATETIME, CREATE_EMPLOYEE_NUMBER, INACTIVE_DATETIME, INACTIVE_EMPLOYEE_NUMBER, REQUIREMENT_DISPLAY_GROUP)
  VALUES(58, 'Incomplete Data', 'Incomplete Data', 'DATA', '0', '0', 0, 'CAMEL', TO_TIMESTAMP('2013-07-23 14:52:23:0','YYYY-MM-DD HH24:MI:SS:FF'), 103208, NULL, NULL, 'INFO');
INSERT INTO requirement_type(REQUIREMENT_ID, REQUIREMENT_DISPLAY, REQUIREMENT_DESCRIPTION, REQUIREMENT_GROUP, ATTACH_WITH_CLAIM, IS_FILM, NEA_IMAGE_CODE, SELECTION_USER_TYPE, CREATE_DATETIME, CREATE_EMPLOYEE_NUMBER, INACTIVE_DATETIME, INACTIVE_EMPLOYEE_NUMBER, REQUIREMENT_DISPLAY_GROUP)
  VALUES(59, 'EHG Warning', 'EHG Warning', 'DATA', '0', '0', 0, 'CAMEL', TO_TIMESTAMP('2013-09-11 14:00:50:0','YYYY-MM-DD HH24:MI:SS:FF'), 103208, NULL, NULL, 'INFO');
INSERT INTO requirement_type(REQUIREMENT_ID, REQUIREMENT_DISPLAY, REQUIREMENT_DESCRIPTION, REQUIREMENT_GROUP, ATTACH_WITH_CLAIM, IS_FILM, NEA_IMAGE_CODE, SELECTION_USER_TYPE, CREATE_DATETIME, CREATE_EMPLOYEE_NUMBER, INACTIVE_DATETIME, INACTIVE_EMPLOYEE_NUMBER, REQUIREMENT_DISPLAY_GROUP)
  VALUES(60, 'RCT Pre-Op PA', 'RCT Pre-Op PA', 'ASSET', '1', '0', 0, 'CBO', TO_TIMESTAMP('2014-01-14 16:47:04:0','YYYY-MM-DD HH24:MI:SS:FF'), 110840, NULL, NULL, 'IMAGES');
INSERT INTO requirement_type(REQUIREMENT_ID, REQUIREMENT_DISPLAY, REQUIREMENT_DESCRIPTION, REQUIREMENT_GROUP, ATTACH_WITH_CLAIM, IS_FILM, NEA_IMAGE_CODE, SELECTION_USER_TYPE, CREATE_DATETIME, CREATE_EMPLOYEE_NUMBER, INACTIVE_DATETIME, INACTIVE_EMPLOYEE_NUMBER, REQUIREMENT_DISPLAY_GROUP)
  VALUES(61, 'Bitewing', 'Bitewing', 'ASSET', '1', '0', 0, 'CBO', TO_TIMESTAMP('2014-01-14 16:47:04:0','YYYY-MM-DD HH24:MI:SS:FF'), 110840, NULL, NULL, 'IMAGES');
INSERT INTO requirement_type(REQUIREMENT_ID, REQUIREMENT_DISPLAY, REQUIREMENT_DESCRIPTION, REQUIREMENT_GROUP, ATTACH_WITH_CLAIM, IS_FILM, NEA_IMAGE_CODE, SELECTION_USER_TYPE, CREATE_DATETIME, CREATE_EMPLOYEE_NUMBER, INACTIVE_DATETIME, INACTIVE_EMPLOYEE_NUMBER, REQUIREMENT_DISPLAY_GROUP)
  VALUES(62, 'Med Ins Card', 'Medical Insurance Card ID', 'ASSET', '1', '0', 0, 'CBO', TO_TIMESTAMP('2014-01-14 16:47:04:0','YYYY-MM-DD HH24:MI:SS:FF'), 110840, NULL, NULL, 'INFO');
INSERT INTO requirement_type(REQUIREMENT_ID, REQUIREMENT_DISPLAY, REQUIREMENT_DESCRIPTION, REQUIREMENT_GROUP, ATTACH_WITH_CLAIM, IS_FILM, NEA_IMAGE_CODE, SELECTION_USER_TYPE, CREATE_DATETIME, CREATE_EMPLOYEE_NUMBER, INACTIVE_DATETIME, INACTIVE_EMPLOYEE_NUMBER, REQUIREMENT_DISPLAY_GROUP)
  VALUES(63, 'Seat X-ray', 'CC/BB Final/Seat X-ray', 'ASSET', '1', '0', 0, 'CBO', TO_TIMESTAMP('2014-01-14 16:47:04:0','YYYY-MM-DD HH24:MI:SS:FF'), 110840, NULL, NULL, 'IMAGES');
INSERT INTO requirement_type(REQUIREMENT_ID, REQUIREMENT_DISPLAY, REQUIREMENT_DESCRIPTION, REQUIREMENT_GROUP, ATTACH_WITH_CLAIM, IS_FILM, NEA_IMAGE_CODE, SELECTION_USER_TYPE, CREATE_DATETIME, CREATE_EMPLOYEE_NUMBER, INACTIVE_DATETIME, INACTIVE_EMPLOYEE_NUMBER, REQUIREMENT_DISPLAY_GROUP)
  VALUES(64, 'Cavity Clear', 'Cavity Clearance', 'ASSET', '1', '0', 0, 'CBO', TO_TIMESTAMP('2014-01-14 16:47:04:0','YYYY-MM-DD HH24:MI:SS:FF'), 110840, NULL, NULL, 'FORMS');
INSERT INTO requirement_type(REQUIREMENT_ID, REQUIREMENT_DISPLAY, REQUIREMENT_DESCRIPTION, REQUIREMENT_GROUP, ATTACH_WITH_CLAIM, IS_FILM, NEA_IMAGE_CODE, SELECTION_USER_TYPE, CREATE_DATETIME, CREATE_EMPLOYEE_NUMBER, INACTIVE_DATETIME, INACTIVE_EMPLOYEE_NUMBER, REQUIREMENT_DISPLAY_GROUP)
  VALUES(65, 'Proof of Guard', 'Proof of Guardianship', 'ASSET', '1', '0', 0, 'CBO', TO_TIMESTAMP('2014-01-14 16:47:04:0','YYYY-MM-DD HH24:MI:SS:FF'), 110840, NULL, NULL, 'INFO');
INSERT INTO requirement_type(REQUIREMENT_ID, REQUIREMENT_DISPLAY, REQUIREMENT_DESCRIPTION, REQUIREMENT_GROUP, ATTACH_WITH_CLAIM, IS_FILM, NEA_IMAGE_CODE, SELECTION_USER_TYPE, CREATE_DATETIME, CREATE_EMPLOYEE_NUMBER, INACTIVE_DATETIME, INACTIVE_EMPLOYEE_NUMBER, REQUIREMENT_DISPLAY_GROUP)
  VALUES(66, 'Proof Fin Resp', 'Proof of Financial Responsibility', 'ASSET', '1', '0', 0, 'CBO', TO_TIMESTAMP('2014-01-14 16:47:04:0','YYYY-MM-DD HH24:MI:SS:FF'), 110840, NULL, NULL, 'INFO');
INSERT INTO requirement_type(REQUIREMENT_ID, REQUIREMENT_DISPLAY, REQUIREMENT_DESCRIPTION, REQUIREMENT_GROUP, ATTACH_WITH_CLAIM, IS_FILM, NEA_IMAGE_CODE, SELECTION_USER_TYPE, CREATE_DATETIME, CREATE_EMPLOYEE_NUMBER, INACTIVE_DATETIME, INACTIVE_EMPLOYEE_NUMBER, REQUIREMENT_DISPLAY_GROUP)
  VALUES(67, 'Pt Recvd Ins Ck', 'Patient Received Insurance Check', 'ASSET', '1', '0', 0, 'CBO', TO_TIMESTAMP('2014-01-14 16:47:04:0','YYYY-MM-DD HH24:MI:SS:FF'), 110840, NULL, NULL, 'INFO');
INSERT INTO requirement_type(REQUIREMENT_ID, REQUIREMENT_DISPLAY, REQUIREMENT_DESCRIPTION, REQUIREMENT_GROUP, ATTACH_WITH_CLAIM, IS_FILM, NEA_IMAGE_CODE, SELECTION_USER_TYPE, CREATE_DATETIME, CREATE_EMPLOYEE_NUMBER, INACTIVE_DATETIME, INACTIVE_EMPLOYEE_NUMBER, REQUIREMENT_DISPLAY_GROUP)
  VALUES(68, 'Divorce Decree', 'Divorce Decree', 'ASSET', '1', '0', 0, 'CBO', TO_TIMESTAMP('2014-01-14 16:47:04:0','YYYY-MM-DD HH24:MI:SS:FF'), 110840, NULL, NULL, 'INFO');
INSERT INTO requirement_type(REQUIREMENT_ID, REQUIREMENT_DISPLAY, REQUIREMENT_DESCRIPTION, REQUIREMENT_GROUP, ATTACH_WITH_CLAIM, IS_FILM, NEA_IMAGE_CODE, SELECTION_USER_TYPE, CREATE_DATETIME, CREATE_EMPLOYEE_NUMBER, INACTIVE_DATETIME, INACTIVE_EMPLOYEE_NUMBER, REQUIREMENT_DISPLAY_GROUP)
  VALUES(69, 'COB/Other Cov', 'Coordination of Benefits / Other Coverage', 'ASSET', '1', '0', 0, 'CBO', TO_TIMESTAMP('2014-01-14 16:47:04:0','YYYY-MM-DD HH24:MI:SS:FF'), 110840, NULL, NULL, 'INFO');
INSERT INTO requirement_type(REQUIREMENT_ID, REQUIREMENT_DISPLAY, REQUIREMENT_DESCRIPTION, REQUIREMENT_GROUP, ATTACH_WITH_CLAIM, IS_FILM, NEA_IMAGE_CODE, SELECTION_USER_TYPE, CREATE_DATETIME, CREATE_EMPLOYEE_NUMBER, INACTIVE_DATETIME, INACTIVE_EMPLOYEE_NUMBER, REQUIREMENT_DISPLAY_GROUP)
  VALUES(70, 'Medical Release', 'Medical Release', 'ASSET', '1', '0', 0, 'CBO', TO_TIMESTAMP('2014-01-14 16:47:04:0','YYYY-MM-DD HH24:MI:SS:FF'), 110840, NULL, NULL, 'FORMS');
INSERT INTO requirement_type(REQUIREMENT_ID, REQUIREMENT_DISPLAY, REQUIREMENT_DESCRIPTION, REQUIREMENT_GROUP, ATTACH_WITH_CLAIM, IS_FILM, NEA_IMAGE_CODE, SELECTION_USER_TYPE, CREATE_DATETIME, CREATE_EMPLOYEE_NUMBER, INACTIVE_DATETIME, INACTIVE_EMPLOYEE_NUMBER, REQUIREMENT_DISPLAY_GROUP)
  VALUES(71, 'Dental Ready', 'Dental Readiness Form', 'ASSET', '1', '0', 0, 'CBO', TO_TIMESTAMP('2014-01-14 16:47:04:0','YYYY-MM-DD HH24:MI:SS:FF'), 110840, NULL, NULL, 'FORMS');
INSERT INTO requirement_type(REQUIREMENT_ID, REQUIREMENT_DISPLAY, REQUIREMENT_DESCRIPTION, REQUIREMENT_GROUP, ATTACH_WITH_CLAIM, IS_FILM, NEA_IMAGE_CODE, SELECTION_USER_TYPE, CREATE_DATETIME, CREATE_EMPLOYEE_NUMBER, INACTIVE_DATETIME, INACTIVE_EMPLOYEE_NUMBER, REQUIREMENT_DISPLAY_GROUP)
  VALUES(72, 'Missing Teeth', 'Missing Teeth', 'DATA', '1', '0', 0, 'CBO', TO_TIMESTAMP('2014-01-14 16:47:04:0','YYYY-MM-DD HH24:MI:SS:FF'), 110840, NULL, NULL, 'HISTORY');
INSERT INTO requirement_type(REQUIREMENT_ID, REQUIREMENT_DISPLAY, REQUIREMENT_DESCRIPTION, REQUIREMENT_GROUP, ATTACH_WITH_CLAIM, IS_FILM, NEA_IMAGE_CODE, SELECTION_USER_TYPE, CREATE_DATETIME, CREATE_EMPLOYEE_NUMBER, INACTIVE_DATETIME, INACTIVE_EMPLOYEE_NUMBER, REQUIREMENT_DISPLAY_GROUP)
  VALUES(73, 'Date of Prior XB', 'Date of Prior Extraction', 'DATA', '1', '0', 0, 'CBO', TO_TIMESTAMP('2014-01-14 16:47:04:0','YYYY-MM-DD HH24:MI:SS:FF'), 110840, NULL, NULL, 'HISTORY');
INSERT INTO requirement_type(REQUIREMENT_ID, REQUIREMENT_DISPLAY, REQUIREMENT_DESCRIPTION, REQUIREMENT_GROUP, ATTACH_WITH_CLAIM, IS_FILM, NEA_IMAGE_CODE, SELECTION_USER_TYPE, CREATE_DATETIME, CREATE_EMPLOYEE_NUMBER, INACTIVE_DATETIME, INACTIVE_EMPLOYEE_NUMBER, REQUIREMENT_DISPLAY_GROUP)
  VALUES(74, 'Prognosis', 'Prognosis', 'DATA', '1', '0', 0, 'CBO', TO_TIMESTAMP('2014-01-14 16:47:04:0','YYYY-MM-DD HH24:MI:SS:FF'), 110840, NULL, NULL, 'HISTORY');
INSERT INTO requirement_type(REQUIREMENT_ID, REQUIREMENT_DISPLAY, REQUIREMENT_DESCRIPTION, REQUIREMENT_GROUP, ATTACH_WITH_CLAIM, IS_FILM, NEA_IMAGE_CODE, SELECTION_USER_TYPE, CREATE_DATETIME, CREATE_EMPLOYEE_NUMBER, INACTIVE_DATETIME, INACTIVE_EMPLOYEE_NUMBER, REQUIREMENT_DISPLAY_GROUP)
  VALUES(75, 'Narr OS Surg XB', 'Narrative-OS- Surgical XB: Symptoms, Pathology, documentation of flap and suture', 'ASSET', '1', '0', 0, 'CBO', TO_TIMESTAMP('2014-01-14 16:47:04:0','YYYY-MM-DD HH24:MI:SS:FF'), 110840, NULL, NULL, 'NARRATIVE');
INSERT INTO requirement_type(REQUIREMENT_ID, REQUIREMENT_DISPLAY, REQUIREMENT_DESCRIPTION, REQUIREMENT_GROUP, ATTACH_WITH_CLAIM, IS_FILM, NEA_IMAGE_CODE, SELECTION_USER_TYPE, CREATE_DATETIME, CREATE_EMPLOYEE_NUMBER, INACTIVE_DATETIME, INACTIVE_EMPLOYEE_NUMBER, REQUIREMENT_DISPLAY_GROUP)
  VALUES(76, 'Narr OS Third', 'Narrative-OS-Third Molar: Symptoms, Pathology, Documentation of  periocoronitis, medical conditions, pain, type of extraction', 'ASSET', '1', '0', 0, 'CBO', TO_TIMESTAMP('2014-01-14 16:47:04:0','YYYY-MM-DD HH24:MI:SS:FF'), 110840, NULL, NULL, 'NARRATIVE');
INSERT INTO requirement_type(REQUIREMENT_ID, REQUIREMENT_DISPLAY, REQUIREMENT_DESCRIPTION, REQUIREMENT_GROUP, ATTACH_WITH_CLAIM, IS_FILM, NEA_IMAGE_CODE, SELECTION_USER_TYPE, CREATE_DATETIME, CREATE_EMPLOYEE_NUMBER, INACTIVE_DATETIME, INACTIVE_EMPLOYEE_NUMBER, REQUIREMENT_DISPLAY_GROUP)
  VALUES(77, 'Narr OS MultXB', 'Narrative- OS-Multiple XB /Surgical:  Symptoms, Pathology, Periocoronitis or Periapical disease present; medical condition present;  restoration/replacement, ', 'ASSET', '1', '0', 0, 'CBO', TO_TIMESTAMP('2014-01-14 16:47:04:0','YYYY-MM-DD HH24:MI:SS:FF'), 110840, NULL, NULL, 'NARRATIVE');
INSERT INTO requirement_type(REQUIREMENT_ID, REQUIREMENT_DISPLAY, REQUIREMENT_DESCRIPTION, REQUIREMENT_GROUP, ATTACH_WITH_CLAIM, IS_FILM, NEA_IMAGE_CODE, SELECTION_USER_TYPE, CREATE_DATETIME, CREATE_EMPLOYEE_NUMBER, INACTIVE_DATETIME, INACTIVE_EMPLOYEE_NUMBER, REQUIREMENT_DISPLAY_GROUP)
  VALUES(78, 'Narr Perio Ref', 'Narrative - Perio - Referral / Treatment: Presence of persistent infection, prior history, symptoms, prognosis, bone loss, apexification; medical conditions.  ', 'ASSET', '1', '0', 0, 'CBO', TO_TIMESTAMP('2014-01-14 16:47:04:0','YYYY-MM-DD HH24:MI:SS:FF'), 110840, NULL, NULL, 'NARRATIVE');
INSERT INTO requirement_type(REQUIREMENT_ID, REQUIREMENT_DISPLAY, REQUIREMENT_DESCRIPTION, REQUIREMENT_GROUP, ATTACH_WITH_CLAIM, IS_FILM, NEA_IMAGE_CODE, SELECTION_USER_TYPE, CREATE_DATETIME, CREATE_EMPLOYEE_NUMBER, INACTIVE_DATETIME, INACTIVE_EMPLOYEE_NUMBER, REQUIREMENT_DISPLAY_GROUP)
  VALUES(79, 'Narr Pr- CR Lng', 'Narrative- Perio -Crown Lengthening:  Documentation of prognosis, tooth structure, restoration; Root to Apex Ratio', 'ASSET', '1', '0', 0, 'CBO', TO_TIMESTAMP('2014-01-14 16:47:04:0','YYYY-MM-DD HH24:MI:SS:FF'), 110840, NULL, NULL, 'NARRATIVE');
INSERT INTO requirement_type(REQUIREMENT_ID, REQUIREMENT_DISPLAY, REQUIREMENT_DESCRIPTION, REQUIREMENT_GROUP, ATTACH_WITH_CLAIM, IS_FILM, NEA_IMAGE_CODE, SELECTION_USER_TYPE, CREATE_DATETIME, CREATE_EMPLOYEE_NUMBER, INACTIVE_DATETIME, INACTIVE_EMPLOYEE_NUMBER, REQUIREMENT_DISPLAY_GROUP)
  VALUES(80, 'Narr Pr-OSS', 'Narrative- Perio - Osseous: Symptoms, prognosis, generalized pocket depth; description of treatment performed or to be performed', 'ASSET', '1', '0', 0, 'CBO', TO_TIMESTAMP('2014-01-14 16:47:04:0','YYYY-MM-DD HH24:MI:SS:FF'), 110840, NULL, NULL, 'NARRATIVE');
INSERT INTO requirement_type(REQUIREMENT_ID, REQUIREMENT_DISPLAY, REQUIREMENT_DESCRIPTION, REQUIREMENT_GROUP, ATTACH_WITH_CLAIM, IS_FILM, NEA_IMAGE_CODE, SELECTION_USER_TYPE, CREATE_DATETIME, CREATE_EMPLOYEE_NUMBER, INACTIVE_DATETIME, INACTIVE_EMPLOYEE_NUMBER, REQUIREMENT_DISPLAY_GROUP)
  VALUES(81, 'Narr Endo RCT', 'Narrative-Endo - RCT- Symptoms, prognosis, documentation of caries or fracture present and approximation to pulpal area, documentation of breakdown of coronal structure, presence of pain and apical pathology', 'ASSET', '1', '0', 0, 'CBO', TO_TIMESTAMP('2014-01-14 16:47:04:0','YYYY-MM-DD HH24:MI:SS:FF'), 110840, NULL, NULL, 'NARRATIVE');
INSERT INTO requirement_type(REQUIREMENT_ID, REQUIREMENT_DISPLAY, REQUIREMENT_DESCRIPTION, REQUIREMENT_GROUP, ATTACH_WITH_CLAIM, IS_FILM, NEA_IMAGE_CODE, SELECTION_USER_TYPE, CREATE_DATETIME, CREATE_EMPLOYEE_NUMBER, INACTIVE_DATETIME, INACTIVE_EMPLOYEE_NUMBER, REQUIREMENT_DISPLAY_GROUP)
  VALUES(82, 'Narr Endo Obs', 'Narrative - Endo-Obstruction/Defect/Apexification: Documentation of pathway to achieve apical seal due to obstruction; bone support; Complex Root Structure', 'ASSET', '1', '0', 0, 'CBO', TO_TIMESTAMP('2014-01-14 16:47:04:0','YYYY-MM-DD HH24:MI:SS:FF'), 110840, NULL, NULL, 'NARRATIVE');
INSERT INTO requirement_type(REQUIREMENT_ID, REQUIREMENT_DISPLAY, REQUIREMENT_DESCRIPTION, REQUIREMENT_GROUP, ATTACH_WITH_CLAIM, IS_FILM, NEA_IMAGE_CODE, SELECTION_USER_TYPE, CREATE_DATETIME, CREATE_EMPLOYEE_NUMBER, INACTIVE_DATETIME, INACTIVE_EMPLOYEE_NUMBER, REQUIREMENT_DISPLAY_GROUP)
  VALUES(83, 'Narr Endo Ref', 'Narrative- Endo-Referral: Symptoms, prognosis, pathology, documentation of fracture/complex root structure/Incomplete apical form; lingering pain; inadequate existing RCT (underfill, overfill, fistula)', 'ASSET', '1', '0', 0, 'CBO', TO_TIMESTAMP('2014-01-14 16:47:04:0','YYYY-MM-DD HH24:MI:SS:FF'), 110840, NULL, NULL, 'NARRATIVE');
INSERT INTO requirement_type(REQUIREMENT_ID, REQUIREMENT_DISPLAY, REQUIREMENT_DESCRIPTION, REQUIREMENT_GROUP, ATTACH_WITH_CLAIM, IS_FILM, NEA_IMAGE_CODE, SELECTION_USER_TYPE, CREATE_DATETIME, CREATE_EMPLOYEE_NUMBER, INACTIVE_DATETIME, INACTIVE_EMPLOYEE_NUMBER, REQUIREMENT_DISPLAY_GROUP)
  VALUES(84, 'Narr Crown', 'Narrative - Crowns - Symptoms, pathology, prognosis; existing large falling filling or other large decay; documentation of loss over vertical dimension/visible horizontal cracks/gross decay/RCT', 'ASSET', '1', '0', 0, 'CBO', TO_TIMESTAMP('2014-01-14 16:47:04:0','YYYY-MM-DD HH24:MI:SS:FF'), 110840, NULL, NULL, 'NARRATIVE');
INSERT INTO requirement_type(REQUIREMENT_ID, REQUIREMENT_DISPLAY, REQUIREMENT_DESCRIPTION, REQUIREMENT_GROUP, ATTACH_WITH_CLAIM, IS_FILM, NEA_IMAGE_CODE, SELECTION_USER_TYPE, CREATE_DATETIME, CREATE_EMPLOYEE_NUMBER, INACTIVE_DATETIME, INACTIVE_EMPLOYEE_NUMBER, REQUIREMENT_DISPLAY_GROUP)
  VALUES(85, 'Narr Part/Dent', 'Narrative - Partial or Denture - Description of treatment and prognosis, documentation restoration will stabilize current condition; Existing missing teeth and status; Clasps; XB Dates', 'ASSET', '1', '0', 0, 'CBO', TO_TIMESTAMP('2014-01-14 16:47:04:0','YYYY-MM-DD HH24:MI:SS:FF'), 110840, NULL, NULL, 'NARRATIVE');
INSERT INTO requirement_type(REQUIREMENT_ID, REQUIREMENT_DISPLAY, REQUIREMENT_DESCRIPTION, REQUIREMENT_GROUP, ATTACH_WITH_CLAIM, IS_FILM, NEA_IMAGE_CODE, SELECTION_USER_TYPE, CREATE_DATETIME, CREATE_EMPLOYEE_NUMBER, INACTIVE_DATETIME, INACTIVE_EMPLOYEE_NUMBER, REQUIREMENT_DISPLAY_GROUP)
  VALUES(86, 'Narr Bridge', 'Narrative - BB - Symptoms, pathology, prognosis; documentation of bone support, perio status, and status of any existing RCT; XB Date', 'ASSET', '1', '0', 0, 'CBO', TO_TIMESTAMP('2014-01-14 16:47:04:0','YYYY-MM-DD HH24:MI:SS:FF'), 110840, NULL, NULL, 'NARRATIVE');
INSERT INTO requirement_type(REQUIREMENT_ID, REQUIREMENT_DISPLAY, REQUIREMENT_DESCRIPTION, REQUIREMENT_GROUP, ATTACH_WITH_CLAIM, IS_FILM, NEA_IMAGE_CODE, SELECTION_USER_TYPE, CREATE_DATETIME, CREATE_EMPLOYEE_NUMBER, INACTIVE_DATETIME, INACTIVE_EMPLOYEE_NUMBER, REQUIREMENT_DISPLAY_GROUP)
  VALUES(87, 'Narr Pedo Ref', 'Narrative - Pedo -Referral/Treatment - Diagnosis and prognosis.  Referral reasons -Specific description of behavior management and multiple dates of attempted service.  Documentation of moderate to severe condition requiring sedation or specialty services.  Documentation of medical condition.  ', 'ASSET', '1', '0', 0, 'CBO', TO_TIMESTAMP('2014-01-14 16:47:04:0','YYYY-MM-DD HH24:MI:SS:FF'), 110840, NULL, NULL, 'NARRATIVE');
INSERT INTO requirement_type(REQUIREMENT_ID, REQUIREMENT_DISPLAY, REQUIREMENT_DESCRIPTION, REQUIREMENT_GROUP, ATTACH_WITH_CLAIM, IS_FILM, NEA_IMAGE_CODE, SELECTION_USER_TYPE, CREATE_DATETIME, CREATE_EMPLOYEE_NUMBER, INACTIVE_DATETIME, INACTIVE_EMPLOYEE_NUMBER, REQUIREMENT_DISPLAY_GROUP)
  VALUES(88, 'Narr ER Exan', 'Narrative - Emergency Exam - Diagnosis, symptoms.  Documentation of one or all of the following- pain, trauma, infection. RX Given', 'ASSET', '1', '0', 0, 'CBO', TO_TIMESTAMP('2014-01-14 16:47:04:0','YYYY-MM-DD HH24:MI:SS:FF'), 110840, NULL, NULL, 'NARRATIVE');
INSERT INTO requirement_type(REQUIREMENT_ID, REQUIREMENT_DISPLAY, REQUIREMENT_DESCRIPTION, REQUIREMENT_GROUP, ATTACH_WITH_CLAIM, IS_FILM, NEA_IMAGE_CODE, SELECTION_USER_TYPE, CREATE_DATETIME, CREATE_EMPLOYEE_NUMBER, INACTIVE_DATETIME, INACTIVE_EMPLOYEE_NUMBER, REQUIREMENT_DISPLAY_GROUP)
  VALUES(89, 'Incorrect Addr', 'Incorrect Address', 'DATA', '1', '0', 0, 'CBO', TO_TIMESTAMP('2014-01-14 16:47:04:0','YYYY-MM-DD HH24:MI:SS:FF'), 110840, NULL, NULL, 'INFO');
INSERT INTO requirement_type(REQUIREMENT_ID, REQUIREMENT_DISPLAY, REQUIREMENT_DESCRIPTION, REQUIREMENT_GROUP, ATTACH_WITH_CLAIM, IS_FILM, NEA_IMAGE_CODE, SELECTION_USER_TYPE, CREATE_DATETIME, CREATE_EMPLOYEE_NUMBER, INACTIVE_DATETIME, INACTIVE_EMPLOYEE_NUMBER, REQUIREMENT_DISPLAY_GROUP)
  VALUES(90, 'Incorrect SSN', 'Incorrect SSN', 'DATA', '1', '0', 0, 'CBO', TO_TIMESTAMP('2014-01-14 16:47:04:0','YYYY-MM-DD HH24:MI:SS:FF'), 110840, NULL, NULL, 'INFO');
INSERT INTO requirement_type(REQUIREMENT_ID, REQUIREMENT_DISPLAY, REQUIREMENT_DESCRIPTION, REQUIREMENT_GROUP, ATTACH_WITH_CLAIM, IS_FILM, NEA_IMAGE_CODE, SELECTION_USER_TYPE, CREATE_DATETIME, CREATE_EMPLOYEE_NUMBER, INACTIVE_DATETIME, INACTIVE_EMPLOYEE_NUMBER, REQUIREMENT_DISPLAY_GROUP)
  VALUES(91, 'Invalid Relation', 'Incorrect Relationshoip', 'DATA', '1', '0', 0, 'CBO', TO_TIMESTAMP('2014-01-14 16:47:04:0','YYYY-MM-DD HH24:MI:SS:FF'), 110840, NULL, NULL, 'INFO');
INSERT INTO requirement_type(REQUIREMENT_ID, REQUIREMENT_DISPLAY, REQUIREMENT_DESCRIPTION, REQUIREMENT_GROUP, ATTACH_WITH_CLAIM, IS_FILM, NEA_IMAGE_CODE, SELECTION_USER_TYPE, CREATE_DATETIME, CREATE_EMPLOYEE_NUMBER, INACTIVE_DATETIME, INACTIVE_EMPLOYEE_NUMBER, REQUIREMENT_DISPLAY_GROUP)
  VALUES(92, 'Incorrect DOB', 'Incorrect Date of Birth', 'DATA', '1', '0', 0, 'CBO', TO_TIMESTAMP('2014-01-14 16:47:04:0','YYYY-MM-DD HH24:MI:SS:FF'), 110840, NULL, NULL, 'INFO');


DROP TABLE ATTACHMENT_REQUIREMENT;
CREATE TABLE ATTACHMENT_REQUIREMENT  (
	ATTACHMENT_REQUIREMENT_ID	NUMBER(15) NOT NULL,
	PLAN_TYPE                	VARCHAR2(2) NULL,
	INS_COMPANY_ID           	NUMBER(10) NULL,
	INSURANCE_PLAN           	NUMBER(10) NULL,
	PROVIDER_TAXONOMY        	VARCHAR2(10) NULL,
	PROCEDURE_CODE           	VARCHAR2(5) NULL,
	REQUIREMENT_ID           	NUMBER(5) NULL,
	VENDOR_TYPE              	VARCHAR2(25) NOT NULL,
	IS_ACTIVE                	CHAR(1) DEFAULT '1' NOT NULL,
	CREATE_EMPLOYEE_NUMBER   	NUMBER(6) NOT NULL,
	CREATE_DATETIME          	TIMESTAMP(6) NOT NULL,
	UPDATE_EMPLOYEE_NUMBER   	NUMBER(6) NULL,
	UPDATE_DATETIME          	TIMESTAMP(6) NULL
	);

DROP SEQUENCE ATTACHMENT_REQ_ID_SEQ;
CREATE SEQUENCE ATTACHMENT_REQ_ID_SEQ
	INCREMENT BY 1
	START WITH 2200
	NOMAXVALUE
	NOMINVALUE
	NOCYCLE;

/*** Insert new Attachment Requirement Records from Test. ***/
CREATE TABLE attachment_requirement  (
	ATTACHMENT_REQUIREMENT_ID	NUMBER(16) NOT NULL,
	PLAN_TYPE                	VARCHAR2(2) NULL,
	INS_COMPANY_ID           	NUMBER(11) NULL,
	INSURANCE_PLAN           	NUMBER(11) NULL,
	PROVIDER_TAXONOMY        	VARCHAR2(10) NULL,
	PROCEDURE_CODE           	VARCHAR2(5) NULL,
	REQUIREMENT_ID           	NUMBER(6) NULL,
	VENDOR_TYPE              	VARCHAR2(25) NOT NULL,
	IS_ACTIVE                	CHAR(1) NOT NULL,
	CREATE_EMPLOYEE_NUMBER   	NUMBER(7) NOT NULL,
	CREATE_DATETIME          	TIMESTAMP NOT NULL,
	UPDATE_EMPLOYEE_NUMBER   	NUMBER(7) NULL,
	UPDATE_DATETIME          	TIMESTAMP NULL
	);
INSERT INTO attachment_requirement(ATTACHMENT_REQUIREMENT_ID, PLAN_TYPE, INS_COMPANY_ID, INSURANCE_PLAN, PROVIDER_TAXONOMY, PROCEDURE_CODE, REQUIREMENT_ID, VENDOR_TYPE, IS_ACTIVE, CREATE_EMPLOYEE_NUMBER, CREATE_DATETIME, UPDATE_EMPLOYEE_NUMBER, UPDATE_DATETIME)
  VALUES(722, 'C4', NULL, NULL, '1223E0200X', NULL, 35, 'SBI', '1', 110840, TO_TIMESTAMP('2014-01-14 13:46:02:0','YYYY-MM-DD HH24:MI:SS:FF'), NULL, NULL);
INSERT INTO attachment_requirement(ATTACHMENT_REQUIREMENT_ID, PLAN_TYPE, INS_COMPANY_ID, INSURANCE_PLAN, PROVIDER_TAXONOMY, PROCEDURE_CODE, REQUIREMENT_ID, VENDOR_TYPE, IS_ACTIVE, CREATE_EMPLOYEE_NUMBER, CREATE_DATETIME, UPDATE_EMPLOYEE_NUMBER, UPDATE_DATETIME)
  VALUES(723, 'C4', NULL, NULL, '1223X0400X', NULL, 35, 'SBI', '1', 110840, TO_TIMESTAMP('2014-01-14 13:46:02:0','YYYY-MM-DD HH24:MI:SS:FF'), NULL, NULL);
INSERT INTO attachment_requirement(ATTACHMENT_REQUIREMENT_ID, PLAN_TYPE, INS_COMPANY_ID, INSURANCE_PLAN, PROVIDER_TAXONOMY, PROCEDURE_CODE, REQUIREMENT_ID, VENDOR_TYPE, IS_ACTIVE, CREATE_EMPLOYEE_NUMBER, CREATE_DATETIME, UPDATE_EMPLOYEE_NUMBER, UPDATE_DATETIME)
  VALUES(724, 'C4', NULL, NULL, '1223P0221X', NULL, 35, 'SBI', '1', 110840, TO_TIMESTAMP('2014-01-14 13:46:02:0','YYYY-MM-DD HH24:MI:SS:FF'), NULL, NULL);
INSERT INTO attachment_requirement(ATTACHMENT_REQUIREMENT_ID, PLAN_TYPE, INS_COMPANY_ID, INSURANCE_PLAN, PROVIDER_TAXONOMY, PROCEDURE_CODE, REQUIREMENT_ID, VENDOR_TYPE, IS_ACTIVE, CREATE_EMPLOYEE_NUMBER, CREATE_DATETIME, UPDATE_EMPLOYEE_NUMBER, UPDATE_DATETIME)
  VALUES(725, 'C4', NULL, NULL, '1223P0300X', NULL, 35, 'SBI', '1', 110840, TO_TIMESTAMP('2014-01-14 13:46:02:0','YYYY-MM-DD HH24:MI:SS:FF'), NULL, NULL);
INSERT INTO attachment_requirement(ATTACHMENT_REQUIREMENT_ID, PLAN_TYPE, INS_COMPANY_ID, INSURANCE_PLAN, PROVIDER_TAXONOMY, PROCEDURE_CODE, REQUIREMENT_ID, VENDOR_TYPE, IS_ACTIVE, CREATE_EMPLOYEE_NUMBER, CREATE_DATETIME, UPDATE_EMPLOYEE_NUMBER, UPDATE_DATETIME)
  VALUES(726, 'C4', NULL, NULL, '1223P0700X', NULL, 35, 'SBI', '1', 110840, TO_TIMESTAMP('2014-01-14 13:46:02:0','YYYY-MM-DD HH24:MI:SS:FF'), NULL, NULL);
INSERT INTO attachment_requirement(ATTACHMENT_REQUIREMENT_ID, PLAN_TYPE, INS_COMPANY_ID, INSURANCE_PLAN, PROVIDER_TAXONOMY, PROCEDURE_CODE, REQUIREMENT_ID, VENDOR_TYPE, IS_ACTIVE, CREATE_EMPLOYEE_NUMBER, CREATE_DATETIME, UPDATE_EMPLOYEE_NUMBER, UPDATE_DATETIME)
  VALUES(729, 'C4', NULL, NULL, '1223S0112X', NULL, 35, 'SBI', '1', 110840, TO_TIMESTAMP('2014-01-14 13:46:02:0','YYYY-MM-DD HH24:MI:SS:FF'), NULL, NULL);
INSERT INTO attachment_requirement(ATTACHMENT_REQUIREMENT_ID, PLAN_TYPE, INS_COMPANY_ID, INSURANCE_PLAN, PROVIDER_TAXONOMY, PROCEDURE_CODE, REQUIREMENT_ID, VENDOR_TYPE, IS_ACTIVE, CREATE_EMPLOYEE_NUMBER, CREATE_DATETIME, UPDATE_EMPLOYEE_NUMBER, UPDATE_DATETIME)
  VALUES(731, 'G4', NULL, NULL, '1223E0200X', NULL, 35, 'SBI', '1', 110840, TO_TIMESTAMP('2014-01-14 13:46:02:0','YYYY-MM-DD HH24:MI:SS:FF'), NULL, NULL);
INSERT INTO attachment_requirement(ATTACHMENT_REQUIREMENT_ID, PLAN_TYPE, INS_COMPANY_ID, INSURANCE_PLAN, PROVIDER_TAXONOMY, PROCEDURE_CODE, REQUIREMENT_ID, VENDOR_TYPE, IS_ACTIVE, CREATE_EMPLOYEE_NUMBER, CREATE_DATETIME, UPDATE_EMPLOYEE_NUMBER, UPDATE_DATETIME)
  VALUES(732, 'G4', NULL, NULL, '1223X0400X', NULL, 35, 'SBI', '1', 110840, TO_TIMESTAMP('2014-01-14 13:46:02:0','YYYY-MM-DD HH24:MI:SS:FF'), NULL, NULL);
INSERT INTO attachment_requirement(ATTACHMENT_REQUIREMENT_ID, PLAN_TYPE, INS_COMPANY_ID, INSURANCE_PLAN, PROVIDER_TAXONOMY, PROCEDURE_CODE, REQUIREMENT_ID, VENDOR_TYPE, IS_ACTIVE, CREATE_EMPLOYEE_NUMBER, CREATE_DATETIME, UPDATE_EMPLOYEE_NUMBER, UPDATE_DATETIME)
  VALUES(733, 'G4', NULL, NULL, '1223P0221X', NULL, 35, 'SBI', '1', 110840, TO_TIMESTAMP('2014-01-14 13:46:02:0','YYYY-MM-DD HH24:MI:SS:FF'), NULL, NULL);
INSERT INTO attachment_requirement(ATTACHMENT_REQUIREMENT_ID, PLAN_TYPE, INS_COMPANY_ID, INSURANCE_PLAN, PROVIDER_TAXONOMY, PROCEDURE_CODE, REQUIREMENT_ID, VENDOR_TYPE, IS_ACTIVE, CREATE_EMPLOYEE_NUMBER, CREATE_DATETIME, UPDATE_EMPLOYEE_NUMBER, UPDATE_DATETIME)
  VALUES(734, 'G4', NULL, NULL, '1223P0300X', NULL, 35, 'SBI', '1', 110840, TO_TIMESTAMP('2014-01-14 13:46:02:0','YYYY-MM-DD HH24:MI:SS:FF'), NULL, NULL);
INSERT INTO attachment_requirement(ATTACHMENT_REQUIREMENT_ID, PLAN_TYPE, INS_COMPANY_ID, INSURANCE_PLAN, PROVIDER_TAXONOMY, PROCEDURE_CODE, REQUIREMENT_ID, VENDOR_TYPE, IS_ACTIVE, CREATE_EMPLOYEE_NUMBER, CREATE_DATETIME, UPDATE_EMPLOYEE_NUMBER, UPDATE_DATETIME)
  VALUES(735, 'G4', NULL, NULL, '1223P0700X', NULL, 35, 'SBI', '1', 110840, TO_TIMESTAMP('2014-01-14 13:46:02:0','YYYY-MM-DD HH24:MI:SS:FF'), NULL, NULL);
INSERT INTO attachment_requirement(ATTACHMENT_REQUIREMENT_ID, PLAN_TYPE, INS_COMPANY_ID, INSURANCE_PLAN, PROVIDER_TAXONOMY, PROCEDURE_CODE, REQUIREMENT_ID, VENDOR_TYPE, IS_ACTIVE, CREATE_EMPLOYEE_NUMBER, CREATE_DATETIME, UPDATE_EMPLOYEE_NUMBER, UPDATE_DATETIME)
  VALUES(738, 'G4', NULL, NULL, '1223S0112X', NULL, 35, 'SBI', '1', 110840, TO_TIMESTAMP('2014-01-14 13:46:02:0','YYYY-MM-DD HH24:MI:SS:FF'), NULL, NULL);
INSERT INTO attachment_requirement(ATTACHMENT_REQUIREMENT_ID, PLAN_TYPE, INS_COMPANY_ID, INSURANCE_PLAN, PROVIDER_TAXONOMY, PROCEDURE_CODE, REQUIREMENT_ID, VENDOR_TYPE, IS_ACTIVE, CREATE_EMPLOYEE_NUMBER, CREATE_DATETIME, UPDATE_EMPLOYEE_NUMBER, UPDATE_DATETIME)
  VALUES(1430, NULL, NULL, 630, NULL, 'D1515', 18, 'SBI', '1', 110840, TO_TIMESTAMP('2014-01-14 16:25:21:0','YYYY-MM-DD HH24:MI:SS:FF'), NULL, NULL);
INSERT INTO attachment_requirement(ATTACHMENT_REQUIREMENT_ID, PLAN_TYPE, INS_COMPANY_ID, INSURANCE_PLAN, PROVIDER_TAXONOMY, PROCEDURE_CODE, REQUIREMENT_ID, VENDOR_TYPE, IS_ACTIVE, CREATE_EMPLOYEE_NUMBER, CREATE_DATETIME, UPDATE_EMPLOYEE_NUMBER, UPDATE_DATETIME)
  VALUES(1431, NULL, NULL, NULL, NULL, 'D0431', 26, 'SBI', '1', 110840, TO_TIMESTAMP('2014-01-14 16:25:21:0','YYYY-MM-DD HH24:MI:SS:FF'), NULL, NULL);
INSERT INTO attachment_requirement(ATTACHMENT_REQUIREMENT_ID, PLAN_TYPE, INS_COMPANY_ID, INSURANCE_PLAN, PROVIDER_TAXONOMY, PROCEDURE_CODE, REQUIREMENT_ID, VENDOR_TYPE, IS_ACTIVE, CREATE_EMPLOYEE_NUMBER, CREATE_DATETIME, UPDATE_EMPLOYEE_NUMBER, UPDATE_DATETIME)
  VALUES(1432, NULL, NULL, 630, NULL, 'D1510', 26, 'SBI', '1', 110840, TO_TIMESTAMP('2014-01-14 16:25:21:0','YYYY-MM-DD HH24:MI:SS:FF'), NULL, NULL);
INSERT INTO attachment_requirement(ATTACHMENT_REQUIREMENT_ID, PLAN_TYPE, INS_COMPANY_ID, INSURANCE_PLAN, PROVIDER_TAXONOMY, PROCEDURE_CODE, REQUIREMENT_ID, VENDOR_TYPE, IS_ACTIVE, CREATE_EMPLOYEE_NUMBER, CREATE_DATETIME, UPDATE_EMPLOYEE_NUMBER, UPDATE_DATETIME)
  VALUES(1433, NULL, NULL, 630, NULL, 'D1520', 18, 'SBI', '1', 110840, TO_TIMESTAMP('2014-01-14 16:25:21:0','YYYY-MM-DD HH24:MI:SS:FF'), NULL, NULL);
INSERT INTO attachment_requirement(ATTACHMENT_REQUIREMENT_ID, PLAN_TYPE, INS_COMPANY_ID, INSURANCE_PLAN, PROVIDER_TAXONOMY, PROCEDURE_CODE, REQUIREMENT_ID, VENDOR_TYPE, IS_ACTIVE, CREATE_EMPLOYEE_NUMBER, CREATE_DATETIME, UPDATE_EMPLOYEE_NUMBER, UPDATE_DATETIME)
  VALUES(1434, NULL, NULL, 630, NULL, 'D1525', 18, 'SBI', '1', 110840, TO_TIMESTAMP('2014-01-14 16:25:21:0','YYYY-MM-DD HH24:MI:SS:FF'), NULL, NULL);
INSERT INTO attachment_requirement(ATTACHMENT_REQUIREMENT_ID, PLAN_TYPE, INS_COMPANY_ID, INSURANCE_PLAN, PROVIDER_TAXONOMY, PROCEDURE_CODE, REQUIREMENT_ID, VENDOR_TYPE, IS_ACTIVE, CREATE_EMPLOYEE_NUMBER, CREATE_DATETIME, UPDATE_EMPLOYEE_NUMBER, UPDATE_DATETIME)
  VALUES(1435, NULL, NULL, NULL, NULL, 'D1550', 26, 'SBI', '1', 110840, TO_TIMESTAMP('2014-01-14 16:25:21:0','YYYY-MM-DD HH24:MI:SS:FF'), NULL, NULL);
INSERT INTO attachment_requirement(ATTACHMENT_REQUIREMENT_ID, PLAN_TYPE, INS_COMPANY_ID, INSURANCE_PLAN, PROVIDER_TAXONOMY, PROCEDURE_CODE, REQUIREMENT_ID, VENDOR_TYPE, IS_ACTIVE, CREATE_EMPLOYEE_NUMBER, CREATE_DATETIME, UPDATE_EMPLOYEE_NUMBER, UPDATE_DATETIME)
  VALUES(1436, NULL, NULL, 630, NULL, 'D2140', 18, 'SBI', '1', 110840, TO_TIMESTAMP('2014-01-14 16:25:21:0','YYYY-MM-DD HH24:MI:SS:FF'), NULL, NULL);
INSERT INTO attachment_requirement(ATTACHMENT_REQUIREMENT_ID, PLAN_TYPE, INS_COMPANY_ID, INSURANCE_PLAN, PROVIDER_TAXONOMY, PROCEDURE_CODE, REQUIREMENT_ID, VENDOR_TYPE, IS_ACTIVE, CREATE_EMPLOYEE_NUMBER, CREATE_DATETIME, UPDATE_EMPLOYEE_NUMBER, UPDATE_DATETIME)
  VALUES(1437, NULL, NULL, 630, NULL, 'D2141', 33, 'SBI', '1', 110840, TO_TIMESTAMP('2014-01-14 16:25:21:0','YYYY-MM-DD HH24:MI:SS:FF'), NULL, NULL);
INSERT INTO attachment_requirement(ATTACHMENT_REQUIREMENT_ID, PLAN_TYPE, INS_COMPANY_ID, INSURANCE_PLAN, PROVIDER_TAXONOMY, PROCEDURE_CODE, REQUIREMENT_ID, VENDOR_TYPE, IS_ACTIVE, CREATE_EMPLOYEE_NUMBER, CREATE_DATETIME, UPDATE_EMPLOYEE_NUMBER, UPDATE_DATETIME)
  VALUES(1438, NULL, NULL, 630, NULL, 'D2150', 18, 'SBI', '1', 110840, TO_TIMESTAMP('2014-01-14 16:25:21:0','YYYY-MM-DD HH24:MI:SS:FF'), NULL, NULL);
INSERT INTO attachment_requirement(ATTACHMENT_REQUIREMENT_ID, PLAN_TYPE, INS_COMPANY_ID, INSURANCE_PLAN, PROVIDER_TAXONOMY, PROCEDURE_CODE, REQUIREMENT_ID, VENDOR_TYPE, IS_ACTIVE, CREATE_EMPLOYEE_NUMBER, CREATE_DATETIME, UPDATE_EMPLOYEE_NUMBER, UPDATE_DATETIME)
  VALUES(1439, NULL, NULL, NULL, NULL, 'D2160', 18, 'SBI', '1', 110840, TO_TIMESTAMP('2014-01-14 16:25:21:0','YYYY-MM-DD HH24:MI:SS:FF'), NULL, NULL);
INSERT INTO attachment_requirement(ATTACHMENT_REQUIREMENT_ID, PLAN_TYPE, INS_COMPANY_ID, INSURANCE_PLAN, PROVIDER_TAXONOMY, PROCEDURE_CODE, REQUIREMENT_ID, VENDOR_TYPE, IS_ACTIVE, CREATE_EMPLOYEE_NUMBER, CREATE_DATETIME, UPDATE_EMPLOYEE_NUMBER, UPDATE_DATETIME)
  VALUES(1440, NULL, NULL, NULL, NULL, 'D2161', 18, 'SBI', '1', 110840, TO_TIMESTAMP('2014-01-14 16:25:21:0','YYYY-MM-DD HH24:MI:SS:FF'), NULL, NULL);
INSERT INTO attachment_requirement(ATTACHMENT_REQUIREMENT_ID, PLAN_TYPE, INS_COMPANY_ID, INSURANCE_PLAN, PROVIDER_TAXONOMY, PROCEDURE_CODE, REQUIREMENT_ID, VENDOR_TYPE, IS_ACTIVE, CREATE_EMPLOYEE_NUMBER, CREATE_DATETIME, UPDATE_EMPLOYEE_NUMBER, UPDATE_DATETIME)
  VALUES(1441, NULL, NULL, 630, NULL, 'D2330', 18, 'SBI', '1', 110840, TO_TIMESTAMP('2014-01-14 16:25:21:0','YYYY-MM-DD HH24:MI:SS:FF'), NULL, NULL);
INSERT INTO attachment_requirement(ATTACHMENT_REQUIREMENT_ID, PLAN_TYPE, INS_COMPANY_ID, INSURANCE_PLAN, PROVIDER_TAXONOMY, PROCEDURE_CODE, REQUIREMENT_ID, VENDOR_TYPE, IS_ACTIVE, CREATE_EMPLOYEE_NUMBER, CREATE_DATETIME, UPDATE_EMPLOYEE_NUMBER, UPDATE_DATETIME)
  VALUES(1442, NULL, NULL, 630, NULL, 'D2331', 18, 'SBI', '1', 110840, TO_TIMESTAMP('2014-01-14 16:25:21:0','YYYY-MM-DD HH24:MI:SS:FF'), NULL, NULL);
INSERT INTO attachment_requirement(ATTACHMENT_REQUIREMENT_ID, PLAN_TYPE, INS_COMPANY_ID, INSURANCE_PLAN, PROVIDER_TAXONOMY, PROCEDURE_CODE, REQUIREMENT_ID, VENDOR_TYPE, IS_ACTIVE, CREATE_EMPLOYEE_NUMBER, CREATE_DATETIME, UPDATE_EMPLOYEE_NUMBER, UPDATE_DATETIME)
  VALUES(1443, NULL, NULL, NULL, NULL, 'D2332', 18, 'SBI', '1', 110840, TO_TIMESTAMP('2014-01-14 16:25:21:0','YYYY-MM-DD HH24:MI:SS:FF'), NULL, NULL);
INSERT INTO attachment_requirement(ATTACHMENT_REQUIREMENT_ID, PLAN_TYPE, INS_COMPANY_ID, INSURANCE_PLAN, PROVIDER_TAXONOMY, PROCEDURE_CODE, REQUIREMENT_ID, VENDOR_TYPE, IS_ACTIVE, CREATE_EMPLOYEE_NUMBER, CREATE_DATETIME, UPDATE_EMPLOYEE_NUMBER, UPDATE_DATETIME)
  VALUES(1444, NULL, NULL, NULL, NULL, 'D2335', 18, 'SBI', '1', 110840, TO_TIMESTAMP('2014-01-14 16:25:21:0','YYYY-MM-DD HH24:MI:SS:FF'), NULL, NULL);
INSERT INTO attachment_requirement(ATTACHMENT_REQUIREMENT_ID, PLAN_TYPE, INS_COMPANY_ID, INSURANCE_PLAN, PROVIDER_TAXONOMY, PROCEDURE_CODE, REQUIREMENT_ID, VENDOR_TYPE, IS_ACTIVE, CREATE_EMPLOYEE_NUMBER, CREATE_DATETIME, UPDATE_EMPLOYEE_NUMBER, UPDATE_DATETIME)
  VALUES(1445, NULL, NULL, NULL, NULL, 'D2390', 26, 'SBI', '1', 110840, TO_TIMESTAMP('2014-01-14 16:25:21:0','YYYY-MM-DD HH24:MI:SS:FF'), NULL, NULL);
INSERT INTO attachment_requirement(ATTACHMENT_REQUIREMENT_ID, PLAN_TYPE, INS_COMPANY_ID, INSURANCE_PLAN, PROVIDER_TAXONOMY, PROCEDURE_CODE, REQUIREMENT_ID, VENDOR_TYPE, IS_ACTIVE, CREATE_EMPLOYEE_NUMBER, CREATE_DATETIME, UPDATE_EMPLOYEE_NUMBER, UPDATE_DATETIME)
  VALUES(1446, NULL, NULL, NULL, NULL, 'D2390', 18, 'SBI', '1', 110840, TO_TIMESTAMP('2014-01-14 16:25:21:0','YYYY-MM-DD HH24:MI:SS:FF'), NULL, NULL);
INSERT INTO attachment_requirement(ATTACHMENT_REQUIREMENT_ID, PLAN_TYPE, INS_COMPANY_ID, INSURANCE_PLAN, PROVIDER_TAXONOMY, PROCEDURE_CODE, REQUIREMENT_ID, VENDOR_TYPE, IS_ACTIVE, CREATE_EMPLOYEE_NUMBER, CREATE_DATETIME, UPDATE_EMPLOYEE_NUMBER, UPDATE_DATETIME)
  VALUES(1447, NULL, NULL, 630, NULL, 'D2391', 18, 'SBI', '1', 110840, TO_TIMESTAMP('2014-01-14 16:25:21:0','YYYY-MM-DD HH24:MI:SS:FF'), NULL, NULL);
INSERT INTO attachment_requirement(ATTACHMENT_REQUIREMENT_ID, PLAN_TYPE, INS_COMPANY_ID, INSURANCE_PLAN, PROVIDER_TAXONOMY, PROCEDURE_CODE, REQUIREMENT_ID, VENDOR_TYPE, IS_ACTIVE, CREATE_EMPLOYEE_NUMBER, CREATE_DATETIME, UPDATE_EMPLOYEE_NUMBER, UPDATE_DATETIME)
  VALUES(1448, NULL, NULL, 630, NULL, 'D2391', 33, 'SBI', '1', 110840, TO_TIMESTAMP('2014-01-14 16:25:21:0','YYYY-MM-DD HH24:MI:SS:FF'), NULL, NULL);
INSERT INTO attachment_requirement(ATTACHMENT_REQUIREMENT_ID, PLAN_TYPE, INS_COMPANY_ID, INSURANCE_PLAN, PROVIDER_TAXONOMY, PROCEDURE_CODE, REQUIREMENT_ID, VENDOR_TYPE, IS_ACTIVE, CREATE_EMPLOYEE_NUMBER, CREATE_DATETIME, UPDATE_EMPLOYEE_NUMBER, UPDATE_DATETIME)
  VALUES(1449, NULL, NULL, 630, NULL, 'D2392', 18, 'SBI', '1', 110840, TO_TIMESTAMP('2014-01-14 16:25:21:0','YYYY-MM-DD HH24:MI:SS:FF'), NULL, NULL);
INSERT INTO attachment_requirement(ATTACHMENT_REQUIREMENT_ID, PLAN_TYPE, INS_COMPANY_ID, INSURANCE_PLAN, PROVIDER_TAXONOMY, PROCEDURE_CODE, REQUIREMENT_ID, VENDOR_TYPE, IS_ACTIVE, CREATE_EMPLOYEE_NUMBER, CREATE_DATETIME, UPDATE_EMPLOYEE_NUMBER, UPDATE_DATETIME)
  VALUES(1450, NULL, NULL, NULL, NULL, 'D2393', 18, 'SBI', '1', 110840, TO_TIMESTAMP('2014-01-14 16:25:21:0','YYYY-MM-DD HH24:MI:SS:FF'), NULL, NULL);
INSERT INTO attachment_requirement(ATTACHMENT_REQUIREMENT_ID, PLAN_TYPE, INS_COMPANY_ID, INSURANCE_PLAN, PROVIDER_TAXONOMY, PROCEDURE_CODE, REQUIREMENT_ID, VENDOR_TYPE, IS_ACTIVE, CREATE_EMPLOYEE_NUMBER, CREATE_DATETIME, UPDATE_EMPLOYEE_NUMBER, UPDATE_DATETIME)
  VALUES(1451, NULL, NULL, NULL, NULL, 'D2394', 18, 'SBI', '1', 110840, TO_TIMESTAMP('2014-01-14 16:25:21:0','YYYY-MM-DD HH24:MI:SS:FF'), NULL, NULL);
INSERT INTO attachment_requirement(ATTACHMENT_REQUIREMENT_ID, PLAN_TYPE, INS_COMPANY_ID, INSURANCE_PLAN, PROVIDER_TAXONOMY, PROCEDURE_CODE, REQUIREMENT_ID, VENDOR_TYPE, IS_ACTIVE, CREATE_EMPLOYEE_NUMBER, CREATE_DATETIME, UPDATE_EMPLOYEE_NUMBER, UPDATE_DATETIME)
  VALUES(1452, NULL, NULL, NULL, NULL, 'D2542', 18, 'SBI', '1', 110840, TO_TIMESTAMP('2014-01-14 16:25:21:0','YYYY-MM-DD HH24:MI:SS:FF'), NULL, NULL);
INSERT INTO attachment_requirement(ATTACHMENT_REQUIREMENT_ID, PLAN_TYPE, INS_COMPANY_ID, INSURANCE_PLAN, PROVIDER_TAXONOMY, PROCEDURE_CODE, REQUIREMENT_ID, VENDOR_TYPE, IS_ACTIVE, CREATE_EMPLOYEE_NUMBER, CREATE_DATETIME, UPDATE_EMPLOYEE_NUMBER, UPDATE_DATETIME)
  VALUES(1453, NULL, NULL, NULL, NULL, 'D2542', 26, 'SBI', '1', 110840, TO_TIMESTAMP('2014-01-14 16:25:21:0','YYYY-MM-DD HH24:MI:SS:FF'), NULL, NULL);
INSERT INTO attachment_requirement(ATTACHMENT_REQUIREMENT_ID, PLAN_TYPE, INS_COMPANY_ID, INSURANCE_PLAN, PROVIDER_TAXONOMY, PROCEDURE_CODE, REQUIREMENT_ID, VENDOR_TYPE, IS_ACTIVE, CREATE_EMPLOYEE_NUMBER, CREATE_DATETIME, UPDATE_EMPLOYEE_NUMBER, UPDATE_DATETIME)
  VALUES(1454, NULL, NULL, NULL, NULL, 'D2543', 18, 'SBI', '1', 110840, TO_TIMESTAMP('2014-01-14 16:25:21:0','YYYY-MM-DD HH24:MI:SS:FF'), NULL, NULL);
INSERT INTO attachment_requirement(ATTACHMENT_REQUIREMENT_ID, PLAN_TYPE, INS_COMPANY_ID, INSURANCE_PLAN, PROVIDER_TAXONOMY, PROCEDURE_CODE, REQUIREMENT_ID, VENDOR_TYPE, IS_ACTIVE, CREATE_EMPLOYEE_NUMBER, CREATE_DATETIME, UPDATE_EMPLOYEE_NUMBER, UPDATE_DATETIME)
  VALUES(1455, NULL, NULL, NULL, NULL, 'D2544', 18, 'SBI', '1', 110840, TO_TIMESTAMP('2014-01-14 16:25:21:0','YYYY-MM-DD HH24:MI:SS:FF'), NULL, NULL);
INSERT INTO attachment_requirement(ATTACHMENT_REQUIREMENT_ID, PLAN_TYPE, INS_COMPANY_ID, INSURANCE_PLAN, PROVIDER_TAXONOMY, PROCEDURE_CODE, REQUIREMENT_ID, VENDOR_TYPE, IS_ACTIVE, CREATE_EMPLOYEE_NUMBER, CREATE_DATETIME, UPDATE_EMPLOYEE_NUMBER, UPDATE_DATETIME)
  VALUES(1456, NULL, NULL, NULL, NULL, 'D2544', 26, 'SBI', '1', 110840, TO_TIMESTAMP('2014-01-14 16:25:21:0','YYYY-MM-DD HH24:MI:SS:FF'), NULL, NULL);
INSERT INTO attachment_requirement(ATTACHMENT_REQUIREMENT_ID, PLAN_TYPE, INS_COMPANY_ID, INSURANCE_PLAN, PROVIDER_TAXONOMY, PROCEDURE_CODE, REQUIREMENT_ID, VENDOR_TYPE, IS_ACTIVE, CREATE_EMPLOYEE_NUMBER, CREATE_DATETIME, UPDATE_EMPLOYEE_NUMBER, UPDATE_DATETIME)
  VALUES(1457, NULL, NULL, NULL, NULL, 'D2610', 18, 'SBI', '1', 110840, TO_TIMESTAMP('2014-01-14 16:25:21:0','YYYY-MM-DD HH24:MI:SS:FF'), NULL, NULL);
INSERT INTO attachment_requirement(ATTACHMENT_REQUIREMENT_ID, PLAN_TYPE, INS_COMPANY_ID, INSURANCE_PLAN, PROVIDER_TAXONOMY, PROCEDURE_CODE, REQUIREMENT_ID, VENDOR_TYPE, IS_ACTIVE, CREATE_EMPLOYEE_NUMBER, CREATE_DATETIME, UPDATE_EMPLOYEE_NUMBER, UPDATE_DATETIME)
  VALUES(1458, NULL, NULL, NULL, NULL, 'D2610', 26, 'SBI', '1', 110840, TO_TIMESTAMP('2014-01-14 16:25:21:0','YYYY-MM-DD HH24:MI:SS:FF'), NULL, NULL);
INSERT INTO attachment_requirement(ATTACHMENT_REQUIREMENT_ID, PLAN_TYPE, INS_COMPANY_ID, INSURANCE_PLAN, PROVIDER_TAXONOMY, PROCEDURE_CODE, REQUIREMENT_ID, VENDOR_TYPE, IS_ACTIVE, CREATE_EMPLOYEE_NUMBER, CREATE_DATETIME, UPDATE_EMPLOYEE_NUMBER, UPDATE_DATETIME)
  VALUES(1459, NULL, NULL, NULL, NULL, 'D2620', 18, 'SBI', '1', 110840, TO_TIMESTAMP('2014-01-14 16:25:21:0','YYYY-MM-DD HH24:MI:SS:FF'), NULL, NULL);
INSERT INTO attachment_requirement(ATTACHMENT_REQUIREMENT_ID, PLAN_TYPE, INS_COMPANY_ID, INSURANCE_PLAN, PROVIDER_TAXONOMY, PROCEDURE_CODE, REQUIREMENT_ID, VENDOR_TYPE, IS_ACTIVE, CREATE_EMPLOYEE_NUMBER, CREATE_DATETIME, UPDATE_EMPLOYEE_NUMBER, UPDATE_DATETIME)
  VALUES(1460, NULL, NULL, NULL, NULL, 'D2620', 26, 'SBI', '1', 110840, TO_TIMESTAMP('2014-01-14 16:25:21:0','YYYY-MM-DD HH24:MI:SS:FF'), NULL, NULL);
INSERT INTO attachment_requirement(ATTACHMENT_REQUIREMENT_ID, PLAN_TYPE, INS_COMPANY_ID, INSURANCE_PLAN, PROVIDER_TAXONOMY, PROCEDURE_CODE, REQUIREMENT_ID, VENDOR_TYPE, IS_ACTIVE, CREATE_EMPLOYEE_NUMBER, CREATE_DATETIME, UPDATE_EMPLOYEE_NUMBER, UPDATE_DATETIME)
  VALUES(1461, NULL, NULL, NULL, NULL, 'D2630', 18, 'SBI', '1', 110840, TO_TIMESTAMP('2014-01-14 16:25:21:0','YYYY-MM-DD HH24:MI:SS:FF'), NULL, NULL);
INSERT INTO attachment_requirement(ATTACHMENT_REQUIREMENT_ID, PLAN_TYPE, INS_COMPANY_ID, INSURANCE_PLAN, PROVIDER_TAXONOMY, PROCEDURE_CODE, REQUIREMENT_ID, VENDOR_TYPE, IS_ACTIVE, CREATE_EMPLOYEE_NUMBER, CREATE_DATETIME, UPDATE_EMPLOYEE_NUMBER, UPDATE_DATETIME)
  VALUES(1462, NULL, NULL, NULL, NULL, 'D2642', 18, 'SBI', '1', 110840, TO_TIMESTAMP('2014-01-14 16:25:21:0','YYYY-MM-DD HH24:MI:SS:FF'), NULL, NULL);
INSERT INTO attachment_requirement(ATTACHMENT_REQUIREMENT_ID, PLAN_TYPE, INS_COMPANY_ID, INSURANCE_PLAN, PROVIDER_TAXONOMY, PROCEDURE_CODE, REQUIREMENT_ID, VENDOR_TYPE, IS_ACTIVE, CREATE_EMPLOYEE_NUMBER, CREATE_DATETIME, UPDATE_EMPLOYEE_NUMBER, UPDATE_DATETIME)
  VALUES(1463, NULL, NULL, NULL, NULL, 'D2642', 26, 'SBI', '1', 110840, TO_TIMESTAMP('2014-01-14 16:25:21:0','YYYY-MM-DD HH24:MI:SS:FF'), NULL, NULL);
INSERT INTO attachment_requirement(ATTACHMENT_REQUIREMENT_ID, PLAN_TYPE, INS_COMPANY_ID, INSURANCE_PLAN, PROVIDER_TAXONOMY, PROCEDURE_CODE, REQUIREMENT_ID, VENDOR_TYPE, IS_ACTIVE, CREATE_EMPLOYEE_NUMBER, CREATE_DATETIME, UPDATE_EMPLOYEE_NUMBER, UPDATE_DATETIME)
  VALUES(1464, NULL, NULL, NULL, NULL, 'D2643', 26, 'SBI', '1', 110840, TO_TIMESTAMP('2014-01-14 16:25:21:0','YYYY-MM-DD HH24:MI:SS:FF'), NULL, NULL);
INSERT INTO attachment_requirement(ATTACHMENT_REQUIREMENT_ID, PLAN_TYPE, INS_COMPANY_ID, INSURANCE_PLAN, PROVIDER_TAXONOMY, PROCEDURE_CODE, REQUIREMENT_ID, VENDOR_TYPE, IS_ACTIVE, CREATE_EMPLOYEE_NUMBER, CREATE_DATETIME, UPDATE_EMPLOYEE_NUMBER, UPDATE_DATETIME)
  VALUES(1465, NULL, NULL, NULL, NULL, 'D2643', 61, 'SBI', '1', 110840, TO_TIMESTAMP('2014-01-14 16:25:21:0','YYYY-MM-DD HH24:MI:SS:FF'), NULL, NULL);
INSERT INTO attachment_requirement(ATTACHMENT_REQUIREMENT_ID, PLAN_TYPE, INS_COMPANY_ID, INSURANCE_PLAN, PROVIDER_TAXONOMY, PROCEDURE_CODE, REQUIREMENT_ID, VENDOR_TYPE, IS_ACTIVE, CREATE_EMPLOYEE_NUMBER, CREATE_DATETIME, UPDATE_EMPLOYEE_NUMBER, UPDATE_DATETIME)
  VALUES(1466, NULL, NULL, NULL, NULL, 'D2644', 26, 'SBI', '1', 110840, TO_TIMESTAMP('2014-01-14 16:25:21:0','YYYY-MM-DD HH24:MI:SS:FF'), NULL, NULL);
INSERT INTO attachment_requirement(ATTACHMENT_REQUIREMENT_ID, PLAN_TYPE, INS_COMPANY_ID, INSURANCE_PLAN, PROVIDER_TAXONOMY, PROCEDURE_CODE, REQUIREMENT_ID, VENDOR_TYPE, IS_ACTIVE, CREATE_EMPLOYEE_NUMBER, CREATE_DATETIME, UPDATE_EMPLOYEE_NUMBER, UPDATE_DATETIME)
  VALUES(1467, NULL, NULL, NULL, NULL, 'D2650', 18, 'SBI', '1', 110840, TO_TIMESTAMP('2014-01-14 16:25:21:0','YYYY-MM-DD HH24:MI:SS:FF'), NULL, NULL);
INSERT INTO attachment_requirement(ATTACHMENT_REQUIREMENT_ID, PLAN_TYPE, INS_COMPANY_ID, INSURANCE_PLAN, PROVIDER_TAXONOMY, PROCEDURE_CODE, REQUIREMENT_ID, VENDOR_TYPE, IS_ACTIVE, CREATE_EMPLOYEE_NUMBER, CREATE_DATETIME, UPDATE_EMPLOYEE_NUMBER, UPDATE_DATETIME)
  VALUES(1468, NULL, NULL, NULL, NULL, 'D2650', 26, 'SBI', '1', 110840, TO_TIMESTAMP('2014-01-14 16:25:21:0','YYYY-MM-DD HH24:MI:SS:FF'), NULL, NULL);
INSERT INTO attachment_requirement(ATTACHMENT_REQUIREMENT_ID, PLAN_TYPE, INS_COMPANY_ID, INSURANCE_PLAN, PROVIDER_TAXONOMY, PROCEDURE_CODE, REQUIREMENT_ID, VENDOR_TYPE, IS_ACTIVE, CREATE_EMPLOYEE_NUMBER, CREATE_DATETIME, UPDATE_EMPLOYEE_NUMBER, UPDATE_DATETIME)
  VALUES(1469, NULL, NULL, NULL, NULL, 'D2650', 31, 'SBI', '1', 110840, TO_TIMESTAMP('2014-01-14 16:25:21:0','YYYY-MM-DD HH24:MI:SS:FF'), NULL, NULL);
INSERT INTO attachment_requirement(ATTACHMENT_REQUIREMENT_ID, PLAN_TYPE, INS_COMPANY_ID, INSURANCE_PLAN, PROVIDER_TAXONOMY, PROCEDURE_CODE, REQUIREMENT_ID, VENDOR_TYPE, IS_ACTIVE, CREATE_EMPLOYEE_NUMBER, CREATE_DATETIME, UPDATE_EMPLOYEE_NUMBER, UPDATE_DATETIME)
  VALUES(1470, NULL, NULL, NULL, NULL, 'D2651', 26, 'SBI', '1', 110840, TO_TIMESTAMP('2014-01-14 16:25:21:0','YYYY-MM-DD HH24:MI:SS:FF'), NULL, NULL);
INSERT INTO attachment_requirement(ATTACHMENT_REQUIREMENT_ID, PLAN_TYPE, INS_COMPANY_ID, INSURANCE_PLAN, PROVIDER_TAXONOMY, PROCEDURE_CODE, REQUIREMENT_ID, VENDOR_TYPE, IS_ACTIVE, CREATE_EMPLOYEE_NUMBER, CREATE_DATETIME, UPDATE_EMPLOYEE_NUMBER, UPDATE_DATETIME)
  VALUES(1471, NULL, NULL, NULL, NULL, 'D2651', 31, 'SBI', '1', 110840, TO_TIMESTAMP('2014-01-14 16:25:21:0','YYYY-MM-DD HH24:MI:SS:FF'), NULL, NULL);
INSERT INTO attachment_requirement(ATTACHMENT_REQUIREMENT_ID, PLAN_TYPE, INS_COMPANY_ID, INSURANCE_PLAN, PROVIDER_TAXONOMY, PROCEDURE_CODE, REQUIREMENT_ID, VENDOR_TYPE, IS_ACTIVE, CREATE_EMPLOYEE_NUMBER, CREATE_DATETIME, UPDATE_EMPLOYEE_NUMBER, UPDATE_DATETIME)
  VALUES(1472, NULL, NULL, NULL, NULL, 'D2651', 61, 'SBI', '1', 110840, TO_TIMESTAMP('2014-01-14 16:25:21:0','YYYY-MM-DD HH24:MI:SS:FF'), NULL, NULL);
INSERT INTO attachment_requirement(ATTACHMENT_REQUIREMENT_ID, PLAN_TYPE, INS_COMPANY_ID, INSURANCE_PLAN, PROVIDER_TAXONOMY, PROCEDURE_CODE, REQUIREMENT_ID, VENDOR_TYPE, IS_ACTIVE, CREATE_EMPLOYEE_NUMBER, CREATE_DATETIME, UPDATE_EMPLOYEE_NUMBER, UPDATE_DATETIME)
  VALUES(1473, NULL, NULL, NULL, NULL, 'D2652', 26, 'SBI', '1', 110840, TO_TIMESTAMP('2014-01-14 16:25:21:0','YYYY-MM-DD HH24:MI:SS:FF'), NULL, NULL);
INSERT INTO attachment_requirement(ATTACHMENT_REQUIREMENT_ID, PLAN_TYPE, INS_COMPANY_ID, INSURANCE_PLAN, PROVIDER_TAXONOMY, PROCEDURE_CODE, REQUIREMENT_ID, VENDOR_TYPE, IS_ACTIVE, CREATE_EMPLOYEE_NUMBER, CREATE_DATETIME, UPDATE_EMPLOYEE_NUMBER, UPDATE_DATETIME)
  VALUES(1474, NULL, NULL, NULL, NULL, 'D2652', 31, 'SBI', '1', 110840, TO_TIMESTAMP('2014-01-14 16:25:21:0','YYYY-MM-DD HH24:MI:SS:FF'), NULL, NULL);
INSERT INTO attachment_requirement(ATTACHMENT_REQUIREMENT_ID, PLAN_TYPE, INS_COMPANY_ID, INSURANCE_PLAN, PROVIDER_TAXONOMY, PROCEDURE_CODE, REQUIREMENT_ID, VENDOR_TYPE, IS_ACTIVE, CREATE_EMPLOYEE_NUMBER, CREATE_DATETIME, UPDATE_EMPLOYEE_NUMBER, UPDATE_DATETIME)
  VALUES(1475, NULL, NULL, NULL, NULL, 'D2652', 61, 'SBI', '1', 110840, TO_TIMESTAMP('2014-01-14 16:25:21:0','YYYY-MM-DD HH24:MI:SS:FF'), NULL, NULL);
INSERT INTO attachment_requirement(ATTACHMENT_REQUIREMENT_ID, PLAN_TYPE, INS_COMPANY_ID, INSURANCE_PLAN, PROVIDER_TAXONOMY, PROCEDURE_CODE, REQUIREMENT_ID, VENDOR_TYPE, IS_ACTIVE, CREATE_EMPLOYEE_NUMBER, CREATE_DATETIME, UPDATE_EMPLOYEE_NUMBER, UPDATE_DATETIME)
  VALUES(1476, NULL, NULL, NULL, NULL, 'D2662', 26, 'SBI', '1', 110840, TO_TIMESTAMP('2014-01-14 16:25:21:0','YYYY-MM-DD HH24:MI:SS:FF'), NULL, NULL);
INSERT INTO attachment_requirement(ATTACHMENT_REQUIREMENT_ID, PLAN_TYPE, INS_COMPANY_ID, INSURANCE_PLAN, PROVIDER_TAXONOMY, PROCEDURE_CODE, REQUIREMENT_ID, VENDOR_TYPE, IS_ACTIVE, CREATE_EMPLOYEE_NUMBER, CREATE_DATETIME, UPDATE_EMPLOYEE_NUMBER, UPDATE_DATETIME)
  VALUES(1477, NULL, NULL, NULL, NULL, 'D2662', 31, 'SBI', '1', 110840, TO_TIMESTAMP('2014-01-14 16:25:21:0','YYYY-MM-DD HH24:MI:SS:FF'), NULL, NULL);
INSERT INTO attachment_requirement(ATTACHMENT_REQUIREMENT_ID, PLAN_TYPE, INS_COMPANY_ID, INSURANCE_PLAN, PROVIDER_TAXONOMY, PROCEDURE_CODE, REQUIREMENT_ID, VENDOR_TYPE, IS_ACTIVE, CREATE_EMPLOYEE_NUMBER, CREATE_DATETIME, UPDATE_EMPLOYEE_NUMBER, UPDATE_DATETIME)
  VALUES(1478, NULL, NULL, NULL, NULL, 'D2662', 61, 'SBI', '1', 110840, TO_TIMESTAMP('2014-01-14 16:25:21:0','YYYY-MM-DD HH24:MI:SS:FF'), NULL, NULL);
INSERT INTO attachment_requirement(ATTACHMENT_REQUIREMENT_ID, PLAN_TYPE, INS_COMPANY_ID, INSURANCE_PLAN, PROVIDER_TAXONOMY, PROCEDURE_CODE, REQUIREMENT_ID, VENDOR_TYPE, IS_ACTIVE, CREATE_EMPLOYEE_NUMBER, CREATE_DATETIME, UPDATE_EMPLOYEE_NUMBER, UPDATE_DATETIME)
  VALUES(1479, NULL, NULL, NULL, NULL, 'D2663', 26, 'SBI', '1', 110840, TO_TIMESTAMP('2014-01-14 16:25:21:0','YYYY-MM-DD HH24:MI:SS:FF'), NULL, NULL);
INSERT INTO attachment_requirement(ATTACHMENT_REQUIREMENT_ID, PLAN_TYPE, INS_COMPANY_ID, INSURANCE_PLAN, PROVIDER_TAXONOMY, PROCEDURE_CODE, REQUIREMENT_ID, VENDOR_TYPE, IS_ACTIVE, CREATE_EMPLOYEE_NUMBER, CREATE_DATETIME, UPDATE_EMPLOYEE_NUMBER, UPDATE_DATETIME)
  VALUES(1480, NULL, NULL, NULL, NULL, 'D2663', 31, 'SBI', '1', 110840, TO_TIMESTAMP('2014-01-14 16:25:21:0','YYYY-MM-DD HH24:MI:SS:FF'), NULL, NULL);
INSERT INTO attachment_requirement(ATTACHMENT_REQUIREMENT_ID, PLAN_TYPE, INS_COMPANY_ID, INSURANCE_PLAN, PROVIDER_TAXONOMY, PROCEDURE_CODE, REQUIREMENT_ID, VENDOR_TYPE, IS_ACTIVE, CREATE_EMPLOYEE_NUMBER, CREATE_DATETIME, UPDATE_EMPLOYEE_NUMBER, UPDATE_DATETIME)
  VALUES(1481, NULL, NULL, NULL, NULL, 'D2663', 61, 'SBI', '1', 110840, TO_TIMESTAMP('2014-01-14 16:25:21:0','YYYY-MM-DD HH24:MI:SS:FF'), NULL, NULL);
INSERT INTO attachment_requirement(ATTACHMENT_REQUIREMENT_ID, PLAN_TYPE, INS_COMPANY_ID, INSURANCE_PLAN, PROVIDER_TAXONOMY, PROCEDURE_CODE, REQUIREMENT_ID, VENDOR_TYPE, IS_ACTIVE, CREATE_EMPLOYEE_NUMBER, CREATE_DATETIME, UPDATE_EMPLOYEE_NUMBER, UPDATE_DATETIME)
  VALUES(1482, NULL, NULL, NULL, NULL, 'D2664', 26, 'SBI', '1', 110840, TO_TIMESTAMP('2014-01-14 16:25:21:0','YYYY-MM-DD HH24:MI:SS:FF'), NULL, NULL);
INSERT INTO attachment_requirement(ATTACHMENT_REQUIREMENT_ID, PLAN_TYPE, INS_COMPANY_ID, INSURANCE_PLAN, PROVIDER_TAXONOMY, PROCEDURE_CODE, REQUIREMENT_ID, VENDOR_TYPE, IS_ACTIVE, CREATE_EMPLOYEE_NUMBER, CREATE_DATETIME, UPDATE_EMPLOYEE_NUMBER, UPDATE_DATETIME)
  VALUES(1483, NULL, NULL, NULL, NULL, 'D2664', 31, 'SBI', '1', 110840, TO_TIMESTAMP('2014-01-14 16:25:21:0','YYYY-MM-DD HH24:MI:SS:FF'), NULL, NULL);
INSERT INTO attachment_requirement(ATTACHMENT_REQUIREMENT_ID, PLAN_TYPE, INS_COMPANY_ID, INSURANCE_PLAN, PROVIDER_TAXONOMY, PROCEDURE_CODE, REQUIREMENT_ID, VENDOR_TYPE, IS_ACTIVE, CREATE_EMPLOYEE_NUMBER, CREATE_DATETIME, UPDATE_EMPLOYEE_NUMBER, UPDATE_DATETIME)
  VALUES(1484, NULL, NULL, NULL, NULL, 'D2664', 61, 'SBI', '1', 110840, TO_TIMESTAMP('2014-01-14 16:25:21:0','YYYY-MM-DD HH24:MI:SS:FF'), NULL, NULL);
INSERT INTO attachment_requirement(ATTACHMENT_REQUIREMENT_ID, PLAN_TYPE, INS_COMPANY_ID, INSURANCE_PLAN, PROVIDER_TAXONOMY, PROCEDURE_CODE, REQUIREMENT_ID, VENDOR_TYPE, IS_ACTIVE, CREATE_EMPLOYEE_NUMBER, CREATE_DATETIME, UPDATE_EMPLOYEE_NUMBER, UPDATE_DATETIME)
  VALUES(1485, NULL, NULL, NULL, NULL, 'D2710', 26, 'SBI', '1', 110840, TO_TIMESTAMP('2014-01-14 16:25:21:0','YYYY-MM-DD HH24:MI:SS:FF'), NULL, NULL);
INSERT INTO attachment_requirement(ATTACHMENT_REQUIREMENT_ID, PLAN_TYPE, INS_COMPANY_ID, INSURANCE_PLAN, PROVIDER_TAXONOMY, PROCEDURE_CODE, REQUIREMENT_ID, VENDOR_TYPE, IS_ACTIVE, CREATE_EMPLOYEE_NUMBER, CREATE_DATETIME, UPDATE_EMPLOYEE_NUMBER, UPDATE_DATETIME)
  VALUES(1486, NULL, NULL, NULL, NULL, 'D2710', 18, 'SBI', '1', 110840, TO_TIMESTAMP('2014-01-14 16:25:21:0','YYYY-MM-DD HH24:MI:SS:FF'), NULL, NULL);
INSERT INTO attachment_requirement(ATTACHMENT_REQUIREMENT_ID, PLAN_TYPE, INS_COMPANY_ID, INSURANCE_PLAN, PROVIDER_TAXONOMY, PROCEDURE_CODE, REQUIREMENT_ID, VENDOR_TYPE, IS_ACTIVE, CREATE_EMPLOYEE_NUMBER, CREATE_DATETIME, UPDATE_EMPLOYEE_NUMBER, UPDATE_DATETIME)
  VALUES(1488, NULL, NULL, NULL, NULL, 'D2712', 2, 'SBI', '1', 110840, TO_TIMESTAMP('2014-01-14 16:25:21:0','YYYY-MM-DD HH24:MI:SS:FF'), NULL, NULL);
INSERT INTO attachment_requirement(ATTACHMENT_REQUIREMENT_ID, PLAN_TYPE, INS_COMPANY_ID, INSURANCE_PLAN, PROVIDER_TAXONOMY, PROCEDURE_CODE, REQUIREMENT_ID, VENDOR_TYPE, IS_ACTIVE, CREATE_EMPLOYEE_NUMBER, CREATE_DATETIME, UPDATE_EMPLOYEE_NUMBER, UPDATE_DATETIME)
  VALUES(1489, NULL, NULL, NULL, NULL, 'D2720', 2, 'SBI', '1', 110840, TO_TIMESTAMP('2014-01-14 16:25:21:0','YYYY-MM-DD HH24:MI:SS:FF'), NULL, NULL);
INSERT INTO attachment_requirement(ATTACHMENT_REQUIREMENT_ID, PLAN_TYPE, INS_COMPANY_ID, INSURANCE_PLAN, PROVIDER_TAXONOMY, PROCEDURE_CODE, REQUIREMENT_ID, VENDOR_TYPE, IS_ACTIVE, CREATE_EMPLOYEE_NUMBER, CREATE_DATETIME, UPDATE_EMPLOYEE_NUMBER, UPDATE_DATETIME)
  VALUES(1490, NULL, NULL, NULL, NULL, 'D2721', 2, 'SBI', '1', 110840, TO_TIMESTAMP('2014-01-14 16:25:21:0','YYYY-MM-DD HH24:MI:SS:FF'), NULL, NULL);
INSERT INTO attachment_requirement(ATTACHMENT_REQUIREMENT_ID, PLAN_TYPE, INS_COMPANY_ID, INSURANCE_PLAN, PROVIDER_TAXONOMY, PROCEDURE_CODE, REQUIREMENT_ID, VENDOR_TYPE, IS_ACTIVE, CREATE_EMPLOYEE_NUMBER, CREATE_DATETIME, UPDATE_EMPLOYEE_NUMBER, UPDATE_DATETIME)
  VALUES(1491, NULL, NULL, NULL, NULL, 'D2722', 1, 'SBI', '1', 110840, TO_TIMESTAMP('2014-01-14 16:25:21:0','YYYY-MM-DD HH24:MI:SS:FF'), NULL, NULL);
INSERT INTO attachment_requirement(ATTACHMENT_REQUIREMENT_ID, PLAN_TYPE, INS_COMPANY_ID, INSURANCE_PLAN, PROVIDER_TAXONOMY, PROCEDURE_CODE, REQUIREMENT_ID, VENDOR_TYPE, IS_ACTIVE, CREATE_EMPLOYEE_NUMBER, CREATE_DATETIME, UPDATE_EMPLOYEE_NUMBER, UPDATE_DATETIME)
  VALUES(1492, NULL, NULL, NULL, NULL, 'D2722', 32, 'SBI', '1', 110840, TO_TIMESTAMP('2014-01-14 16:25:21:0','YYYY-MM-DD HH24:MI:SS:FF'), NULL, NULL);
INSERT INTO attachment_requirement(ATTACHMENT_REQUIREMENT_ID, PLAN_TYPE, INS_COMPANY_ID, INSURANCE_PLAN, PROVIDER_TAXONOMY, PROCEDURE_CODE, REQUIREMENT_ID, VENDOR_TYPE, IS_ACTIVE, CREATE_EMPLOYEE_NUMBER, CREATE_DATETIME, UPDATE_EMPLOYEE_NUMBER, UPDATE_DATETIME)
  VALUES(1493, NULL, NULL, NULL, NULL, 'D2740', 26, 'SBI', '1', 110840, TO_TIMESTAMP('2014-01-14 16:25:21:0','YYYY-MM-DD HH24:MI:SS:FF'), NULL, NULL);
INSERT INTO attachment_requirement(ATTACHMENT_REQUIREMENT_ID, PLAN_TYPE, INS_COMPANY_ID, INSURANCE_PLAN, PROVIDER_TAXONOMY, PROCEDURE_CODE, REQUIREMENT_ID, VENDOR_TYPE, IS_ACTIVE, CREATE_EMPLOYEE_NUMBER, CREATE_DATETIME, UPDATE_EMPLOYEE_NUMBER, UPDATE_DATETIME)
  VALUES(1494, NULL, NULL, NULL, NULL, 'D2740', 18, 'SBI', '1', 110840, TO_TIMESTAMP('2014-01-14 16:25:21:0','YYYY-MM-DD HH24:MI:SS:FF'), NULL, NULL);
INSERT INTO attachment_requirement(ATTACHMENT_REQUIREMENT_ID, PLAN_TYPE, INS_COMPANY_ID, INSURANCE_PLAN, PROVIDER_TAXONOMY, PROCEDURE_CODE, REQUIREMENT_ID, VENDOR_TYPE, IS_ACTIVE, CREATE_EMPLOYEE_NUMBER, CREATE_DATETIME, UPDATE_EMPLOYEE_NUMBER, UPDATE_DATETIME)
  VALUES(1497, NULL, NULL, NULL, NULL, 'D2750', 26, 'SBI', '1', 110840, TO_TIMESTAMP('2014-01-14 16:25:21:0','YYYY-MM-DD HH24:MI:SS:FF'), NULL, NULL);
INSERT INTO attachment_requirement(ATTACHMENT_REQUIREMENT_ID, PLAN_TYPE, INS_COMPANY_ID, INSURANCE_PLAN, PROVIDER_TAXONOMY, PROCEDURE_CODE, REQUIREMENT_ID, VENDOR_TYPE, IS_ACTIVE, CREATE_EMPLOYEE_NUMBER, CREATE_DATETIME, UPDATE_EMPLOYEE_NUMBER, UPDATE_DATETIME)
  VALUES(1498, NULL, NULL, NULL, NULL, 'D2750', 18, 'SBI', '1', 110840, TO_TIMESTAMP('2014-01-14 16:25:21:0','YYYY-MM-DD HH24:MI:SS:FF'), NULL, NULL);
INSERT INTO attachment_requirement(ATTACHMENT_REQUIREMENT_ID, PLAN_TYPE, INS_COMPANY_ID, INSURANCE_PLAN, PROVIDER_TAXONOMY, PROCEDURE_CODE, REQUIREMENT_ID, VENDOR_TYPE, IS_ACTIVE, CREATE_EMPLOYEE_NUMBER, CREATE_DATETIME, UPDATE_EMPLOYEE_NUMBER, UPDATE_DATETIME)
  VALUES(1500, NULL, NULL, NULL, NULL, 'D2751', 26, 'SBI', '1', 110840, TO_TIMESTAMP('2014-01-14 16:25:21:0','YYYY-MM-DD HH24:MI:SS:FF'), NULL, NULL);
INSERT INTO attachment_requirement(ATTACHMENT_REQUIREMENT_ID, PLAN_TYPE, INS_COMPANY_ID, INSURANCE_PLAN, PROVIDER_TAXONOMY, PROCEDURE_CODE, REQUIREMENT_ID, VENDOR_TYPE, IS_ACTIVE, CREATE_EMPLOYEE_NUMBER, CREATE_DATETIME, UPDATE_EMPLOYEE_NUMBER, UPDATE_DATETIME)
  VALUES(1501, NULL, NULL, NULL, NULL, 'D2751', 18, 'SBI', '1', 110840, TO_TIMESTAMP('2014-01-14 16:25:21:0','YYYY-MM-DD HH24:MI:SS:FF'), NULL, NULL);
INSERT INTO attachment_requirement(ATTACHMENT_REQUIREMENT_ID, PLAN_TYPE, INS_COMPANY_ID, INSURANCE_PLAN, PROVIDER_TAXONOMY, PROCEDURE_CODE, REQUIREMENT_ID, VENDOR_TYPE, IS_ACTIVE, CREATE_EMPLOYEE_NUMBER, CREATE_DATETIME, UPDATE_EMPLOYEE_NUMBER, UPDATE_DATETIME)
  VALUES(1504, NULL, NULL, NULL, NULL, 'D2752', 26, 'SBI', '1', 110840, TO_TIMESTAMP('2014-01-14 16:25:21:0','YYYY-MM-DD HH24:MI:SS:FF'), NULL, NULL);
INSERT INTO attachment_requirement(ATTACHMENT_REQUIREMENT_ID, PLAN_TYPE, INS_COMPANY_ID, INSURANCE_PLAN, PROVIDER_TAXONOMY, PROCEDURE_CODE, REQUIREMENT_ID, VENDOR_TYPE, IS_ACTIVE, CREATE_EMPLOYEE_NUMBER, CREATE_DATETIME, UPDATE_EMPLOYEE_NUMBER, UPDATE_DATETIME)
  VALUES(1505, NULL, NULL, NULL, NULL, 'D2752', 18, 'SBI', '1', 110840, TO_TIMESTAMP('2014-01-14 16:25:21:0','YYYY-MM-DD HH24:MI:SS:FF'), NULL, NULL);
INSERT INTO attachment_requirement(ATTACHMENT_REQUIREMENT_ID, PLAN_TYPE, INS_COMPANY_ID, INSURANCE_PLAN, PROVIDER_TAXONOMY, PROCEDURE_CODE, REQUIREMENT_ID, VENDOR_TYPE, IS_ACTIVE, CREATE_EMPLOYEE_NUMBER, CREATE_DATETIME, UPDATE_EMPLOYEE_NUMBER, UPDATE_DATETIME)
  VALUES(1507, NULL, NULL, NULL, NULL, 'D2780', 2, 'SBI', '1', 110840, TO_TIMESTAMP('2014-01-14 16:25:21:0','YYYY-MM-DD HH24:MI:SS:FF'), NULL, NULL);
INSERT INTO attachment_requirement(ATTACHMENT_REQUIREMENT_ID, PLAN_TYPE, INS_COMPANY_ID, INSURANCE_PLAN, PROVIDER_TAXONOMY, PROCEDURE_CODE, REQUIREMENT_ID, VENDOR_TYPE, IS_ACTIVE, CREATE_EMPLOYEE_NUMBER, CREATE_DATETIME, UPDATE_EMPLOYEE_NUMBER, UPDATE_DATETIME)
  VALUES(1508, NULL, NULL, NULL, NULL, 'D2781', 26, 'SBI', '1', 110840, TO_TIMESTAMP('2014-01-14 16:25:21:0','YYYY-MM-DD HH24:MI:SS:FF'), NULL, NULL);
INSERT INTO attachment_requirement(ATTACHMENT_REQUIREMENT_ID, PLAN_TYPE, INS_COMPANY_ID, INSURANCE_PLAN, PROVIDER_TAXONOMY, PROCEDURE_CODE, REQUIREMENT_ID, VENDOR_TYPE, IS_ACTIVE, CREATE_EMPLOYEE_NUMBER, CREATE_DATETIME, UPDATE_EMPLOYEE_NUMBER, UPDATE_DATETIME)
  VALUES(1509, NULL, NULL, NULL, NULL, 'D2781', 18, 'SBI', '1', 110840, TO_TIMESTAMP('2014-01-14 16:25:21:0','YYYY-MM-DD HH24:MI:SS:FF'), NULL, NULL);
INSERT INTO attachment_requirement(ATTACHMENT_REQUIREMENT_ID, PLAN_TYPE, INS_COMPANY_ID, INSURANCE_PLAN, PROVIDER_TAXONOMY, PROCEDURE_CODE, REQUIREMENT_ID, VENDOR_TYPE, IS_ACTIVE, CREATE_EMPLOYEE_NUMBER, CREATE_DATETIME, UPDATE_EMPLOYEE_NUMBER, UPDATE_DATETIME)
  VALUES(1511, NULL, NULL, NULL, NULL, 'D2782', 2, 'SBI', '1', 110840, TO_TIMESTAMP('2014-01-14 16:25:21:0','YYYY-MM-DD HH24:MI:SS:FF'), NULL, NULL);
INSERT INTO attachment_requirement(ATTACHMENT_REQUIREMENT_ID, PLAN_TYPE, INS_COMPANY_ID, INSURANCE_PLAN, PROVIDER_TAXONOMY, PROCEDURE_CODE, REQUIREMENT_ID, VENDOR_TYPE, IS_ACTIVE, CREATE_EMPLOYEE_NUMBER, CREATE_DATETIME, UPDATE_EMPLOYEE_NUMBER, UPDATE_DATETIME)
  VALUES(1512, NULL, NULL, NULL, NULL, 'D2783', 2, 'SBI', '1', 110840, TO_TIMESTAMP('2014-01-14 16:25:21:0','YYYY-MM-DD HH24:MI:SS:FF'), NULL, NULL);
INSERT INTO attachment_requirement(ATTACHMENT_REQUIREMENT_ID, PLAN_TYPE, INS_COMPANY_ID, INSURANCE_PLAN, PROVIDER_TAXONOMY, PROCEDURE_CODE, REQUIREMENT_ID, VENDOR_TYPE, IS_ACTIVE, CREATE_EMPLOYEE_NUMBER, CREATE_DATETIME, UPDATE_EMPLOYEE_NUMBER, UPDATE_DATETIME)
  VALUES(1513, NULL, NULL, NULL, NULL, 'D2790', 26, 'SBI', '1', 110840, TO_TIMESTAMP('2014-01-14 16:25:21:0','YYYY-MM-DD HH24:MI:SS:FF'), NULL, NULL);
INSERT INTO attachment_requirement(ATTACHMENT_REQUIREMENT_ID, PLAN_TYPE, INS_COMPANY_ID, INSURANCE_PLAN, PROVIDER_TAXONOMY, PROCEDURE_CODE, REQUIREMENT_ID, VENDOR_TYPE, IS_ACTIVE, CREATE_EMPLOYEE_NUMBER, CREATE_DATETIME, UPDATE_EMPLOYEE_NUMBER, UPDATE_DATETIME)
  VALUES(1514, NULL, NULL, NULL, NULL, 'D2790', 18, 'SBI', '1', 110840, TO_TIMESTAMP('2014-01-14 16:25:21:0','YYYY-MM-DD HH24:MI:SS:FF'), NULL, NULL);
INSERT INTO attachment_requirement(ATTACHMENT_REQUIREMENT_ID, PLAN_TYPE, INS_COMPANY_ID, INSURANCE_PLAN, PROVIDER_TAXONOMY, PROCEDURE_CODE, REQUIREMENT_ID, VENDOR_TYPE, IS_ACTIVE, CREATE_EMPLOYEE_NUMBER, CREATE_DATETIME, UPDATE_EMPLOYEE_NUMBER, UPDATE_DATETIME)
  VALUES(1517, NULL, NULL, NULL, NULL, 'D2791', 26, 'SBI', '1', 110840, TO_TIMESTAMP('2014-01-14 16:25:21:0','YYYY-MM-DD HH24:MI:SS:FF'), NULL, NULL);
INSERT INTO attachment_requirement(ATTACHMENT_REQUIREMENT_ID, PLAN_TYPE, INS_COMPANY_ID, INSURANCE_PLAN, PROVIDER_TAXONOMY, PROCEDURE_CODE, REQUIREMENT_ID, VENDOR_TYPE, IS_ACTIVE, CREATE_EMPLOYEE_NUMBER, CREATE_DATETIME, UPDATE_EMPLOYEE_NUMBER, UPDATE_DATETIME)
  VALUES(1518, NULL, NULL, NULL, NULL, 'D2791', 18, 'SBI', '1', 110840, TO_TIMESTAMP('2014-01-14 16:25:21:0','YYYY-MM-DD HH24:MI:SS:FF'), NULL, NULL);
INSERT INTO attachment_requirement(ATTACHMENT_REQUIREMENT_ID, PLAN_TYPE, INS_COMPANY_ID, INSURANCE_PLAN, PROVIDER_TAXONOMY, PROCEDURE_CODE, REQUIREMENT_ID, VENDOR_TYPE, IS_ACTIVE, CREATE_EMPLOYEE_NUMBER, CREATE_DATETIME, UPDATE_EMPLOYEE_NUMBER, UPDATE_DATETIME)
  VALUES(1521, NULL, NULL, NULL, NULL, 'D2792', 26, 'SBI', '1', 110840, TO_TIMESTAMP('2014-01-14 16:25:21:0','YYYY-MM-DD HH24:MI:SS:FF'), NULL, NULL);
INSERT INTO attachment_requirement(ATTACHMENT_REQUIREMENT_ID, PLAN_TYPE, INS_COMPANY_ID, INSURANCE_PLAN, PROVIDER_TAXONOMY, PROCEDURE_CODE, REQUIREMENT_ID, VENDOR_TYPE, IS_ACTIVE, CREATE_EMPLOYEE_NUMBER, CREATE_DATETIME, UPDATE_EMPLOYEE_NUMBER, UPDATE_DATETIME)
  VALUES(1522, NULL, NULL, NULL, NULL, 'D2792', 18, 'SBI', '1', 110840, TO_TIMESTAMP('2014-01-14 16:25:21:0','YYYY-MM-DD HH24:MI:SS:FF'), NULL, NULL);
INSERT INTO attachment_requirement(ATTACHMENT_REQUIREMENT_ID, PLAN_TYPE, INS_COMPANY_ID, INSURANCE_PLAN, PROVIDER_TAXONOMY, PROCEDURE_CODE, REQUIREMENT_ID, VENDOR_TYPE, IS_ACTIVE, CREATE_EMPLOYEE_NUMBER, CREATE_DATETIME, UPDATE_EMPLOYEE_NUMBER, UPDATE_DATETIME)
  VALUES(1525, NULL, NULL, NULL, NULL, 'D2794', 2, 'SBI', '1', 110840, TO_TIMESTAMP('2014-01-14 16:25:21:0','YYYY-MM-DD HH24:MI:SS:FF'), NULL, NULL);
INSERT INTO attachment_requirement(ATTACHMENT_REQUIREMENT_ID, PLAN_TYPE, INS_COMPANY_ID, INSURANCE_PLAN, PROVIDER_TAXONOMY, PROCEDURE_CODE, REQUIREMENT_ID, VENDOR_TYPE, IS_ACTIVE, CREATE_EMPLOYEE_NUMBER, CREATE_DATETIME, UPDATE_EMPLOYEE_NUMBER, UPDATE_DATETIME)
  VALUES(1526, NULL, NULL, NULL, NULL, 'D2799', 18, 'SBI', '1', 110840, TO_TIMESTAMP('2014-01-14 16:25:21:0','YYYY-MM-DD HH24:MI:SS:FF'), NULL, NULL);
INSERT INTO attachment_requirement(ATTACHMENT_REQUIREMENT_ID, PLAN_TYPE, INS_COMPANY_ID, INSURANCE_PLAN, PROVIDER_TAXONOMY, PROCEDURE_CODE, REQUIREMENT_ID, VENDOR_TYPE, IS_ACTIVE, CREATE_EMPLOYEE_NUMBER, CREATE_DATETIME, UPDATE_EMPLOYEE_NUMBER, UPDATE_DATETIME)
  VALUES(1527, NULL, NULL, NULL, NULL, 'D2799', 26, 'SBI', '1', 110840, TO_TIMESTAMP('2014-01-14 16:25:21:0','YYYY-MM-DD HH24:MI:SS:FF'), NULL, NULL);
INSERT INTO attachment_requirement(ATTACHMENT_REQUIREMENT_ID, PLAN_TYPE, INS_COMPANY_ID, INSURANCE_PLAN, PROVIDER_TAXONOMY, PROCEDURE_CODE, REQUIREMENT_ID, VENDOR_TYPE, IS_ACTIVE, CREATE_EMPLOYEE_NUMBER, CREATE_DATETIME, UPDATE_EMPLOYEE_NUMBER, UPDATE_DATETIME)
  VALUES(1528, NULL, NULL, NULL, NULL, 'D2930', 26, 'SBI', '1', 110840, TO_TIMESTAMP('2014-01-14 16:25:21:0','YYYY-MM-DD HH24:MI:SS:FF'), NULL, NULL);
INSERT INTO attachment_requirement(ATTACHMENT_REQUIREMENT_ID, PLAN_TYPE, INS_COMPANY_ID, INSURANCE_PLAN, PROVIDER_TAXONOMY, PROCEDURE_CODE, REQUIREMENT_ID, VENDOR_TYPE, IS_ACTIVE, CREATE_EMPLOYEE_NUMBER, CREATE_DATETIME, UPDATE_EMPLOYEE_NUMBER, UPDATE_DATETIME)
  VALUES(1530, NULL, NULL, NULL, NULL, 'D2931', 26, 'SBI', '1', 110840, TO_TIMESTAMP('2014-01-14 16:25:21:0','YYYY-MM-DD HH24:MI:SS:FF'), NULL, NULL);
INSERT INTO attachment_requirement(ATTACHMENT_REQUIREMENT_ID, PLAN_TYPE, INS_COMPANY_ID, INSURANCE_PLAN, PROVIDER_TAXONOMY, PROCEDURE_CODE, REQUIREMENT_ID, VENDOR_TYPE, IS_ACTIVE, CREATE_EMPLOYEE_NUMBER, CREATE_DATETIME, UPDATE_EMPLOYEE_NUMBER, UPDATE_DATETIME)
  VALUES(1532, NULL, NULL, NULL, NULL, 'D2932', 26, 'SBI', '1', 110840, TO_TIMESTAMP('2014-01-14 16:25:21:0','YYYY-MM-DD HH24:MI:SS:FF'), NULL, NULL);
INSERT INTO attachment_requirement(ATTACHMENT_REQUIREMENT_ID, PLAN_TYPE, INS_COMPANY_ID, INSURANCE_PLAN, PROVIDER_TAXONOMY, PROCEDURE_CODE, REQUIREMENT_ID, VENDOR_TYPE, IS_ACTIVE, CREATE_EMPLOYEE_NUMBER, CREATE_DATETIME, UPDATE_EMPLOYEE_NUMBER, UPDATE_DATETIME)
  VALUES(1533, NULL, NULL, NULL, NULL, 'D2933', 26, 'SBI', '1', 110840, TO_TIMESTAMP('2014-01-14 16:25:21:0','YYYY-MM-DD HH24:MI:SS:FF'), NULL, NULL);
INSERT INTO attachment_requirement(ATTACHMENT_REQUIREMENT_ID, PLAN_TYPE, INS_COMPANY_ID, INSURANCE_PLAN, PROVIDER_TAXONOMY, PROCEDURE_CODE, REQUIREMENT_ID, VENDOR_TYPE, IS_ACTIVE, CREATE_EMPLOYEE_NUMBER, CREATE_DATETIME, UPDATE_EMPLOYEE_NUMBER, UPDATE_DATETIME)
  VALUES(1534, NULL, NULL, NULL, NULL, 'D2934', 26, 'SBI', '1', 110840, TO_TIMESTAMP('2014-01-14 16:25:21:0','YYYY-MM-DD HH24:MI:SS:FF'), NULL, NULL);
INSERT INTO attachment_requirement(ATTACHMENT_REQUIREMENT_ID, PLAN_TYPE, INS_COMPANY_ID, INSURANCE_PLAN, PROVIDER_TAXONOMY, PROCEDURE_CODE, REQUIREMENT_ID, VENDOR_TYPE, IS_ACTIVE, CREATE_EMPLOYEE_NUMBER, CREATE_DATETIME, UPDATE_EMPLOYEE_NUMBER, UPDATE_DATETIME)
  VALUES(1535, NULL, NULL, NULL, NULL, 'D2940', 26, 'SBI', '1', 110840, TO_TIMESTAMP('2014-01-14 16:25:21:0','YYYY-MM-DD HH24:MI:SS:FF'), NULL, NULL);
INSERT INTO attachment_requirement(ATTACHMENT_REQUIREMENT_ID, PLAN_TYPE, INS_COMPANY_ID, INSURANCE_PLAN, PROVIDER_TAXONOMY, PROCEDURE_CODE, REQUIREMENT_ID, VENDOR_TYPE, IS_ACTIVE, CREATE_EMPLOYEE_NUMBER, CREATE_DATETIME, UPDATE_EMPLOYEE_NUMBER, UPDATE_DATETIME)
  VALUES(1536, NULL, NULL, NULL, NULL, 'D2949', 26, 'SBI', '1', 110840, TO_TIMESTAMP('2014-01-14 16:25:21:0','YYYY-MM-DD HH24:MI:SS:FF'), NULL, NULL);
INSERT INTO attachment_requirement(ATTACHMENT_REQUIREMENT_ID, PLAN_TYPE, INS_COMPANY_ID, INSURANCE_PLAN, PROVIDER_TAXONOMY, PROCEDURE_CODE, REQUIREMENT_ID, VENDOR_TYPE, IS_ACTIVE, CREATE_EMPLOYEE_NUMBER, CREATE_DATETIME, UPDATE_EMPLOYEE_NUMBER, UPDATE_DATETIME)
  VALUES(1537, NULL, NULL, NULL, NULL, 'D2950', 2, 'SBI', '1', 110840, TO_TIMESTAMP('2014-01-14 16:25:21:0','YYYY-MM-DD HH24:MI:SS:FF'), NULL, NULL);
INSERT INTO attachment_requirement(ATTACHMENT_REQUIREMENT_ID, PLAN_TYPE, INS_COMPANY_ID, INSURANCE_PLAN, PROVIDER_TAXONOMY, PROCEDURE_CODE, REQUIREMENT_ID, VENDOR_TYPE, IS_ACTIVE, CREATE_EMPLOYEE_NUMBER, CREATE_DATETIME, UPDATE_EMPLOYEE_NUMBER, UPDATE_DATETIME)
  VALUES(1538, NULL, NULL, NULL, NULL, 'D2950', 26, 'SBI', '1', 110840, TO_TIMESTAMP('2014-01-14 16:25:21:0','YYYY-MM-DD HH24:MI:SS:FF'), NULL, NULL);
INSERT INTO attachment_requirement(ATTACHMENT_REQUIREMENT_ID, PLAN_TYPE, INS_COMPANY_ID, INSURANCE_PLAN, PROVIDER_TAXONOMY, PROCEDURE_CODE, REQUIREMENT_ID, VENDOR_TYPE, IS_ACTIVE, CREATE_EMPLOYEE_NUMBER, CREATE_DATETIME, UPDATE_EMPLOYEE_NUMBER, UPDATE_DATETIME)
  VALUES(1539, NULL, NULL, NULL, NULL, 'D2951', 26, 'SBI', '1', 110840, TO_TIMESTAMP('2014-01-14 16:25:21:0','YYYY-MM-DD HH24:MI:SS:FF'), NULL, NULL);
INSERT INTO attachment_requirement(ATTACHMENT_REQUIREMENT_ID, PLAN_TYPE, INS_COMPANY_ID, INSURANCE_PLAN, PROVIDER_TAXONOMY, PROCEDURE_CODE, REQUIREMENT_ID, VENDOR_TYPE, IS_ACTIVE, CREATE_EMPLOYEE_NUMBER, CREATE_DATETIME, UPDATE_EMPLOYEE_NUMBER, UPDATE_DATETIME)
  VALUES(1540, NULL, NULL, NULL, NULL, 'D2952', 26, 'SBI', '1', 110840, TO_TIMESTAMP('2014-01-14 16:25:21:0','YYYY-MM-DD HH24:MI:SS:FF'), NULL, NULL);
INSERT INTO attachment_requirement(ATTACHMENT_REQUIREMENT_ID, PLAN_TYPE, INS_COMPANY_ID, INSURANCE_PLAN, PROVIDER_TAXONOMY, PROCEDURE_CODE, REQUIREMENT_ID, VENDOR_TYPE, IS_ACTIVE, CREATE_EMPLOYEE_NUMBER, CREATE_DATETIME, UPDATE_EMPLOYEE_NUMBER, UPDATE_DATETIME)
  VALUES(1541, NULL, NULL, NULL, NULL, 'D2954', 2, 'SBI', '1', 110840, TO_TIMESTAMP('2014-01-14 16:25:21:0','YYYY-MM-DD HH24:MI:SS:FF'), NULL, NULL);
INSERT INTO attachment_requirement(ATTACHMENT_REQUIREMENT_ID, PLAN_TYPE, INS_COMPANY_ID, INSURANCE_PLAN, PROVIDER_TAXONOMY, PROCEDURE_CODE, REQUIREMENT_ID, VENDOR_TYPE, IS_ACTIVE, CREATE_EMPLOYEE_NUMBER, CREATE_DATETIME, UPDATE_EMPLOYEE_NUMBER, UPDATE_DATETIME)
  VALUES(1542, NULL, NULL, NULL, NULL, 'D2954', 26, 'SBI', '1', 110840, TO_TIMESTAMP('2014-01-14 16:25:21:0','YYYY-MM-DD HH24:MI:SS:FF'), NULL, NULL);
INSERT INTO attachment_requirement(ATTACHMENT_REQUIREMENT_ID, PLAN_TYPE, INS_COMPANY_ID, INSURANCE_PLAN, PROVIDER_TAXONOMY, PROCEDURE_CODE, REQUIREMENT_ID, VENDOR_TYPE, IS_ACTIVE, CREATE_EMPLOYEE_NUMBER, CREATE_DATETIME, UPDATE_EMPLOYEE_NUMBER, UPDATE_DATETIME)
  VALUES(1543, NULL, NULL, NULL, NULL, 'D2955', 26, 'SBI', '1', 110840, TO_TIMESTAMP('2014-01-14 16:25:21:0','YYYY-MM-DD HH24:MI:SS:FF'), NULL, NULL);
INSERT INTO attachment_requirement(ATTACHMENT_REQUIREMENT_ID, PLAN_TYPE, INS_COMPANY_ID, INSURANCE_PLAN, PROVIDER_TAXONOMY, PROCEDURE_CODE, REQUIREMENT_ID, VENDOR_TYPE, IS_ACTIVE, CREATE_EMPLOYEE_NUMBER, CREATE_DATETIME, UPDATE_EMPLOYEE_NUMBER, UPDATE_DATETIME)
  VALUES(1544, NULL, NULL, NULL, NULL, 'D2960', 31, 'SBI', '1', 110840, TO_TIMESTAMP('2014-01-14 16:25:21:0','YYYY-MM-DD HH24:MI:SS:FF'), NULL, NULL);
INSERT INTO attachment_requirement(ATTACHMENT_REQUIREMENT_ID, PLAN_TYPE, INS_COMPANY_ID, INSURANCE_PLAN, PROVIDER_TAXONOMY, PROCEDURE_CODE, REQUIREMENT_ID, VENDOR_TYPE, IS_ACTIVE, CREATE_EMPLOYEE_NUMBER, CREATE_DATETIME, UPDATE_EMPLOYEE_NUMBER, UPDATE_DATETIME)
  VALUES(1545, NULL, NULL, NULL, NULL, 'D2961', 31, 'SBI', '1', 110840, TO_TIMESTAMP('2014-01-14 16:25:21:0','YYYY-MM-DD HH24:MI:SS:FF'), NULL, NULL);
INSERT INTO attachment_requirement(ATTACHMENT_REQUIREMENT_ID, PLAN_TYPE, INS_COMPANY_ID, INSURANCE_PLAN, PROVIDER_TAXONOMY, PROCEDURE_CODE, REQUIREMENT_ID, VENDOR_TYPE, IS_ACTIVE, CREATE_EMPLOYEE_NUMBER, CREATE_DATETIME, UPDATE_EMPLOYEE_NUMBER, UPDATE_DATETIME)
  VALUES(1546, NULL, NULL, NULL, NULL, 'D2962', 31, 'SBI', '1', 110840, TO_TIMESTAMP('2014-01-14 16:25:21:0','YYYY-MM-DD HH24:MI:SS:FF'), NULL, NULL);
INSERT INTO attachment_requirement(ATTACHMENT_REQUIREMENT_ID, PLAN_TYPE, INS_COMPANY_ID, INSURANCE_PLAN, PROVIDER_TAXONOMY, PROCEDURE_CODE, REQUIREMENT_ID, VENDOR_TYPE, IS_ACTIVE, CREATE_EMPLOYEE_NUMBER, CREATE_DATETIME, UPDATE_EMPLOYEE_NUMBER, UPDATE_DATETIME)
  VALUES(1547, NULL, NULL, NULL, NULL, 'D2962', 26, 'SBI', '1', 110840, TO_TIMESTAMP('2014-01-14 16:25:21:0','YYYY-MM-DD HH24:MI:SS:FF'), NULL, NULL);
INSERT INTO attachment_requirement(ATTACHMENT_REQUIREMENT_ID, PLAN_TYPE, INS_COMPANY_ID, INSURANCE_PLAN, PROVIDER_TAXONOMY, PROCEDURE_CODE, REQUIREMENT_ID, VENDOR_TYPE, IS_ACTIVE, CREATE_EMPLOYEE_NUMBER, CREATE_DATETIME, UPDATE_EMPLOYEE_NUMBER, UPDATE_DATETIME)
  VALUES(1548, NULL, NULL, NULL, NULL, 'D2970', 26, 'SBI', '1', 110840, TO_TIMESTAMP('2014-01-14 16:25:21:0','YYYY-MM-DD HH24:MI:SS:FF'), NULL, NULL);
INSERT INTO attachment_requirement(ATTACHMENT_REQUIREMENT_ID, PLAN_TYPE, INS_COMPANY_ID, INSURANCE_PLAN, PROVIDER_TAXONOMY, PROCEDURE_CODE, REQUIREMENT_ID, VENDOR_TYPE, IS_ACTIVE, CREATE_EMPLOYEE_NUMBER, CREATE_DATETIME, UPDATE_EMPLOYEE_NUMBER, UPDATE_DATETIME)
  VALUES(1549, NULL, NULL, NULL, NULL, 'D2971', 2, 'SBI', '1', 110840, TO_TIMESTAMP('2014-01-14 16:25:21:0','YYYY-MM-DD HH24:MI:SS:FF'), NULL, NULL);
INSERT INTO attachment_requirement(ATTACHMENT_REQUIREMENT_ID, PLAN_TYPE, INS_COMPANY_ID, INSURANCE_PLAN, PROVIDER_TAXONOMY, PROCEDURE_CODE, REQUIREMENT_ID, VENDOR_TYPE, IS_ACTIVE, CREATE_EMPLOYEE_NUMBER, CREATE_DATETIME, UPDATE_EMPLOYEE_NUMBER, UPDATE_DATETIME)
  VALUES(1550, NULL, NULL, NULL, NULL, 'D2971', 26, 'SBI', '1', 110840, TO_TIMESTAMP('2014-01-14 16:25:21:0','YYYY-MM-DD HH24:MI:SS:FF'), NULL, NULL);
INSERT INTO attachment_requirement(ATTACHMENT_REQUIREMENT_ID, PLAN_TYPE, INS_COMPANY_ID, INSURANCE_PLAN, PROVIDER_TAXONOMY, PROCEDURE_CODE, REQUIREMENT_ID, VENDOR_TYPE, IS_ACTIVE, CREATE_EMPLOYEE_NUMBER, CREATE_DATETIME, UPDATE_EMPLOYEE_NUMBER, UPDATE_DATETIME)
  VALUES(1552, NULL, NULL, NULL, NULL, 'D2980', 26, 'SBI', '1', 110840, TO_TIMESTAMP('2014-01-14 16:25:21:0','YYYY-MM-DD HH24:MI:SS:FF'), NULL, NULL);
INSERT INTO attachment_requirement(ATTACHMENT_REQUIREMENT_ID, PLAN_TYPE, INS_COMPANY_ID, INSURANCE_PLAN, PROVIDER_TAXONOMY, PROCEDURE_CODE, REQUIREMENT_ID, VENDOR_TYPE, IS_ACTIVE, CREATE_EMPLOYEE_NUMBER, CREATE_DATETIME, UPDATE_EMPLOYEE_NUMBER, UPDATE_DATETIME)
  VALUES(1553, NULL, NULL, NULL, NULL, 'D3110', 26, 'SBI', '1', 110840, TO_TIMESTAMP('2014-01-14 16:25:21:0','YYYY-MM-DD HH24:MI:SS:FF'), NULL, NULL);
INSERT INTO attachment_requirement(ATTACHMENT_REQUIREMENT_ID, PLAN_TYPE, INS_COMPANY_ID, INSURANCE_PLAN, PROVIDER_TAXONOMY, PROCEDURE_CODE, REQUIREMENT_ID, VENDOR_TYPE, IS_ACTIVE, CREATE_EMPLOYEE_NUMBER, CREATE_DATETIME, UPDATE_EMPLOYEE_NUMBER, UPDATE_DATETIME)
  VALUES(1554, NULL, NULL, NULL, NULL, 'D3120', 26, 'SBI', '1', 110840, TO_TIMESTAMP('2014-01-14 16:25:21:0','YYYY-MM-DD HH24:MI:SS:FF'), NULL, NULL);
INSERT INTO attachment_requirement(ATTACHMENT_REQUIREMENT_ID, PLAN_TYPE, INS_COMPANY_ID, INSURANCE_PLAN, PROVIDER_TAXONOMY, PROCEDURE_CODE, REQUIREMENT_ID, VENDOR_TYPE, IS_ACTIVE, CREATE_EMPLOYEE_NUMBER, CREATE_DATETIME, UPDATE_EMPLOYEE_NUMBER, UPDATE_DATETIME)
  VALUES(1555, NULL, NULL, NULL, NULL, 'D3220', 60, 'SBI', '1', 110840, TO_TIMESTAMP('2014-01-14 16:25:21:0','YYYY-MM-DD HH24:MI:SS:FF'), NULL, NULL);
INSERT INTO attachment_requirement(ATTACHMENT_REQUIREMENT_ID, PLAN_TYPE, INS_COMPANY_ID, INSURANCE_PLAN, PROVIDER_TAXONOMY, PROCEDURE_CODE, REQUIREMENT_ID, VENDOR_TYPE, IS_ACTIVE, CREATE_EMPLOYEE_NUMBER, CREATE_DATETIME, UPDATE_EMPLOYEE_NUMBER, UPDATE_DATETIME)
  VALUES(1556, NULL, NULL, 630, NULL, 'D3220', 60, 'SBI', '1', 110840, TO_TIMESTAMP('2014-01-14 16:25:21:0','YYYY-MM-DD HH24:MI:SS:FF'), NULL, NULL);
INSERT INTO attachment_requirement(ATTACHMENT_REQUIREMENT_ID, PLAN_TYPE, INS_COMPANY_ID, INSURANCE_PLAN, PROVIDER_TAXONOMY, PROCEDURE_CODE, REQUIREMENT_ID, VENDOR_TYPE, IS_ACTIVE, CREATE_EMPLOYEE_NUMBER, CREATE_DATETIME, UPDATE_EMPLOYEE_NUMBER, UPDATE_DATETIME)
  VALUES(1557, NULL, NULL, NULL, NULL, 'D3221', 26, 'SBI', '1', 110840, TO_TIMESTAMP('2014-01-14 16:25:21:0','YYYY-MM-DD HH24:MI:SS:FF'), NULL, NULL);
INSERT INTO attachment_requirement(ATTACHMENT_REQUIREMENT_ID, PLAN_TYPE, INS_COMPANY_ID, INSURANCE_PLAN, PROVIDER_TAXONOMY, PROCEDURE_CODE, REQUIREMENT_ID, VENDOR_TYPE, IS_ACTIVE, CREATE_EMPLOYEE_NUMBER, CREATE_DATETIME, UPDATE_EMPLOYEE_NUMBER, UPDATE_DATETIME)
  VALUES(1559, NULL, NULL, NULL, NULL, 'D3230', 26, 'SBI', '1', 110840, TO_TIMESTAMP('2014-01-14 16:25:21:0','YYYY-MM-DD HH24:MI:SS:FF'), NULL, NULL);
INSERT INTO attachment_requirement(ATTACHMENT_REQUIREMENT_ID, PLAN_TYPE, INS_COMPANY_ID, INSURANCE_PLAN, PROVIDER_TAXONOMY, PROCEDURE_CODE, REQUIREMENT_ID, VENDOR_TYPE, IS_ACTIVE, CREATE_EMPLOYEE_NUMBER, CREATE_DATETIME, UPDATE_EMPLOYEE_NUMBER, UPDATE_DATETIME)
  VALUES(1560, NULL, NULL, NULL, NULL, 'D3240', 26, 'SBI', '1', 110840, TO_TIMESTAMP('2014-01-14 16:25:21:0','YYYY-MM-DD HH24:MI:SS:FF'), NULL, NULL);
INSERT INTO attachment_requirement(ATTACHMENT_REQUIREMENT_ID, PLAN_TYPE, INS_COMPANY_ID, INSURANCE_PLAN, PROVIDER_TAXONOMY, PROCEDURE_CODE, REQUIREMENT_ID, VENDOR_TYPE, IS_ACTIVE, CREATE_EMPLOYEE_NUMBER, CREATE_DATETIME, UPDATE_EMPLOYEE_NUMBER, UPDATE_DATETIME)
  VALUES(1561, NULL, NULL, NULL, NULL, 'D3310', 2, 'SBI', '1', 110840, TO_TIMESTAMP('2014-01-14 16:25:21:0','YYYY-MM-DD HH24:MI:SS:FF'), NULL, NULL);
INSERT INTO attachment_requirement(ATTACHMENT_REQUIREMENT_ID, PLAN_TYPE, INS_COMPANY_ID, INSURANCE_PLAN, PROVIDER_TAXONOMY, PROCEDURE_CODE, REQUIREMENT_ID, VENDOR_TYPE, IS_ACTIVE, CREATE_EMPLOYEE_NUMBER, CREATE_DATETIME, UPDATE_EMPLOYEE_NUMBER, UPDATE_DATETIME)
  VALUES(1562, NULL, NULL, NULL, NULL, 'D3310', 60, 'SBI', '1', 110840, TO_TIMESTAMP('2014-01-14 16:25:21:0','YYYY-MM-DD HH24:MI:SS:FF'), NULL, NULL);
INSERT INTO attachment_requirement(ATTACHMENT_REQUIREMENT_ID, PLAN_TYPE, INS_COMPANY_ID, INSURANCE_PLAN, PROVIDER_TAXONOMY, PROCEDURE_CODE, REQUIREMENT_ID, VENDOR_TYPE, IS_ACTIVE, CREATE_EMPLOYEE_NUMBER, CREATE_DATETIME, UPDATE_EMPLOYEE_NUMBER, UPDATE_DATETIME)
  VALUES(1563, NULL, NULL, NULL, NULL, 'D3310', 26, 'SBI', '1', 110840, TO_TIMESTAMP('2014-01-14 16:25:21:0','YYYY-MM-DD HH24:MI:SS:FF'), NULL, NULL);
INSERT INTO attachment_requirement(ATTACHMENT_REQUIREMENT_ID, PLAN_TYPE, INS_COMPANY_ID, INSURANCE_PLAN, PROVIDER_TAXONOMY, PROCEDURE_CODE, REQUIREMENT_ID, VENDOR_TYPE, IS_ACTIVE, CREATE_EMPLOYEE_NUMBER, CREATE_DATETIME, UPDATE_EMPLOYEE_NUMBER, UPDATE_DATETIME)
  VALUES(1564, NULL, NULL, NULL, NULL, 'D3320', 60, 'SBI', '1', 110840, TO_TIMESTAMP('2014-01-14 16:25:21:0','YYYY-MM-DD HH24:MI:SS:FF'), NULL, NULL);
INSERT INTO attachment_requirement(ATTACHMENT_REQUIREMENT_ID, PLAN_TYPE, INS_COMPANY_ID, INSURANCE_PLAN, PROVIDER_TAXONOMY, PROCEDURE_CODE, REQUIREMENT_ID, VENDOR_TYPE, IS_ACTIVE, CREATE_EMPLOYEE_NUMBER, CREATE_DATETIME, UPDATE_EMPLOYEE_NUMBER, UPDATE_DATETIME)
  VALUES(1565, NULL, NULL, NULL, NULL, 'D3320', 26, 'SBI', '1', 110840, TO_TIMESTAMP('2014-01-14 16:25:21:0','YYYY-MM-DD HH24:MI:SS:FF'), NULL, NULL);
INSERT INTO attachment_requirement(ATTACHMENT_REQUIREMENT_ID, PLAN_TYPE, INS_COMPANY_ID, INSURANCE_PLAN, PROVIDER_TAXONOMY, PROCEDURE_CODE, REQUIREMENT_ID, VENDOR_TYPE, IS_ACTIVE, CREATE_EMPLOYEE_NUMBER, CREATE_DATETIME, UPDATE_EMPLOYEE_NUMBER, UPDATE_DATETIME)
  VALUES(1566, NULL, NULL, NULL, NULL, 'D3330', 60, 'SBI', '1', 110840, TO_TIMESTAMP('2014-01-14 16:25:21:0','YYYY-MM-DD HH24:MI:SS:FF'), NULL, NULL);
INSERT INTO attachment_requirement(ATTACHMENT_REQUIREMENT_ID, PLAN_TYPE, INS_COMPANY_ID, INSURANCE_PLAN, PROVIDER_TAXONOMY, PROCEDURE_CODE, REQUIREMENT_ID, VENDOR_TYPE, IS_ACTIVE, CREATE_EMPLOYEE_NUMBER, CREATE_DATETIME, UPDATE_EMPLOYEE_NUMBER, UPDATE_DATETIME)
  VALUES(1567, NULL, NULL, NULL, NULL, 'D3330', 26, 'SBI', '1', 110840, TO_TIMESTAMP('2014-01-14 16:25:21:0','YYYY-MM-DD HH24:MI:SS:FF'), NULL, NULL);
INSERT INTO attachment_requirement(ATTACHMENT_REQUIREMENT_ID, PLAN_TYPE, INS_COMPANY_ID, INSURANCE_PLAN, PROVIDER_TAXONOMY, PROCEDURE_CODE, REQUIREMENT_ID, VENDOR_TYPE, IS_ACTIVE, CREATE_EMPLOYEE_NUMBER, CREATE_DATETIME, UPDATE_EMPLOYEE_NUMBER, UPDATE_DATETIME)
  VALUES(1568, NULL, NULL, NULL, NULL, 'D3331', 60, 'SBI', '1', 110840, TO_TIMESTAMP('2014-01-14 16:25:21:0','YYYY-MM-DD HH24:MI:SS:FF'), NULL, NULL);
INSERT INTO attachment_requirement(ATTACHMENT_REQUIREMENT_ID, PLAN_TYPE, INS_COMPANY_ID, INSURANCE_PLAN, PROVIDER_TAXONOMY, PROCEDURE_CODE, REQUIREMENT_ID, VENDOR_TYPE, IS_ACTIVE, CREATE_EMPLOYEE_NUMBER, CREATE_DATETIME, UPDATE_EMPLOYEE_NUMBER, UPDATE_DATETIME)
  VALUES(1569, NULL, NULL, NULL, NULL, 'D3331', 26, 'SBI', '1', 110840, TO_TIMESTAMP('2014-01-14 16:25:21:0','YYYY-MM-DD HH24:MI:SS:FF'), NULL, NULL);
INSERT INTO attachment_requirement(ATTACHMENT_REQUIREMENT_ID, PLAN_TYPE, INS_COMPANY_ID, INSURANCE_PLAN, PROVIDER_TAXONOMY, PROCEDURE_CODE, REQUIREMENT_ID, VENDOR_TYPE, IS_ACTIVE, CREATE_EMPLOYEE_NUMBER, CREATE_DATETIME, UPDATE_EMPLOYEE_NUMBER, UPDATE_DATETIME)
  VALUES(1570, NULL, NULL, NULL, NULL, 'D3332', 60, 'SBI', '1', 110840, TO_TIMESTAMP('2014-01-14 16:25:21:0','YYYY-MM-DD HH24:MI:SS:FF'), NULL, NULL);
INSERT INTO attachment_requirement(ATTACHMENT_REQUIREMENT_ID, PLAN_TYPE, INS_COMPANY_ID, INSURANCE_PLAN, PROVIDER_TAXONOMY, PROCEDURE_CODE, REQUIREMENT_ID, VENDOR_TYPE, IS_ACTIVE, CREATE_EMPLOYEE_NUMBER, CREATE_DATETIME, UPDATE_EMPLOYEE_NUMBER, UPDATE_DATETIME)
  VALUES(1571, NULL, NULL, NULL, NULL, 'D3332', 26, 'SBI', '1', 110840, TO_TIMESTAMP('2014-01-14 16:25:21:0','YYYY-MM-DD HH24:MI:SS:FF'), NULL, NULL);
INSERT INTO attachment_requirement(ATTACHMENT_REQUIREMENT_ID, PLAN_TYPE, INS_COMPANY_ID, INSURANCE_PLAN, PROVIDER_TAXONOMY, PROCEDURE_CODE, REQUIREMENT_ID, VENDOR_TYPE, IS_ACTIVE, CREATE_EMPLOYEE_NUMBER, CREATE_DATETIME, UPDATE_EMPLOYEE_NUMBER, UPDATE_DATETIME)
  VALUES(1572, NULL, NULL, NULL, NULL, 'D3333', 60, 'SBI', '1', 110840, TO_TIMESTAMP('2014-01-14 16:25:21:0','YYYY-MM-DD HH24:MI:SS:FF'), NULL, NULL);
INSERT INTO attachment_requirement(ATTACHMENT_REQUIREMENT_ID, PLAN_TYPE, INS_COMPANY_ID, INSURANCE_PLAN, PROVIDER_TAXONOMY, PROCEDURE_CODE, REQUIREMENT_ID, VENDOR_TYPE, IS_ACTIVE, CREATE_EMPLOYEE_NUMBER, CREATE_DATETIME, UPDATE_EMPLOYEE_NUMBER, UPDATE_DATETIME)
  VALUES(1573, NULL, NULL, NULL, NULL, 'D3346', 17, 'SBI', '1', 110840, TO_TIMESTAMP('2014-01-14 16:25:21:0','YYYY-MM-DD HH24:MI:SS:FF'), NULL, NULL);
INSERT INTO attachment_requirement(ATTACHMENT_REQUIREMENT_ID, PLAN_TYPE, INS_COMPANY_ID, INSURANCE_PLAN, PROVIDER_TAXONOMY, PROCEDURE_CODE, REQUIREMENT_ID, VENDOR_TYPE, IS_ACTIVE, CREATE_EMPLOYEE_NUMBER, CREATE_DATETIME, UPDATE_EMPLOYEE_NUMBER, UPDATE_DATETIME)
  VALUES(1574, NULL, NULL, NULL, NULL, 'D3346', 60, 'SBI', '1', 110840, TO_TIMESTAMP('2014-01-14 16:25:21:0','YYYY-MM-DD HH24:MI:SS:FF'), NULL, NULL);
INSERT INTO attachment_requirement(ATTACHMENT_REQUIREMENT_ID, PLAN_TYPE, INS_COMPANY_ID, INSURANCE_PLAN, PROVIDER_TAXONOMY, PROCEDURE_CODE, REQUIREMENT_ID, VENDOR_TYPE, IS_ACTIVE, CREATE_EMPLOYEE_NUMBER, CREATE_DATETIME, UPDATE_EMPLOYEE_NUMBER, UPDATE_DATETIME)
  VALUES(1575, NULL, NULL, NULL, NULL, 'D3346', 26, 'SBI', '1', 110840, TO_TIMESTAMP('2014-01-14 16:25:21:0','YYYY-MM-DD HH24:MI:SS:FF'), NULL, NULL);
INSERT INTO attachment_requirement(ATTACHMENT_REQUIREMENT_ID, PLAN_TYPE, INS_COMPANY_ID, INSURANCE_PLAN, PROVIDER_TAXONOMY, PROCEDURE_CODE, REQUIREMENT_ID, VENDOR_TYPE, IS_ACTIVE, CREATE_EMPLOYEE_NUMBER, CREATE_DATETIME, UPDATE_EMPLOYEE_NUMBER, UPDATE_DATETIME)
  VALUES(1576, NULL, NULL, NULL, NULL, 'D3347', 17, 'SBI', '1', 110840, TO_TIMESTAMP('2014-01-14 16:25:21:0','YYYY-MM-DD HH24:MI:SS:FF'), NULL, NULL);
INSERT INTO attachment_requirement(ATTACHMENT_REQUIREMENT_ID, PLAN_TYPE, INS_COMPANY_ID, INSURANCE_PLAN, PROVIDER_TAXONOMY, PROCEDURE_CODE, REQUIREMENT_ID, VENDOR_TYPE, IS_ACTIVE, CREATE_EMPLOYEE_NUMBER, CREATE_DATETIME, UPDATE_EMPLOYEE_NUMBER, UPDATE_DATETIME)
  VALUES(1577, NULL, NULL, NULL, NULL, 'D3347', 60, 'SBI', '1', 110840, TO_TIMESTAMP('2014-01-14 16:25:21:0','YYYY-MM-DD HH24:MI:SS:FF'), NULL, NULL);
INSERT INTO attachment_requirement(ATTACHMENT_REQUIREMENT_ID, PLAN_TYPE, INS_COMPANY_ID, INSURANCE_PLAN, PROVIDER_TAXONOMY, PROCEDURE_CODE, REQUIREMENT_ID, VENDOR_TYPE, IS_ACTIVE, CREATE_EMPLOYEE_NUMBER, CREATE_DATETIME, UPDATE_EMPLOYEE_NUMBER, UPDATE_DATETIME)
  VALUES(1578, NULL, NULL, NULL, NULL, 'D3347', 26, 'SBI', '1', 110840, TO_TIMESTAMP('2014-01-14 16:25:21:0','YYYY-MM-DD HH24:MI:SS:FF'), NULL, NULL);
INSERT INTO attachment_requirement(ATTACHMENT_REQUIREMENT_ID, PLAN_TYPE, INS_COMPANY_ID, INSURANCE_PLAN, PROVIDER_TAXONOMY, PROCEDURE_CODE, REQUIREMENT_ID, VENDOR_TYPE, IS_ACTIVE, CREATE_EMPLOYEE_NUMBER, CREATE_DATETIME, UPDATE_EMPLOYEE_NUMBER, UPDATE_DATETIME)
  VALUES(1579, NULL, NULL, NULL, NULL, 'D3348', 17, 'SBI', '1', 110840, TO_TIMESTAMP('2014-01-14 16:25:21:0','YYYY-MM-DD HH24:MI:SS:FF'), NULL, NULL);
INSERT INTO attachment_requirement(ATTACHMENT_REQUIREMENT_ID, PLAN_TYPE, INS_COMPANY_ID, INSURANCE_PLAN, PROVIDER_TAXONOMY, PROCEDURE_CODE, REQUIREMENT_ID, VENDOR_TYPE, IS_ACTIVE, CREATE_EMPLOYEE_NUMBER, CREATE_DATETIME, UPDATE_EMPLOYEE_NUMBER, UPDATE_DATETIME)
  VALUES(1580, NULL, NULL, NULL, NULL, 'D3348', 60, 'SBI', '1', 110840, TO_TIMESTAMP('2014-01-14 16:25:21:0','YYYY-MM-DD HH24:MI:SS:FF'), NULL, NULL);
INSERT INTO attachment_requirement(ATTACHMENT_REQUIREMENT_ID, PLAN_TYPE, INS_COMPANY_ID, INSURANCE_PLAN, PROVIDER_TAXONOMY, PROCEDURE_CODE, REQUIREMENT_ID, VENDOR_TYPE, IS_ACTIVE, CREATE_EMPLOYEE_NUMBER, CREATE_DATETIME, UPDATE_EMPLOYEE_NUMBER, UPDATE_DATETIME)
  VALUES(1583, NULL, NULL, NULL, NULL, 'D3348', 26, 'SBI', '1', 110840, TO_TIMESTAMP('2014-01-14 16:25:21:0','YYYY-MM-DD HH24:MI:SS:FF'), NULL, NULL);
INSERT INTO attachment_requirement(ATTACHMENT_REQUIREMENT_ID, PLAN_TYPE, INS_COMPANY_ID, INSURANCE_PLAN, PROVIDER_TAXONOMY, PROCEDURE_CODE, REQUIREMENT_ID, VENDOR_TYPE, IS_ACTIVE, CREATE_EMPLOYEE_NUMBER, CREATE_DATETIME, UPDATE_EMPLOYEE_NUMBER, UPDATE_DATETIME)
  VALUES(1584, NULL, NULL, NULL, NULL, 'D3351', 60, 'SBI', '1', 110840, TO_TIMESTAMP('2014-01-14 16:25:21:0','YYYY-MM-DD HH24:MI:SS:FF'), NULL, NULL);
INSERT INTO attachment_requirement(ATTACHMENT_REQUIREMENT_ID, PLAN_TYPE, INS_COMPANY_ID, INSURANCE_PLAN, PROVIDER_TAXONOMY, PROCEDURE_CODE, REQUIREMENT_ID, VENDOR_TYPE, IS_ACTIVE, CREATE_EMPLOYEE_NUMBER, CREATE_DATETIME, UPDATE_EMPLOYEE_NUMBER, UPDATE_DATETIME)
  VALUES(1585, NULL, NULL, NULL, NULL, 'D3352', 26, 'SBI', '1', 110840, TO_TIMESTAMP('2014-01-14 16:25:21:0','YYYY-MM-DD HH24:MI:SS:FF'), NULL, NULL);
INSERT INTO attachment_requirement(ATTACHMENT_REQUIREMENT_ID, PLAN_TYPE, INS_COMPANY_ID, INSURANCE_PLAN, PROVIDER_TAXONOMY, PROCEDURE_CODE, REQUIREMENT_ID, VENDOR_TYPE, IS_ACTIVE, CREATE_EMPLOYEE_NUMBER, CREATE_DATETIME, UPDATE_EMPLOYEE_NUMBER, UPDATE_DATETIME)
  VALUES(1586, NULL, NULL, NULL, NULL, 'D3352', 60, 'SBI', '1', 110840, TO_TIMESTAMP('2014-01-14 16:25:21:0','YYYY-MM-DD HH24:MI:SS:FF'), NULL, NULL);
INSERT INTO attachment_requirement(ATTACHMENT_REQUIREMENT_ID, PLAN_TYPE, INS_COMPANY_ID, INSURANCE_PLAN, PROVIDER_TAXONOMY, PROCEDURE_CODE, REQUIREMENT_ID, VENDOR_TYPE, IS_ACTIVE, CREATE_EMPLOYEE_NUMBER, CREATE_DATETIME, UPDATE_EMPLOYEE_NUMBER, UPDATE_DATETIME)
  VALUES(1587, NULL, NULL, NULL, NULL, 'D3410', 26, 'SBI', '1', 110840, TO_TIMESTAMP('2014-01-14 16:25:21:0','YYYY-MM-DD HH24:MI:SS:FF'), NULL, NULL);
INSERT INTO attachment_requirement(ATTACHMENT_REQUIREMENT_ID, PLAN_TYPE, INS_COMPANY_ID, INSURANCE_PLAN, PROVIDER_TAXONOMY, PROCEDURE_CODE, REQUIREMENT_ID, VENDOR_TYPE, IS_ACTIVE, CREATE_EMPLOYEE_NUMBER, CREATE_DATETIME, UPDATE_EMPLOYEE_NUMBER, UPDATE_DATETIME)
  VALUES(1589, NULL, NULL, NULL, NULL, 'D3410', 60, 'SBI', '1', 110840, TO_TIMESTAMP('2014-01-14 16:25:21:0','YYYY-MM-DD HH24:MI:SS:FF'), NULL, NULL);
INSERT INTO attachment_requirement(ATTACHMENT_REQUIREMENT_ID, PLAN_TYPE, INS_COMPANY_ID, INSURANCE_PLAN, PROVIDER_TAXONOMY, PROCEDURE_CODE, REQUIREMENT_ID, VENDOR_TYPE, IS_ACTIVE, CREATE_EMPLOYEE_NUMBER, CREATE_DATETIME, UPDATE_EMPLOYEE_NUMBER, UPDATE_DATETIME)
  VALUES(1590, NULL, NULL, NULL, NULL, 'D3421', 26, 'SBI', '1', 110840, TO_TIMESTAMP('2014-01-14 16:25:21:0','YYYY-MM-DD HH24:MI:SS:FF'), NULL, NULL);
INSERT INTO attachment_requirement(ATTACHMENT_REQUIREMENT_ID, PLAN_TYPE, INS_COMPANY_ID, INSURANCE_PLAN, PROVIDER_TAXONOMY, PROCEDURE_CODE, REQUIREMENT_ID, VENDOR_TYPE, IS_ACTIVE, CREATE_EMPLOYEE_NUMBER, CREATE_DATETIME, UPDATE_EMPLOYEE_NUMBER, UPDATE_DATETIME)
  VALUES(1591, NULL, NULL, NULL, NULL, 'D3421', 60, 'SBI', '1', 110840, TO_TIMESTAMP('2014-01-14 16:25:21:0','YYYY-MM-DD HH24:MI:SS:FF'), NULL, NULL);
INSERT INTO attachment_requirement(ATTACHMENT_REQUIREMENT_ID, PLAN_TYPE, INS_COMPANY_ID, INSURANCE_PLAN, PROVIDER_TAXONOMY, PROCEDURE_CODE, REQUIREMENT_ID, VENDOR_TYPE, IS_ACTIVE, CREATE_EMPLOYEE_NUMBER, CREATE_DATETIME, UPDATE_EMPLOYEE_NUMBER, UPDATE_DATETIME)
  VALUES(1592, NULL, NULL, NULL, NULL, 'D3425', 26, 'SBI', '1', 110840, TO_TIMESTAMP('2014-01-14 16:25:21:0','YYYY-MM-DD HH24:MI:SS:FF'), NULL, NULL);
INSERT INTO attachment_requirement(ATTACHMENT_REQUIREMENT_ID, PLAN_TYPE, INS_COMPANY_ID, INSURANCE_PLAN, PROVIDER_TAXONOMY, PROCEDURE_CODE, REQUIREMENT_ID, VENDOR_TYPE, IS_ACTIVE, CREATE_EMPLOYEE_NUMBER, CREATE_DATETIME, UPDATE_EMPLOYEE_NUMBER, UPDATE_DATETIME)
  VALUES(1593, NULL, NULL, NULL, NULL, 'D3425', 60, 'SBI', '1', 110840, TO_TIMESTAMP('2014-01-14 16:25:21:0','YYYY-MM-DD HH24:MI:SS:FF'), NULL, NULL);
INSERT INTO attachment_requirement(ATTACHMENT_REQUIREMENT_ID, PLAN_TYPE, INS_COMPANY_ID, INSURANCE_PLAN, PROVIDER_TAXONOMY, PROCEDURE_CODE, REQUIREMENT_ID, VENDOR_TYPE, IS_ACTIVE, CREATE_EMPLOYEE_NUMBER, CREATE_DATETIME, UPDATE_EMPLOYEE_NUMBER, UPDATE_DATETIME)
  VALUES(1594, NULL, NULL, NULL, NULL, 'D3426', 26, 'SBI', '1', 110840, TO_TIMESTAMP('2014-01-14 16:25:21:0','YYYY-MM-DD HH24:MI:SS:FF'), NULL, NULL);
INSERT INTO attachment_requirement(ATTACHMENT_REQUIREMENT_ID, PLAN_TYPE, INS_COMPANY_ID, INSURANCE_PLAN, PROVIDER_TAXONOMY, PROCEDURE_CODE, REQUIREMENT_ID, VENDOR_TYPE, IS_ACTIVE, CREATE_EMPLOYEE_NUMBER, CREATE_DATETIME, UPDATE_EMPLOYEE_NUMBER, UPDATE_DATETIME)
  VALUES(1595, NULL, NULL, NULL, NULL, 'D3426', 60, 'SBI', '1', 110840, TO_TIMESTAMP('2014-01-14 16:25:21:0','YYYY-MM-DD HH24:MI:SS:FF'), NULL, NULL);
INSERT INTO attachment_requirement(ATTACHMENT_REQUIREMENT_ID, PLAN_TYPE, INS_COMPANY_ID, INSURANCE_PLAN, PROVIDER_TAXONOMY, PROCEDURE_CODE, REQUIREMENT_ID, VENDOR_TYPE, IS_ACTIVE, CREATE_EMPLOYEE_NUMBER, CREATE_DATETIME, UPDATE_EMPLOYEE_NUMBER, UPDATE_DATETIME)
  VALUES(1597, NULL, NULL, NULL, NULL, 'D3427', 18, 'SBI', '1', 110840, TO_TIMESTAMP('2014-01-14 16:25:21:0','YYYY-MM-DD HH24:MI:SS:FF'), NULL, NULL);
INSERT INTO attachment_requirement(ATTACHMENT_REQUIREMENT_ID, PLAN_TYPE, INS_COMPANY_ID, INSURANCE_PLAN, PROVIDER_TAXONOMY, PROCEDURE_CODE, REQUIREMENT_ID, VENDOR_TYPE, IS_ACTIVE, CREATE_EMPLOYEE_NUMBER, CREATE_DATETIME, UPDATE_EMPLOYEE_NUMBER, UPDATE_DATETIME)
  VALUES(1598, NULL, NULL, NULL, NULL, 'D3427', 26, 'SBI', '1', 110840, TO_TIMESTAMP('2014-01-14 16:25:21:0','YYYY-MM-DD HH24:MI:SS:FF'), NULL, NULL);
INSERT INTO attachment_requirement(ATTACHMENT_REQUIREMENT_ID, PLAN_TYPE, INS_COMPANY_ID, INSURANCE_PLAN, PROVIDER_TAXONOMY, PROCEDURE_CODE, REQUIREMENT_ID, VENDOR_TYPE, IS_ACTIVE, CREATE_EMPLOYEE_NUMBER, CREATE_DATETIME, UPDATE_EMPLOYEE_NUMBER, UPDATE_DATETIME)
  VALUES(1599, NULL, NULL, NULL, NULL, 'D3428', 18, 'SBI', '1', 110840, TO_TIMESTAMP('2014-01-14 16:25:21:0','YYYY-MM-DD HH24:MI:SS:FF'), NULL, NULL);
INSERT INTO attachment_requirement(ATTACHMENT_REQUIREMENT_ID, PLAN_TYPE, INS_COMPANY_ID, INSURANCE_PLAN, PROVIDER_TAXONOMY, PROCEDURE_CODE, REQUIREMENT_ID, VENDOR_TYPE, IS_ACTIVE, CREATE_EMPLOYEE_NUMBER, CREATE_DATETIME, UPDATE_EMPLOYEE_NUMBER, UPDATE_DATETIME)
  VALUES(1600, NULL, NULL, NULL, NULL, 'D3428', 26, 'SBI', '1', 110840, TO_TIMESTAMP('2014-01-14 16:25:21:0','YYYY-MM-DD HH24:MI:SS:FF'), NULL, NULL);
INSERT INTO attachment_requirement(ATTACHMENT_REQUIREMENT_ID, PLAN_TYPE, INS_COMPANY_ID, INSURANCE_PLAN, PROVIDER_TAXONOMY, PROCEDURE_CODE, REQUIREMENT_ID, VENDOR_TYPE, IS_ACTIVE, CREATE_EMPLOYEE_NUMBER, CREATE_DATETIME, UPDATE_EMPLOYEE_NUMBER, UPDATE_DATETIME)
  VALUES(1601, NULL, NULL, NULL, NULL, 'D3429', 18, 'SBI', '1', 110840, TO_TIMESTAMP('2014-01-14 16:25:21:0','YYYY-MM-DD HH24:MI:SS:FF'), NULL, NULL);
INSERT INTO attachment_requirement(ATTACHMENT_REQUIREMENT_ID, PLAN_TYPE, INS_COMPANY_ID, INSURANCE_PLAN, PROVIDER_TAXONOMY, PROCEDURE_CODE, REQUIREMENT_ID, VENDOR_TYPE, IS_ACTIVE, CREATE_EMPLOYEE_NUMBER, CREATE_DATETIME, UPDATE_EMPLOYEE_NUMBER, UPDATE_DATETIME)
  VALUES(1602, NULL, NULL, NULL, NULL, 'D3429', 26, 'SBI', '1', 110840, TO_TIMESTAMP('2014-01-14 16:25:21:0','YYYY-MM-DD HH24:MI:SS:FF'), NULL, NULL);
INSERT INTO attachment_requirement(ATTACHMENT_REQUIREMENT_ID, PLAN_TYPE, INS_COMPANY_ID, INSURANCE_PLAN, PROVIDER_TAXONOMY, PROCEDURE_CODE, REQUIREMENT_ID, VENDOR_TYPE, IS_ACTIVE, CREATE_EMPLOYEE_NUMBER, CREATE_DATETIME, UPDATE_EMPLOYEE_NUMBER, UPDATE_DATETIME)
  VALUES(1603, NULL, NULL, NULL, NULL, 'D3430', 17, 'SBI', '1', 110840, TO_TIMESTAMP('2014-01-14 16:25:21:0','YYYY-MM-DD HH24:MI:SS:FF'), NULL, NULL);
INSERT INTO attachment_requirement(ATTACHMENT_REQUIREMENT_ID, PLAN_TYPE, INS_COMPANY_ID, INSURANCE_PLAN, PROVIDER_TAXONOMY, PROCEDURE_CODE, REQUIREMENT_ID, VENDOR_TYPE, IS_ACTIVE, CREATE_EMPLOYEE_NUMBER, CREATE_DATETIME, UPDATE_EMPLOYEE_NUMBER, UPDATE_DATETIME)
  VALUES(1604, NULL, NULL, NULL, NULL, 'D3430', 60, 'SBI', '1', 110840, TO_TIMESTAMP('2014-01-14 16:25:21:0','YYYY-MM-DD HH24:MI:SS:FF'), NULL, NULL);
INSERT INTO attachment_requirement(ATTACHMENT_REQUIREMENT_ID, PLAN_TYPE, INS_COMPANY_ID, INSURANCE_PLAN, PROVIDER_TAXONOMY, PROCEDURE_CODE, REQUIREMENT_ID, VENDOR_TYPE, IS_ACTIVE, CREATE_EMPLOYEE_NUMBER, CREATE_DATETIME, UPDATE_EMPLOYEE_NUMBER, UPDATE_DATETIME)
  VALUES(1605, NULL, NULL, NULL, NULL, 'D3432', 26, 'SBI', '1', 110840, TO_TIMESTAMP('2014-01-14 16:25:21:0','YYYY-MM-DD HH24:MI:SS:FF'), NULL, NULL);
INSERT INTO attachment_requirement(ATTACHMENT_REQUIREMENT_ID, PLAN_TYPE, INS_COMPANY_ID, INSURANCE_PLAN, PROVIDER_TAXONOMY, PROCEDURE_CODE, REQUIREMENT_ID, VENDOR_TYPE, IS_ACTIVE, CREATE_EMPLOYEE_NUMBER, CREATE_DATETIME, UPDATE_EMPLOYEE_NUMBER, UPDATE_DATETIME)
  VALUES(1606, NULL, NULL, NULL, NULL, 'D3450', 26, 'SBI', '1', 110840, TO_TIMESTAMP('2014-01-14 16:25:21:0','YYYY-MM-DD HH24:MI:SS:FF'), NULL, NULL);
INSERT INTO attachment_requirement(ATTACHMENT_REQUIREMENT_ID, PLAN_TYPE, INS_COMPANY_ID, INSURANCE_PLAN, PROVIDER_TAXONOMY, PROCEDURE_CODE, REQUIREMENT_ID, VENDOR_TYPE, IS_ACTIVE, CREATE_EMPLOYEE_NUMBER, CREATE_DATETIME, UPDATE_EMPLOYEE_NUMBER, UPDATE_DATETIME)
  VALUES(1607, NULL, NULL, NULL, NULL, 'D3450', 60, 'SBI', '1', 110840, TO_TIMESTAMP('2014-01-14 16:25:21:0','YYYY-MM-DD HH24:MI:SS:FF'), NULL, NULL);
INSERT INTO attachment_requirement(ATTACHMENT_REQUIREMENT_ID, PLAN_TYPE, INS_COMPANY_ID, INSURANCE_PLAN, PROVIDER_TAXONOMY, PROCEDURE_CODE, REQUIREMENT_ID, VENDOR_TYPE, IS_ACTIVE, CREATE_EMPLOYEE_NUMBER, CREATE_DATETIME, UPDATE_EMPLOYEE_NUMBER, UPDATE_DATETIME)
  VALUES(1608, NULL, NULL, NULL, NULL, 'D3460', 26, 'SBI', '1', 110840, TO_TIMESTAMP('2014-01-14 16:25:21:0','YYYY-MM-DD HH24:MI:SS:FF'), NULL, NULL);
INSERT INTO attachment_requirement(ATTACHMENT_REQUIREMENT_ID, PLAN_TYPE, INS_COMPANY_ID, INSURANCE_PLAN, PROVIDER_TAXONOMY, PROCEDURE_CODE, REQUIREMENT_ID, VENDOR_TYPE, IS_ACTIVE, CREATE_EMPLOYEE_NUMBER, CREATE_DATETIME, UPDATE_EMPLOYEE_NUMBER, UPDATE_DATETIME)
  VALUES(1609, NULL, NULL, NULL, NULL, 'D3470', 26, 'SBI', '1', 110840, TO_TIMESTAMP('2014-01-14 16:25:21:0','YYYY-MM-DD HH24:MI:SS:FF'), NULL, NULL);
INSERT INTO attachment_requirement(ATTACHMENT_REQUIREMENT_ID, PLAN_TYPE, INS_COMPANY_ID, INSURANCE_PLAN, PROVIDER_TAXONOMY, PROCEDURE_CODE, REQUIREMENT_ID, VENDOR_TYPE, IS_ACTIVE, CREATE_EMPLOYEE_NUMBER, CREATE_DATETIME, UPDATE_EMPLOYEE_NUMBER, UPDATE_DATETIME)
  VALUES(1610, NULL, NULL, NULL, NULL, 'D3920', 17, 'SBI', '1', 110840, TO_TIMESTAMP('2014-01-14 16:25:21:0','YYYY-MM-DD HH24:MI:SS:FF'), NULL, NULL);
INSERT INTO attachment_requirement(ATTACHMENT_REQUIREMENT_ID, PLAN_TYPE, INS_COMPANY_ID, INSURANCE_PLAN, PROVIDER_TAXONOMY, PROCEDURE_CODE, REQUIREMENT_ID, VENDOR_TYPE, IS_ACTIVE, CREATE_EMPLOYEE_NUMBER, CREATE_DATETIME, UPDATE_EMPLOYEE_NUMBER, UPDATE_DATETIME)
  VALUES(1611, NULL, NULL, NULL, NULL, 'D3920', 60, 'SBI', '1', 110840, TO_TIMESTAMP('2014-01-14 16:25:21:0','YYYY-MM-DD HH24:MI:SS:FF'), NULL, NULL);
INSERT INTO attachment_requirement(ATTACHMENT_REQUIREMENT_ID, PLAN_TYPE, INS_COMPANY_ID, INSURANCE_PLAN, PROVIDER_TAXONOMY, PROCEDURE_CODE, REQUIREMENT_ID, VENDOR_TYPE, IS_ACTIVE, CREATE_EMPLOYEE_NUMBER, CREATE_DATETIME, UPDATE_EMPLOYEE_NUMBER, UPDATE_DATETIME)
  VALUES(1612, NULL, NULL, NULL, NULL, 'D3921', 26, 'SBI', '1', 110840, TO_TIMESTAMP('2014-01-14 16:25:21:0','YYYY-MM-DD HH24:MI:SS:FF'), NULL, NULL);
INSERT INTO attachment_requirement(ATTACHMENT_REQUIREMENT_ID, PLAN_TYPE, INS_COMPANY_ID, INSURANCE_PLAN, PROVIDER_TAXONOMY, PROCEDURE_CODE, REQUIREMENT_ID, VENDOR_TYPE, IS_ACTIVE, CREATE_EMPLOYEE_NUMBER, CREATE_DATETIME, UPDATE_EMPLOYEE_NUMBER, UPDATE_DATETIME)
  VALUES(1613, NULL, NULL, NULL, NULL, 'D3950', 60, 'SBI', '1', 110840, TO_TIMESTAMP('2014-01-14 16:25:21:0','YYYY-MM-DD HH24:MI:SS:FF'), NULL, NULL);
INSERT INTO attachment_requirement(ATTACHMENT_REQUIREMENT_ID, PLAN_TYPE, INS_COMPANY_ID, INSURANCE_PLAN, PROVIDER_TAXONOMY, PROCEDURE_CODE, REQUIREMENT_ID, VENDOR_TYPE, IS_ACTIVE, CREATE_EMPLOYEE_NUMBER, CREATE_DATETIME, UPDATE_EMPLOYEE_NUMBER, UPDATE_DATETIME)
  VALUES(1614, NULL, NULL, NULL, NULL, 'D3951', 26, 'SBI', '1', 110840, TO_TIMESTAMP('2014-01-14 16:25:21:0','YYYY-MM-DD HH24:MI:SS:FF'), NULL, NULL);
INSERT INTO attachment_requirement(ATTACHMENT_REQUIREMENT_ID, PLAN_TYPE, INS_COMPANY_ID, INSURANCE_PLAN, PROVIDER_TAXONOMY, PROCEDURE_CODE, REQUIREMENT_ID, VENDOR_TYPE, IS_ACTIVE, CREATE_EMPLOYEE_NUMBER, CREATE_DATETIME, UPDATE_EMPLOYEE_NUMBER, UPDATE_DATETIME)
  VALUES(1615, NULL, NULL, NULL, NULL, 'D3999', 26, 'SBI', '1', 110840, TO_TIMESTAMP('2014-01-14 16:25:21:0','YYYY-MM-DD HH24:MI:SS:FF'), NULL, NULL);
INSERT INTO attachment_requirement(ATTACHMENT_REQUIREMENT_ID, PLAN_TYPE, INS_COMPANY_ID, INSURANCE_PLAN, PROVIDER_TAXONOMY, PROCEDURE_CODE, REQUIREMENT_ID, VENDOR_TYPE, IS_ACTIVE, CREATE_EMPLOYEE_NUMBER, CREATE_DATETIME, UPDATE_EMPLOYEE_NUMBER, UPDATE_DATETIME)
  VALUES(1616, NULL, NULL, NULL, NULL, 'D4210', 1, 'SBI', '1', 110840, TO_TIMESTAMP('2014-01-14 16:25:21:0','YYYY-MM-DD HH24:MI:SS:FF'), NULL, NULL);
INSERT INTO attachment_requirement(ATTACHMENT_REQUIREMENT_ID, PLAN_TYPE, INS_COMPANY_ID, INSURANCE_PLAN, PROVIDER_TAXONOMY, PROCEDURE_CODE, REQUIREMENT_ID, VENDOR_TYPE, IS_ACTIVE, CREATE_EMPLOYEE_NUMBER, CREATE_DATETIME, UPDATE_EMPLOYEE_NUMBER, UPDATE_DATETIME)
  VALUES(1617, NULL, NULL, NULL, NULL, 'D4210', 18, 'SBI', '1', 110840, TO_TIMESTAMP('2014-01-14 16:25:21:0','YYYY-MM-DD HH24:MI:SS:FF'), NULL, NULL);
INSERT INTO attachment_requirement(ATTACHMENT_REQUIREMENT_ID, PLAN_TYPE, INS_COMPANY_ID, INSURANCE_PLAN, PROVIDER_TAXONOMY, PROCEDURE_CODE, REQUIREMENT_ID, VENDOR_TYPE, IS_ACTIVE, CREATE_EMPLOYEE_NUMBER, CREATE_DATETIME, UPDATE_EMPLOYEE_NUMBER, UPDATE_DATETIME)
  VALUES(1618, NULL, NULL, NULL, NULL, 'D4210', 26, 'SBI', '1', 110840, TO_TIMESTAMP('2014-01-14 16:25:21:0','YYYY-MM-DD HH24:MI:SS:FF'), NULL, NULL);
INSERT INTO attachment_requirement(ATTACHMENT_REQUIREMENT_ID, PLAN_TYPE, INS_COMPANY_ID, INSURANCE_PLAN, PROVIDER_TAXONOMY, PROCEDURE_CODE, REQUIREMENT_ID, VENDOR_TYPE, IS_ACTIVE, CREATE_EMPLOYEE_NUMBER, CREATE_DATETIME, UPDATE_EMPLOYEE_NUMBER, UPDATE_DATETIME)
  VALUES(1619, NULL, NULL, NULL, NULL, 'D4211', 1, 'SBI', '1', 110840, TO_TIMESTAMP('2014-01-14 16:25:21:0','YYYY-MM-DD HH24:MI:SS:FF'), NULL, NULL);
INSERT INTO attachment_requirement(ATTACHMENT_REQUIREMENT_ID, PLAN_TYPE, INS_COMPANY_ID, INSURANCE_PLAN, PROVIDER_TAXONOMY, PROCEDURE_CODE, REQUIREMENT_ID, VENDOR_TYPE, IS_ACTIVE, CREATE_EMPLOYEE_NUMBER, CREATE_DATETIME, UPDATE_EMPLOYEE_NUMBER, UPDATE_DATETIME)
  VALUES(1621, NULL, NULL, NULL, NULL, 'D4211', 26, 'SBI', '1', 110840, TO_TIMESTAMP('2014-01-14 16:25:21:0','YYYY-MM-DD HH24:MI:SS:FF'), NULL, NULL);
INSERT INTO attachment_requirement(ATTACHMENT_REQUIREMENT_ID, PLAN_TYPE, INS_COMPANY_ID, INSURANCE_PLAN, PROVIDER_TAXONOMY, PROCEDURE_CODE, REQUIREMENT_ID, VENDOR_TYPE, IS_ACTIVE, CREATE_EMPLOYEE_NUMBER, CREATE_DATETIME, UPDATE_EMPLOYEE_NUMBER, UPDATE_DATETIME)
  VALUES(1622, NULL, NULL, NULL, NULL, 'D4230', 1, 'SBI', '1', 110840, TO_TIMESTAMP('2014-01-14 16:25:21:0','YYYY-MM-DD HH24:MI:SS:FF'), NULL, NULL);
INSERT INTO attachment_requirement(ATTACHMENT_REQUIREMENT_ID, PLAN_TYPE, INS_COMPANY_ID, INSURANCE_PLAN, PROVIDER_TAXONOMY, PROCEDURE_CODE, REQUIREMENT_ID, VENDOR_TYPE, IS_ACTIVE, CREATE_EMPLOYEE_NUMBER, CREATE_DATETIME, UPDATE_EMPLOYEE_NUMBER, UPDATE_DATETIME)
  VALUES(1623, NULL, NULL, NULL, NULL, 'D4230', 18, 'SBI', '1', 110840, TO_TIMESTAMP('2014-01-14 16:25:21:0','YYYY-MM-DD HH24:MI:SS:FF'), NULL, NULL);
INSERT INTO attachment_requirement(ATTACHMENT_REQUIREMENT_ID, PLAN_TYPE, INS_COMPANY_ID, INSURANCE_PLAN, PROVIDER_TAXONOMY, PROCEDURE_CODE, REQUIREMENT_ID, VENDOR_TYPE, IS_ACTIVE, CREATE_EMPLOYEE_NUMBER, CREATE_DATETIME, UPDATE_EMPLOYEE_NUMBER, UPDATE_DATETIME)
  VALUES(1624, NULL, NULL, NULL, NULL, 'D4231', 1, 'SBI', '1', 110840, TO_TIMESTAMP('2014-01-14 16:25:21:0','YYYY-MM-DD HH24:MI:SS:FF'), NULL, NULL);
INSERT INTO attachment_requirement(ATTACHMENT_REQUIREMENT_ID, PLAN_TYPE, INS_COMPANY_ID, INSURANCE_PLAN, PROVIDER_TAXONOMY, PROCEDURE_CODE, REQUIREMENT_ID, VENDOR_TYPE, IS_ACTIVE, CREATE_EMPLOYEE_NUMBER, CREATE_DATETIME, UPDATE_EMPLOYEE_NUMBER, UPDATE_DATETIME)
  VALUES(1625, NULL, NULL, NULL, NULL, 'D4231', 18, 'SBI', '1', 110840, TO_TIMESTAMP('2014-01-14 16:25:21:0','YYYY-MM-DD HH24:MI:SS:FF'), NULL, NULL);
INSERT INTO attachment_requirement(ATTACHMENT_REQUIREMENT_ID, PLAN_TYPE, INS_COMPANY_ID, INSURANCE_PLAN, PROVIDER_TAXONOMY, PROCEDURE_CODE, REQUIREMENT_ID, VENDOR_TYPE, IS_ACTIVE, CREATE_EMPLOYEE_NUMBER, CREATE_DATETIME, UPDATE_EMPLOYEE_NUMBER, UPDATE_DATETIME)
  VALUES(1626, NULL, NULL, NULL, NULL, 'D4231', 26, 'SBI', '1', 110840, TO_TIMESTAMP('2014-01-14 16:25:21:0','YYYY-MM-DD HH24:MI:SS:FF'), NULL, NULL);
INSERT INTO attachment_requirement(ATTACHMENT_REQUIREMENT_ID, PLAN_TYPE, INS_COMPANY_ID, INSURANCE_PLAN, PROVIDER_TAXONOMY, PROCEDURE_CODE, REQUIREMENT_ID, VENDOR_TYPE, IS_ACTIVE, CREATE_EMPLOYEE_NUMBER, CREATE_DATETIME, UPDATE_EMPLOYEE_NUMBER, UPDATE_DATETIME)
  VALUES(1627, NULL, NULL, NULL, NULL, 'D4240', 1, 'SBI', '1', 110840, TO_TIMESTAMP('2014-01-14 16:25:21:0','YYYY-MM-DD HH24:MI:SS:FF'), NULL, NULL);
INSERT INTO attachment_requirement(ATTACHMENT_REQUIREMENT_ID, PLAN_TYPE, INS_COMPANY_ID, INSURANCE_PLAN, PROVIDER_TAXONOMY, PROCEDURE_CODE, REQUIREMENT_ID, VENDOR_TYPE, IS_ACTIVE, CREATE_EMPLOYEE_NUMBER, CREATE_DATETIME, UPDATE_EMPLOYEE_NUMBER, UPDATE_DATETIME)
  VALUES(1628, NULL, NULL, NULL, NULL, 'D4240', 18, 'SBI', '1', 110840, TO_TIMESTAMP('2014-01-14 16:25:21:0','YYYY-MM-DD HH24:MI:SS:FF'), NULL, NULL);
INSERT INTO attachment_requirement(ATTACHMENT_REQUIREMENT_ID, PLAN_TYPE, INS_COMPANY_ID, INSURANCE_PLAN, PROVIDER_TAXONOMY, PROCEDURE_CODE, REQUIREMENT_ID, VENDOR_TYPE, IS_ACTIVE, CREATE_EMPLOYEE_NUMBER, CREATE_DATETIME, UPDATE_EMPLOYEE_NUMBER, UPDATE_DATETIME)
  VALUES(1629, NULL, NULL, NULL, NULL, 'D4240', 26, 'SBI', '1', 110840, TO_TIMESTAMP('2014-01-14 16:25:21:0','YYYY-MM-DD HH24:MI:SS:FF'), NULL, NULL);
INSERT INTO attachment_requirement(ATTACHMENT_REQUIREMENT_ID, PLAN_TYPE, INS_COMPANY_ID, INSURANCE_PLAN, PROVIDER_TAXONOMY, PROCEDURE_CODE, REQUIREMENT_ID, VENDOR_TYPE, IS_ACTIVE, CREATE_EMPLOYEE_NUMBER, CREATE_DATETIME, UPDATE_EMPLOYEE_NUMBER, UPDATE_DATETIME)
  VALUES(1630, NULL, NULL, NULL, NULL, 'D4241', 1, 'SBI', '1', 110840, TO_TIMESTAMP('2014-01-14 16:25:21:0','YYYY-MM-DD HH24:MI:SS:FF'), NULL, NULL);
INSERT INTO attachment_requirement(ATTACHMENT_REQUIREMENT_ID, PLAN_TYPE, INS_COMPANY_ID, INSURANCE_PLAN, PROVIDER_TAXONOMY, PROCEDURE_CODE, REQUIREMENT_ID, VENDOR_TYPE, IS_ACTIVE, CREATE_EMPLOYEE_NUMBER, CREATE_DATETIME, UPDATE_EMPLOYEE_NUMBER, UPDATE_DATETIME)
  VALUES(1631, NULL, NULL, NULL, NULL, 'D4241', 18, 'SBI', '1', 110840, TO_TIMESTAMP('2014-01-14 16:25:21:0','YYYY-MM-DD HH24:MI:SS:FF'), NULL, NULL);
INSERT INTO attachment_requirement(ATTACHMENT_REQUIREMENT_ID, PLAN_TYPE, INS_COMPANY_ID, INSURANCE_PLAN, PROVIDER_TAXONOMY, PROCEDURE_CODE, REQUIREMENT_ID, VENDOR_TYPE, IS_ACTIVE, CREATE_EMPLOYEE_NUMBER, CREATE_DATETIME, UPDATE_EMPLOYEE_NUMBER, UPDATE_DATETIME)
  VALUES(1632, NULL, NULL, NULL, NULL, 'D4241', 26, 'SBI', '1', 110840, TO_TIMESTAMP('2014-01-14 16:25:21:0','YYYY-MM-DD HH24:MI:SS:FF'), NULL, NULL);
INSERT INTO attachment_requirement(ATTACHMENT_REQUIREMENT_ID, PLAN_TYPE, INS_COMPANY_ID, INSURANCE_PLAN, PROVIDER_TAXONOMY, PROCEDURE_CODE, REQUIREMENT_ID, VENDOR_TYPE, IS_ACTIVE, CREATE_EMPLOYEE_NUMBER, CREATE_DATETIME, UPDATE_EMPLOYEE_NUMBER, UPDATE_DATETIME)
  VALUES(1633, NULL, NULL, NULL, NULL, 'D4245', 1, 'SBI', '1', 110840, TO_TIMESTAMP('2014-01-14 16:25:21:0','YYYY-MM-DD HH24:MI:SS:FF'), NULL, NULL);
INSERT INTO attachment_requirement(ATTACHMENT_REQUIREMENT_ID, PLAN_TYPE, INS_COMPANY_ID, INSURANCE_PLAN, PROVIDER_TAXONOMY, PROCEDURE_CODE, REQUIREMENT_ID, VENDOR_TYPE, IS_ACTIVE, CREATE_EMPLOYEE_NUMBER, CREATE_DATETIME, UPDATE_EMPLOYEE_NUMBER, UPDATE_DATETIME)
  VALUES(1634, NULL, NULL, NULL, NULL, 'D4245', 26, 'SBI', '1', 110840, TO_TIMESTAMP('2014-01-14 16:25:21:0','YYYY-MM-DD HH24:MI:SS:FF'), NULL, NULL);
INSERT INTO attachment_requirement(ATTACHMENT_REQUIREMENT_ID, PLAN_TYPE, INS_COMPANY_ID, INSURANCE_PLAN, PROVIDER_TAXONOMY, PROCEDURE_CODE, REQUIREMENT_ID, VENDOR_TYPE, IS_ACTIVE, CREATE_EMPLOYEE_NUMBER, CREATE_DATETIME, UPDATE_EMPLOYEE_NUMBER, UPDATE_DATETIME)
  VALUES(1635, NULL, NULL, NULL, NULL, 'D4249', 1, 'SBI', '1', 110840, TO_TIMESTAMP('2014-01-14 16:25:21:0','YYYY-MM-DD HH24:MI:SS:FF'), NULL, NULL);
INSERT INTO attachment_requirement(ATTACHMENT_REQUIREMENT_ID, PLAN_TYPE, INS_COMPANY_ID, INSURANCE_PLAN, PROVIDER_TAXONOMY, PROCEDURE_CODE, REQUIREMENT_ID, VENDOR_TYPE, IS_ACTIVE, CREATE_EMPLOYEE_NUMBER, CREATE_DATETIME, UPDATE_EMPLOYEE_NUMBER, UPDATE_DATETIME)
  VALUES(1636, NULL, NULL, NULL, NULL, 'D4249', 2, 'SBI', '1', 110840, TO_TIMESTAMP('2014-01-14 16:25:21:0','YYYY-MM-DD HH24:MI:SS:FF'), NULL, NULL);
INSERT INTO attachment_requirement(ATTACHMENT_REQUIREMENT_ID, PLAN_TYPE, INS_COMPANY_ID, INSURANCE_PLAN, PROVIDER_TAXONOMY, PROCEDURE_CODE, REQUIREMENT_ID, VENDOR_TYPE, IS_ACTIVE, CREATE_EMPLOYEE_NUMBER, CREATE_DATETIME, UPDATE_EMPLOYEE_NUMBER, UPDATE_DATETIME)
  VALUES(1637, NULL, NULL, NULL, NULL, 'D4260', 26, 'SBI', '1', 110840, TO_TIMESTAMP('2014-01-14 16:25:21:0','YYYY-MM-DD HH24:MI:SS:FF'), NULL, NULL);
INSERT INTO attachment_requirement(ATTACHMENT_REQUIREMENT_ID, PLAN_TYPE, INS_COMPANY_ID, INSURANCE_PLAN, PROVIDER_TAXONOMY, PROCEDURE_CODE, REQUIREMENT_ID, VENDOR_TYPE, IS_ACTIVE, CREATE_EMPLOYEE_NUMBER, CREATE_DATETIME, UPDATE_EMPLOYEE_NUMBER, UPDATE_DATETIME)
  VALUES(1638, NULL, NULL, NULL, NULL, 'D4260', 1, 'SBI', '1', 110840, TO_TIMESTAMP('2014-01-14 16:25:21:0','YYYY-MM-DD HH24:MI:SS:FF'), NULL, NULL);
INSERT INTO attachment_requirement(ATTACHMENT_REQUIREMENT_ID, PLAN_TYPE, INS_COMPANY_ID, INSURANCE_PLAN, PROVIDER_TAXONOMY, PROCEDURE_CODE, REQUIREMENT_ID, VENDOR_TYPE, IS_ACTIVE, CREATE_EMPLOYEE_NUMBER, CREATE_DATETIME, UPDATE_EMPLOYEE_NUMBER, UPDATE_DATETIME)
  VALUES(1639, NULL, NULL, NULL, NULL, 'D4260', 18, 'SBI', '1', 110840, TO_TIMESTAMP('2014-01-14 16:25:21:0','YYYY-MM-DD HH24:MI:SS:FF'), NULL, NULL);
INSERT INTO attachment_requirement(ATTACHMENT_REQUIREMENT_ID, PLAN_TYPE, INS_COMPANY_ID, INSURANCE_PLAN, PROVIDER_TAXONOMY, PROCEDURE_CODE, REQUIREMENT_ID, VENDOR_TYPE, IS_ACTIVE, CREATE_EMPLOYEE_NUMBER, CREATE_DATETIME, UPDATE_EMPLOYEE_NUMBER, UPDATE_DATETIME)
  VALUES(1640, NULL, NULL, NULL, NULL, 'D4260', 45, 'SBI', '1', 110840, TO_TIMESTAMP('2014-01-14 16:25:21:0','YYYY-MM-DD HH24:MI:SS:FF'), NULL, NULL);
INSERT INTO attachment_requirement(ATTACHMENT_REQUIREMENT_ID, PLAN_TYPE, INS_COMPANY_ID, INSURANCE_PLAN, PROVIDER_TAXONOMY, PROCEDURE_CODE, REQUIREMENT_ID, VENDOR_TYPE, IS_ACTIVE, CREATE_EMPLOYEE_NUMBER, CREATE_DATETIME, UPDATE_EMPLOYEE_NUMBER, UPDATE_DATETIME)
  VALUES(1641, NULL, NULL, NULL, NULL, 'D4261', 26, 'SBI', '1', 110840, TO_TIMESTAMP('2014-01-14 16:25:21:0','YYYY-MM-DD HH24:MI:SS:FF'), NULL, NULL);
INSERT INTO attachment_requirement(ATTACHMENT_REQUIREMENT_ID, PLAN_TYPE, INS_COMPANY_ID, INSURANCE_PLAN, PROVIDER_TAXONOMY, PROCEDURE_CODE, REQUIREMENT_ID, VENDOR_TYPE, IS_ACTIVE, CREATE_EMPLOYEE_NUMBER, CREATE_DATETIME, UPDATE_EMPLOYEE_NUMBER, UPDATE_DATETIME)
  VALUES(1642, NULL, NULL, NULL, NULL, 'D4261', 1, 'SBI', '1', 110840, TO_TIMESTAMP('2014-01-14 16:25:21:0','YYYY-MM-DD HH24:MI:SS:FF'), NULL, NULL);
INSERT INTO attachment_requirement(ATTACHMENT_REQUIREMENT_ID, PLAN_TYPE, INS_COMPANY_ID, INSURANCE_PLAN, PROVIDER_TAXONOMY, PROCEDURE_CODE, REQUIREMENT_ID, VENDOR_TYPE, IS_ACTIVE, CREATE_EMPLOYEE_NUMBER, CREATE_DATETIME, UPDATE_EMPLOYEE_NUMBER, UPDATE_DATETIME)
  VALUES(1643, NULL, NULL, NULL, NULL, 'D4261', 18, 'SBI', '1', 110840, TO_TIMESTAMP('2014-01-14 16:25:21:0','YYYY-MM-DD HH24:MI:SS:FF'), NULL, NULL);
INSERT INTO attachment_requirement(ATTACHMENT_REQUIREMENT_ID, PLAN_TYPE, INS_COMPANY_ID, INSURANCE_PLAN, PROVIDER_TAXONOMY, PROCEDURE_CODE, REQUIREMENT_ID, VENDOR_TYPE, IS_ACTIVE, CREATE_EMPLOYEE_NUMBER, CREATE_DATETIME, UPDATE_EMPLOYEE_NUMBER, UPDATE_DATETIME)
  VALUES(1644, NULL, NULL, NULL, NULL, 'D4261', 45, 'SBI', '1', 110840, TO_TIMESTAMP('2014-01-14 16:25:21:0','YYYY-MM-DD HH24:MI:SS:FF'), NULL, NULL);
INSERT INTO attachment_requirement(ATTACHMENT_REQUIREMENT_ID, PLAN_TYPE, INS_COMPANY_ID, INSURANCE_PLAN, PROVIDER_TAXONOMY, PROCEDURE_CODE, REQUIREMENT_ID, VENDOR_TYPE, IS_ACTIVE, CREATE_EMPLOYEE_NUMBER, CREATE_DATETIME, UPDATE_EMPLOYEE_NUMBER, UPDATE_DATETIME)
  VALUES(1645, NULL, NULL, NULL, NULL, 'D4263', 1, 'SBI', '1', 110840, TO_TIMESTAMP('2014-01-14 16:25:21:0','YYYY-MM-DD HH24:MI:SS:FF'), NULL, NULL);
INSERT INTO attachment_requirement(ATTACHMENT_REQUIREMENT_ID, PLAN_TYPE, INS_COMPANY_ID, INSURANCE_PLAN, PROVIDER_TAXONOMY, PROCEDURE_CODE, REQUIREMENT_ID, VENDOR_TYPE, IS_ACTIVE, CREATE_EMPLOYEE_NUMBER, CREATE_DATETIME, UPDATE_EMPLOYEE_NUMBER, UPDATE_DATETIME)
  VALUES(1646, NULL, NULL, NULL, NULL, 'D4263', 18, 'SBI', '1', 110840, TO_TIMESTAMP('2014-01-14 16:25:21:0','YYYY-MM-DD HH24:MI:SS:FF'), NULL, NULL);
INSERT INTO attachment_requirement(ATTACHMENT_REQUIREMENT_ID, PLAN_TYPE, INS_COMPANY_ID, INSURANCE_PLAN, PROVIDER_TAXONOMY, PROCEDURE_CODE, REQUIREMENT_ID, VENDOR_TYPE, IS_ACTIVE, CREATE_EMPLOYEE_NUMBER, CREATE_DATETIME, UPDATE_EMPLOYEE_NUMBER, UPDATE_DATETIME)
  VALUES(1647, NULL, NULL, NULL, NULL, 'D4264', 26, 'SBI', '1', 110840, TO_TIMESTAMP('2014-01-14 16:25:21:0','YYYY-MM-DD HH24:MI:SS:FF'), NULL, NULL);
INSERT INTO attachment_requirement(ATTACHMENT_REQUIREMENT_ID, PLAN_TYPE, INS_COMPANY_ID, INSURANCE_PLAN, PROVIDER_TAXONOMY, PROCEDURE_CODE, REQUIREMENT_ID, VENDOR_TYPE, IS_ACTIVE, CREATE_EMPLOYEE_NUMBER, CREATE_DATETIME, UPDATE_EMPLOYEE_NUMBER, UPDATE_DATETIME)
  VALUES(1648, NULL, NULL, NULL, NULL, 'D4265', 1, 'SBI', '1', 110840, TO_TIMESTAMP('2014-01-14 16:25:21:0','YYYY-MM-DD HH24:MI:SS:FF'), NULL, NULL);
INSERT INTO attachment_requirement(ATTACHMENT_REQUIREMENT_ID, PLAN_TYPE, INS_COMPANY_ID, INSURANCE_PLAN, PROVIDER_TAXONOMY, PROCEDURE_CODE, REQUIREMENT_ID, VENDOR_TYPE, IS_ACTIVE, CREATE_EMPLOYEE_NUMBER, CREATE_DATETIME, UPDATE_EMPLOYEE_NUMBER, UPDATE_DATETIME)
  VALUES(1649, NULL, NULL, NULL, NULL, 'D4265', 18, 'SBI', '1', 110840, TO_TIMESTAMP('2014-01-14 16:25:21:0','YYYY-MM-DD HH24:MI:SS:FF'), NULL, NULL);
INSERT INTO attachment_requirement(ATTACHMENT_REQUIREMENT_ID, PLAN_TYPE, INS_COMPANY_ID, INSURANCE_PLAN, PROVIDER_TAXONOMY, PROCEDURE_CODE, REQUIREMENT_ID, VENDOR_TYPE, IS_ACTIVE, CREATE_EMPLOYEE_NUMBER, CREATE_DATETIME, UPDATE_EMPLOYEE_NUMBER, UPDATE_DATETIME)
  VALUES(1650, NULL, NULL, NULL, NULL, 'D4266', 1, 'SBI', '1', 110840, TO_TIMESTAMP('2014-01-14 16:25:21:0','YYYY-MM-DD HH24:MI:SS:FF'), NULL, NULL);
INSERT INTO attachment_requirement(ATTACHMENT_REQUIREMENT_ID, PLAN_TYPE, INS_COMPANY_ID, INSURANCE_PLAN, PROVIDER_TAXONOMY, PROCEDURE_CODE, REQUIREMENT_ID, VENDOR_TYPE, IS_ACTIVE, CREATE_EMPLOYEE_NUMBER, CREATE_DATETIME, UPDATE_EMPLOYEE_NUMBER, UPDATE_DATETIME)
  VALUES(1651, NULL, NULL, NULL, NULL, 'D4266', 18, 'SBI', '1', 110840, TO_TIMESTAMP('2014-01-14 16:25:21:0','YYYY-MM-DD HH24:MI:SS:FF'), NULL, NULL);
INSERT INTO attachment_requirement(ATTACHMENT_REQUIREMENT_ID, PLAN_TYPE, INS_COMPANY_ID, INSURANCE_PLAN, PROVIDER_TAXONOMY, PROCEDURE_CODE, REQUIREMENT_ID, VENDOR_TYPE, IS_ACTIVE, CREATE_EMPLOYEE_NUMBER, CREATE_DATETIME, UPDATE_EMPLOYEE_NUMBER, UPDATE_DATETIME)
  VALUES(1652, NULL, NULL, NULL, NULL, 'D4266', 26, 'SBI', '1', 110840, TO_TIMESTAMP('2014-01-14 16:25:21:0','YYYY-MM-DD HH24:MI:SS:FF'), NULL, NULL);
INSERT INTO attachment_requirement(ATTACHMENT_REQUIREMENT_ID, PLAN_TYPE, INS_COMPANY_ID, INSURANCE_PLAN, PROVIDER_TAXONOMY, PROCEDURE_CODE, REQUIREMENT_ID, VENDOR_TYPE, IS_ACTIVE, CREATE_EMPLOYEE_NUMBER, CREATE_DATETIME, UPDATE_EMPLOYEE_NUMBER, UPDATE_DATETIME)
  VALUES(1653, NULL, NULL, NULL, NULL, 'D4267', 1, 'SBI', '1', 110840, TO_TIMESTAMP('2014-01-14 16:25:21:0','YYYY-MM-DD HH24:MI:SS:FF'), NULL, NULL);
INSERT INTO attachment_requirement(ATTACHMENT_REQUIREMENT_ID, PLAN_TYPE, INS_COMPANY_ID, INSURANCE_PLAN, PROVIDER_TAXONOMY, PROCEDURE_CODE, REQUIREMENT_ID, VENDOR_TYPE, IS_ACTIVE, CREATE_EMPLOYEE_NUMBER, CREATE_DATETIME, UPDATE_EMPLOYEE_NUMBER, UPDATE_DATETIME)
  VALUES(1654, NULL, NULL, NULL, NULL, 'D4267', 18, 'SBI', '1', 110840, TO_TIMESTAMP('2014-01-14 16:25:21:0','YYYY-MM-DD HH24:MI:SS:FF'), NULL, NULL);
INSERT INTO attachment_requirement(ATTACHMENT_REQUIREMENT_ID, PLAN_TYPE, INS_COMPANY_ID, INSURANCE_PLAN, PROVIDER_TAXONOMY, PROCEDURE_CODE, REQUIREMENT_ID, VENDOR_TYPE, IS_ACTIVE, CREATE_EMPLOYEE_NUMBER, CREATE_DATETIME, UPDATE_EMPLOYEE_NUMBER, UPDATE_DATETIME)
  VALUES(1655, NULL, NULL, NULL, NULL, 'D4267', 26, 'SBI', '1', 110840, TO_TIMESTAMP('2014-01-14 16:25:21:0','YYYY-MM-DD HH24:MI:SS:FF'), NULL, NULL);
INSERT INTO attachment_requirement(ATTACHMENT_REQUIREMENT_ID, PLAN_TYPE, INS_COMPANY_ID, INSURANCE_PLAN, PROVIDER_TAXONOMY, PROCEDURE_CODE, REQUIREMENT_ID, VENDOR_TYPE, IS_ACTIVE, CREATE_EMPLOYEE_NUMBER, CREATE_DATETIME, UPDATE_EMPLOYEE_NUMBER, UPDATE_DATETIME)
  VALUES(1656, NULL, NULL, NULL, NULL, 'D4268', 1, 'SBI', '1', 110840, TO_TIMESTAMP('2014-01-14 16:25:21:0','YYYY-MM-DD HH24:MI:SS:FF'), NULL, NULL);
INSERT INTO attachment_requirement(ATTACHMENT_REQUIREMENT_ID, PLAN_TYPE, INS_COMPANY_ID, INSURANCE_PLAN, PROVIDER_TAXONOMY, PROCEDURE_CODE, REQUIREMENT_ID, VENDOR_TYPE, IS_ACTIVE, CREATE_EMPLOYEE_NUMBER, CREATE_DATETIME, UPDATE_EMPLOYEE_NUMBER, UPDATE_DATETIME)
  VALUES(1657, NULL, NULL, NULL, NULL, 'D4268', 18, 'SBI', '1', 110840, TO_TIMESTAMP('2014-01-14 16:25:21:0','YYYY-MM-DD HH24:MI:SS:FF'), NULL, NULL);
INSERT INTO attachment_requirement(ATTACHMENT_REQUIREMENT_ID, PLAN_TYPE, INS_COMPANY_ID, INSURANCE_PLAN, PROVIDER_TAXONOMY, PROCEDURE_CODE, REQUIREMENT_ID, VENDOR_TYPE, IS_ACTIVE, CREATE_EMPLOYEE_NUMBER, CREATE_DATETIME, UPDATE_EMPLOYEE_NUMBER, UPDATE_DATETIME)
  VALUES(1658, NULL, NULL, NULL, NULL, 'D4268', 26, 'SBI', '1', 110840, TO_TIMESTAMP('2014-01-14 16:25:21:0','YYYY-MM-DD HH24:MI:SS:FF'), NULL, NULL);
INSERT INTO attachment_requirement(ATTACHMENT_REQUIREMENT_ID, PLAN_TYPE, INS_COMPANY_ID, INSURANCE_PLAN, PROVIDER_TAXONOMY, PROCEDURE_CODE, REQUIREMENT_ID, VENDOR_TYPE, IS_ACTIVE, CREATE_EMPLOYEE_NUMBER, CREATE_DATETIME, UPDATE_EMPLOYEE_NUMBER, UPDATE_DATETIME)
  VALUES(1660, NULL, NULL, NULL, NULL, 'D4270', 1, 'SBI', '1', 110840, TO_TIMESTAMP('2014-01-14 16:25:21:0','YYYY-MM-DD HH24:MI:SS:FF'), NULL, NULL);
INSERT INTO attachment_requirement(ATTACHMENT_REQUIREMENT_ID, PLAN_TYPE, INS_COMPANY_ID, INSURANCE_PLAN, PROVIDER_TAXONOMY, PROCEDURE_CODE, REQUIREMENT_ID, VENDOR_TYPE, IS_ACTIVE, CREATE_EMPLOYEE_NUMBER, CREATE_DATETIME, UPDATE_EMPLOYEE_NUMBER, UPDATE_DATETIME)
  VALUES(1661, NULL, NULL, NULL, NULL, 'D4270', 26, 'SBI', '1', 110840, TO_TIMESTAMP('2014-01-14 16:25:21:0','YYYY-MM-DD HH24:MI:SS:FF'), NULL, NULL);
INSERT INTO attachment_requirement(ATTACHMENT_REQUIREMENT_ID, PLAN_TYPE, INS_COMPANY_ID, INSURANCE_PLAN, PROVIDER_TAXONOMY, PROCEDURE_CODE, REQUIREMENT_ID, VENDOR_TYPE, IS_ACTIVE, CREATE_EMPLOYEE_NUMBER, CREATE_DATETIME, UPDATE_EMPLOYEE_NUMBER, UPDATE_DATETIME)
  VALUES(1663, NULL, NULL, NULL, NULL, 'D4271', 1, 'SBI', '1', 110840, TO_TIMESTAMP('2014-01-14 16:25:21:0','YYYY-MM-DD HH24:MI:SS:FF'), NULL, NULL);
INSERT INTO attachment_requirement(ATTACHMENT_REQUIREMENT_ID, PLAN_TYPE, INS_COMPANY_ID, INSURANCE_PLAN, PROVIDER_TAXONOMY, PROCEDURE_CODE, REQUIREMENT_ID, VENDOR_TYPE, IS_ACTIVE, CREATE_EMPLOYEE_NUMBER, CREATE_DATETIME, UPDATE_EMPLOYEE_NUMBER, UPDATE_DATETIME)
  VALUES(1664, NULL, NULL, NULL, NULL, 'D4271', 26, 'SBI', '1', 110840, TO_TIMESTAMP('2014-01-14 16:25:21:0','YYYY-MM-DD HH24:MI:SS:FF'), NULL, NULL);
INSERT INTO attachment_requirement(ATTACHMENT_REQUIREMENT_ID, PLAN_TYPE, INS_COMPANY_ID, INSURANCE_PLAN, PROVIDER_TAXONOMY, PROCEDURE_CODE, REQUIREMENT_ID, VENDOR_TYPE, IS_ACTIVE, CREATE_EMPLOYEE_NUMBER, CREATE_DATETIME, UPDATE_EMPLOYEE_NUMBER, UPDATE_DATETIME)
  VALUES(1665, NULL, NULL, NULL, NULL, 'D4273', 1, 'SBI', '1', 110840, TO_TIMESTAMP('2014-01-14 16:25:21:0','YYYY-MM-DD HH24:MI:SS:FF'), NULL, NULL);
INSERT INTO attachment_requirement(ATTACHMENT_REQUIREMENT_ID, PLAN_TYPE, INS_COMPANY_ID, INSURANCE_PLAN, PROVIDER_TAXONOMY, PROCEDURE_CODE, REQUIREMENT_ID, VENDOR_TYPE, IS_ACTIVE, CREATE_EMPLOYEE_NUMBER, CREATE_DATETIME, UPDATE_EMPLOYEE_NUMBER, UPDATE_DATETIME)
  VALUES(1666, NULL, NULL, NULL, NULL, 'D4273', 18, 'SBI', '1', 110840, TO_TIMESTAMP('2014-01-14 16:25:21:0','YYYY-MM-DD HH24:MI:SS:FF'), NULL, NULL);
INSERT INTO attachment_requirement(ATTACHMENT_REQUIREMENT_ID, PLAN_TYPE, INS_COMPANY_ID, INSURANCE_PLAN, PROVIDER_TAXONOMY, PROCEDURE_CODE, REQUIREMENT_ID, VENDOR_TYPE, IS_ACTIVE, CREATE_EMPLOYEE_NUMBER, CREATE_DATETIME, UPDATE_EMPLOYEE_NUMBER, UPDATE_DATETIME)
  VALUES(1667, NULL, NULL, NULL, NULL, 'D4273', 26, 'SBI', '1', 110840, TO_TIMESTAMP('2014-01-14 16:25:21:0','YYYY-MM-DD HH24:MI:SS:FF'), NULL, NULL);
INSERT INTO attachment_requirement(ATTACHMENT_REQUIREMENT_ID, PLAN_TYPE, INS_COMPANY_ID, INSURANCE_PLAN, PROVIDER_TAXONOMY, PROCEDURE_CODE, REQUIREMENT_ID, VENDOR_TYPE, IS_ACTIVE, CREATE_EMPLOYEE_NUMBER, CREATE_DATETIME, UPDATE_EMPLOYEE_NUMBER, UPDATE_DATETIME)
  VALUES(1668, NULL, NULL, NULL, NULL, 'D4274', 1, 'SBI', '1', 110840, TO_TIMESTAMP('2014-01-14 16:25:21:0','YYYY-MM-DD HH24:MI:SS:FF'), NULL, NULL);
INSERT INTO attachment_requirement(ATTACHMENT_REQUIREMENT_ID, PLAN_TYPE, INS_COMPANY_ID, INSURANCE_PLAN, PROVIDER_TAXONOMY, PROCEDURE_CODE, REQUIREMENT_ID, VENDOR_TYPE, IS_ACTIVE, CREATE_EMPLOYEE_NUMBER, CREATE_DATETIME, UPDATE_EMPLOYEE_NUMBER, UPDATE_DATETIME)
  VALUES(1669, NULL, NULL, NULL, NULL, 'D4274', 18, 'SBI', '1', 110840, TO_TIMESTAMP('2014-01-14 16:25:21:0','YYYY-MM-DD HH24:MI:SS:FF'), NULL, NULL);
INSERT INTO attachment_requirement(ATTACHMENT_REQUIREMENT_ID, PLAN_TYPE, INS_COMPANY_ID, INSURANCE_PLAN, PROVIDER_TAXONOMY, PROCEDURE_CODE, REQUIREMENT_ID, VENDOR_TYPE, IS_ACTIVE, CREATE_EMPLOYEE_NUMBER, CREATE_DATETIME, UPDATE_EMPLOYEE_NUMBER, UPDATE_DATETIME)
  VALUES(1670, NULL, NULL, NULL, NULL, 'D4275', 1, 'SBI', '1', 110840, TO_TIMESTAMP('2014-01-14 16:25:21:0','YYYY-MM-DD HH24:MI:SS:FF'), NULL, NULL);
INSERT INTO attachment_requirement(ATTACHMENT_REQUIREMENT_ID, PLAN_TYPE, INS_COMPANY_ID, INSURANCE_PLAN, PROVIDER_TAXONOMY, PROCEDURE_CODE, REQUIREMENT_ID, VENDOR_TYPE, IS_ACTIVE, CREATE_EMPLOYEE_NUMBER, CREATE_DATETIME, UPDATE_EMPLOYEE_NUMBER, UPDATE_DATETIME)
  VALUES(1671, NULL, NULL, NULL, NULL, 'D4275', 18, 'SBI', '1', 110840, TO_TIMESTAMP('2014-01-14 16:25:21:0','YYYY-MM-DD HH24:MI:SS:FF'), NULL, NULL);
INSERT INTO attachment_requirement(ATTACHMENT_REQUIREMENT_ID, PLAN_TYPE, INS_COMPANY_ID, INSURANCE_PLAN, PROVIDER_TAXONOMY, PROCEDURE_CODE, REQUIREMENT_ID, VENDOR_TYPE, IS_ACTIVE, CREATE_EMPLOYEE_NUMBER, CREATE_DATETIME, UPDATE_EMPLOYEE_NUMBER, UPDATE_DATETIME)
  VALUES(1672, NULL, NULL, NULL, NULL, 'D4275', 26, 'SBI', '1', 110840, TO_TIMESTAMP('2014-01-14 16:25:21:0','YYYY-MM-DD HH24:MI:SS:FF'), NULL, NULL);
INSERT INTO attachment_requirement(ATTACHMENT_REQUIREMENT_ID, PLAN_TYPE, INS_COMPANY_ID, INSURANCE_PLAN, PROVIDER_TAXONOMY, PROCEDURE_CODE, REQUIREMENT_ID, VENDOR_TYPE, IS_ACTIVE, CREATE_EMPLOYEE_NUMBER, CREATE_DATETIME, UPDATE_EMPLOYEE_NUMBER, UPDATE_DATETIME)
  VALUES(1673, NULL, NULL, NULL, NULL, 'D4276', 1, 'SBI', '1', 110840, TO_TIMESTAMP('2014-01-14 16:25:21:0','YYYY-MM-DD HH24:MI:SS:FF'), NULL, NULL);
INSERT INTO attachment_requirement(ATTACHMENT_REQUIREMENT_ID, PLAN_TYPE, INS_COMPANY_ID, INSURANCE_PLAN, PROVIDER_TAXONOMY, PROCEDURE_CODE, REQUIREMENT_ID, VENDOR_TYPE, IS_ACTIVE, CREATE_EMPLOYEE_NUMBER, CREATE_DATETIME, UPDATE_EMPLOYEE_NUMBER, UPDATE_DATETIME)
  VALUES(1674, NULL, NULL, NULL, NULL, 'D4276', 18, 'SBI', '1', 110840, TO_TIMESTAMP('2014-01-14 16:25:21:0','YYYY-MM-DD HH24:MI:SS:FF'), NULL, NULL);
INSERT INTO attachment_requirement(ATTACHMENT_REQUIREMENT_ID, PLAN_TYPE, INS_COMPANY_ID, INSURANCE_PLAN, PROVIDER_TAXONOMY, PROCEDURE_CODE, REQUIREMENT_ID, VENDOR_TYPE, IS_ACTIVE, CREATE_EMPLOYEE_NUMBER, CREATE_DATETIME, UPDATE_EMPLOYEE_NUMBER, UPDATE_DATETIME)
  VALUES(1675, NULL, NULL, NULL, NULL, 'D4276', 26, 'SBI', '1', 110840, TO_TIMESTAMP('2014-01-14 16:25:21:0','YYYY-MM-DD HH24:MI:SS:FF'), NULL, NULL);
INSERT INTO attachment_requirement(ATTACHMENT_REQUIREMENT_ID, PLAN_TYPE, INS_COMPANY_ID, INSURANCE_PLAN, PROVIDER_TAXONOMY, PROCEDURE_CODE, REQUIREMENT_ID, VENDOR_TYPE, IS_ACTIVE, CREATE_EMPLOYEE_NUMBER, CREATE_DATETIME, UPDATE_EMPLOYEE_NUMBER, UPDATE_DATETIME)
  VALUES(1676, NULL, NULL, NULL, NULL, 'D4277', 1, 'SBI', '1', 110840, TO_TIMESTAMP('2014-01-14 16:25:21:0','YYYY-MM-DD HH24:MI:SS:FF'), NULL, NULL);
INSERT INTO attachment_requirement(ATTACHMENT_REQUIREMENT_ID, PLAN_TYPE, INS_COMPANY_ID, INSURANCE_PLAN, PROVIDER_TAXONOMY, PROCEDURE_CODE, REQUIREMENT_ID, VENDOR_TYPE, IS_ACTIVE, CREATE_EMPLOYEE_NUMBER, CREATE_DATETIME, UPDATE_EMPLOYEE_NUMBER, UPDATE_DATETIME)
  VALUES(1677, NULL, NULL, NULL, NULL, 'D4277', 26, 'SBI', '1', 110840, TO_TIMESTAMP('2014-01-14 16:25:21:0','YYYY-MM-DD HH24:MI:SS:FF'), NULL, NULL);
INSERT INTO attachment_requirement(ATTACHMENT_REQUIREMENT_ID, PLAN_TYPE, INS_COMPANY_ID, INSURANCE_PLAN, PROVIDER_TAXONOMY, PROCEDURE_CODE, REQUIREMENT_ID, VENDOR_TYPE, IS_ACTIVE, CREATE_EMPLOYEE_NUMBER, CREATE_DATETIME, UPDATE_EMPLOYEE_NUMBER, UPDATE_DATETIME)
  VALUES(1678, NULL, NULL, NULL, NULL, 'D4320', 26, 'SBI', '1', 110840, TO_TIMESTAMP('2014-01-14 16:25:21:0','YYYY-MM-DD HH24:MI:SS:FF'), NULL, NULL);
INSERT INTO attachment_requirement(ATTACHMENT_REQUIREMENT_ID, PLAN_TYPE, INS_COMPANY_ID, INSURANCE_PLAN, PROVIDER_TAXONOMY, PROCEDURE_CODE, REQUIREMENT_ID, VENDOR_TYPE, IS_ACTIVE, CREATE_EMPLOYEE_NUMBER, CREATE_DATETIME, UPDATE_EMPLOYEE_NUMBER, UPDATE_DATETIME)
  VALUES(1679, NULL, NULL, NULL, NULL, 'D4321', 26, 'SBI', '1', 110840, TO_TIMESTAMP('2014-01-14 16:25:21:0','YYYY-MM-DD HH24:MI:SS:FF'), NULL, NULL);
INSERT INTO attachment_requirement(ATTACHMENT_REQUIREMENT_ID, PLAN_TYPE, INS_COMPANY_ID, INSURANCE_PLAN, PROVIDER_TAXONOMY, PROCEDURE_CODE, REQUIREMENT_ID, VENDOR_TYPE, IS_ACTIVE, CREATE_EMPLOYEE_NUMBER, CREATE_DATETIME, UPDATE_EMPLOYEE_NUMBER, UPDATE_DATETIME)
  VALUES(1680, NULL, NULL, NULL, NULL, 'D4341', 1, 'SBI', '1', 110840, TO_TIMESTAMP('2014-01-14 16:25:21:0','YYYY-MM-DD HH24:MI:SS:FF'), NULL, NULL);
INSERT INTO attachment_requirement(ATTACHMENT_REQUIREMENT_ID, PLAN_TYPE, INS_COMPANY_ID, INSURANCE_PLAN, PROVIDER_TAXONOMY, PROCEDURE_CODE, REQUIREMENT_ID, VENDOR_TYPE, IS_ACTIVE, CREATE_EMPLOYEE_NUMBER, CREATE_DATETIME, UPDATE_EMPLOYEE_NUMBER, UPDATE_DATETIME)
  VALUES(1681, NULL, NULL, NULL, NULL, 'D4341', 18, 'SBI', '1', 110840, TO_TIMESTAMP('2014-01-14 16:25:21:0','YYYY-MM-DD HH24:MI:SS:FF'), NULL, NULL);
INSERT INTO attachment_requirement(ATTACHMENT_REQUIREMENT_ID, PLAN_TYPE, INS_COMPANY_ID, INSURANCE_PLAN, PROVIDER_TAXONOMY, PROCEDURE_CODE, REQUIREMENT_ID, VENDOR_TYPE, IS_ACTIVE, CREATE_EMPLOYEE_NUMBER, CREATE_DATETIME, UPDATE_EMPLOYEE_NUMBER, UPDATE_DATETIME)
  VALUES(1682, NULL, NULL, NULL, NULL, 'D4342', 1, 'SBI', '1', 110840, TO_TIMESTAMP('2014-01-14 16:25:21:0','YYYY-MM-DD HH24:MI:SS:FF'), NULL, NULL);
INSERT INTO attachment_requirement(ATTACHMENT_REQUIREMENT_ID, PLAN_TYPE, INS_COMPANY_ID, INSURANCE_PLAN, PROVIDER_TAXONOMY, PROCEDURE_CODE, REQUIREMENT_ID, VENDOR_TYPE, IS_ACTIVE, CREATE_EMPLOYEE_NUMBER, CREATE_DATETIME, UPDATE_EMPLOYEE_NUMBER, UPDATE_DATETIME)
  VALUES(1683, NULL, NULL, NULL, NULL, 'D4342', 18, 'SBI', '1', 110840, TO_TIMESTAMP('2014-01-14 16:25:21:0','YYYY-MM-DD HH24:MI:SS:FF'), NULL, NULL);
INSERT INTO attachment_requirement(ATTACHMENT_REQUIREMENT_ID, PLAN_TYPE, INS_COMPANY_ID, INSURANCE_PLAN, PROVIDER_TAXONOMY, PROCEDURE_CODE, REQUIREMENT_ID, VENDOR_TYPE, IS_ACTIVE, CREATE_EMPLOYEE_NUMBER, CREATE_DATETIME, UPDATE_EMPLOYEE_NUMBER, UPDATE_DATETIME)
  VALUES(1684, NULL, NULL, NULL, NULL, 'D4355', 26, 'SBI', '1', 110840, TO_TIMESTAMP('2014-01-14 16:25:21:0','YYYY-MM-DD HH24:MI:SS:FF'), NULL, NULL);
INSERT INTO attachment_requirement(ATTACHMENT_REQUIREMENT_ID, PLAN_TYPE, INS_COMPANY_ID, INSURANCE_PLAN, PROVIDER_TAXONOMY, PROCEDURE_CODE, REQUIREMENT_ID, VENDOR_TYPE, IS_ACTIVE, CREATE_EMPLOYEE_NUMBER, CREATE_DATETIME, UPDATE_EMPLOYEE_NUMBER, UPDATE_DATETIME)
  VALUES(1685, NULL, NULL, NULL, NULL, 'D4381', 1, 'SBI', '1', 110840, TO_TIMESTAMP('2014-01-14 16:25:21:0','YYYY-MM-DD HH24:MI:SS:FF'), NULL, NULL);
INSERT INTO attachment_requirement(ATTACHMENT_REQUIREMENT_ID, PLAN_TYPE, INS_COMPANY_ID, INSURANCE_PLAN, PROVIDER_TAXONOMY, PROCEDURE_CODE, REQUIREMENT_ID, VENDOR_TYPE, IS_ACTIVE, CREATE_EMPLOYEE_NUMBER, CREATE_DATETIME, UPDATE_EMPLOYEE_NUMBER, UPDATE_DATETIME)
  VALUES(1686, NULL, NULL, NULL, NULL, 'D4381', 18, 'SBI', '1', 110840, TO_TIMESTAMP('2014-01-14 16:25:21:0','YYYY-MM-DD HH24:MI:SS:FF'), NULL, NULL);
INSERT INTO attachment_requirement(ATTACHMENT_REQUIREMENT_ID, PLAN_TYPE, INS_COMPANY_ID, INSURANCE_PLAN, PROVIDER_TAXONOMY, PROCEDURE_CODE, REQUIREMENT_ID, VENDOR_TYPE, IS_ACTIVE, CREATE_EMPLOYEE_NUMBER, CREATE_DATETIME, UPDATE_EMPLOYEE_NUMBER, UPDATE_DATETIME)
  VALUES(1687, NULL, NULL, NULL, NULL, 'D4381', 26, 'SBI', '1', 110840, TO_TIMESTAMP('2014-01-14 16:25:21:0','YYYY-MM-DD HH24:MI:SS:FF'), NULL, NULL);
INSERT INTO attachment_requirement(ATTACHMENT_REQUIREMENT_ID, PLAN_TYPE, INS_COMPANY_ID, INSURANCE_PLAN, PROVIDER_TAXONOMY, PROCEDURE_CODE, REQUIREMENT_ID, VENDOR_TYPE, IS_ACTIVE, CREATE_EMPLOYEE_NUMBER, CREATE_DATETIME, UPDATE_EMPLOYEE_NUMBER, UPDATE_DATETIME)
  VALUES(1688, NULL, NULL, NULL, NULL, 'D4910', 1, 'SBI', '1', 110840, TO_TIMESTAMP('2014-01-14 16:25:21:0','YYYY-MM-DD HH24:MI:SS:FF'), NULL, NULL);
INSERT INTO attachment_requirement(ATTACHMENT_REQUIREMENT_ID, PLAN_TYPE, INS_COMPANY_ID, INSURANCE_PLAN, PROVIDER_TAXONOMY, PROCEDURE_CODE, REQUIREMENT_ID, VENDOR_TYPE, IS_ACTIVE, CREATE_EMPLOYEE_NUMBER, CREATE_DATETIME, UPDATE_EMPLOYEE_NUMBER, UPDATE_DATETIME)
  VALUES(1689, NULL, NULL, NULL, NULL, 'D4910', 45, 'SBI', '1', 110840, TO_TIMESTAMP('2014-01-14 16:25:21:0','YYYY-MM-DD HH24:MI:SS:FF'), NULL, NULL);
INSERT INTO attachment_requirement(ATTACHMENT_REQUIREMENT_ID, PLAN_TYPE, INS_COMPANY_ID, INSURANCE_PLAN, PROVIDER_TAXONOMY, PROCEDURE_CODE, REQUIREMENT_ID, VENDOR_TYPE, IS_ACTIVE, CREATE_EMPLOYEE_NUMBER, CREATE_DATETIME, UPDATE_EMPLOYEE_NUMBER, UPDATE_DATETIME)
  VALUES(1690, NULL, NULL, NULL, NULL, 'D4920', 26, 'SBI', '1', 110840, TO_TIMESTAMP('2014-01-14 16:25:21:0','YYYY-MM-DD HH24:MI:SS:FF'), NULL, NULL);
INSERT INTO attachment_requirement(ATTACHMENT_REQUIREMENT_ID, PLAN_TYPE, INS_COMPANY_ID, INSURANCE_PLAN, PROVIDER_TAXONOMY, PROCEDURE_CODE, REQUIREMENT_ID, VENDOR_TYPE, IS_ACTIVE, CREATE_EMPLOYEE_NUMBER, CREATE_DATETIME, UPDATE_EMPLOYEE_NUMBER, UPDATE_DATETIME)
  VALUES(1691, NULL, NULL, NULL, NULL, 'D4999', 26, 'SBI', '1', 110840, TO_TIMESTAMP('2014-01-14 16:25:21:0','YYYY-MM-DD HH24:MI:SS:FF'), NULL, NULL);
INSERT INTO attachment_requirement(ATTACHMENT_REQUIREMENT_ID, PLAN_TYPE, INS_COMPANY_ID, INSURANCE_PLAN, PROVIDER_TAXONOMY, PROCEDURE_CODE, REQUIREMENT_ID, VENDOR_TYPE, IS_ACTIVE, CREATE_EMPLOYEE_NUMBER, CREATE_DATETIME, UPDATE_EMPLOYEE_NUMBER, UPDATE_DATETIME)
  VALUES(1696, NULL, NULL, NULL, NULL, 'D4999', 1, 'SBI', '1', 110840, TO_TIMESTAMP('2014-01-14 16:25:21:0','YYYY-MM-DD HH24:MI:SS:FF'), NULL, NULL);
INSERT INTO attachment_requirement(ATTACHMENT_REQUIREMENT_ID, PLAN_TYPE, INS_COMPANY_ID, INSURANCE_PLAN, PROVIDER_TAXONOMY, PROCEDURE_CODE, REQUIREMENT_ID, VENDOR_TYPE, IS_ACTIVE, CREATE_EMPLOYEE_NUMBER, CREATE_DATETIME, UPDATE_EMPLOYEE_NUMBER, UPDATE_DATETIME)
  VALUES(1699, NULL, NULL, NULL, NULL, 'D5110', 18, 'SBI', '1', 110840, TO_TIMESTAMP('2014-01-14 16:25:21:0','YYYY-MM-DD HH24:MI:SS:FF'), NULL, NULL);
INSERT INTO attachment_requirement(ATTACHMENT_REQUIREMENT_ID, PLAN_TYPE, INS_COMPANY_ID, INSURANCE_PLAN, PROVIDER_TAXONOMY, PROCEDURE_CODE, REQUIREMENT_ID, VENDOR_TYPE, IS_ACTIVE, CREATE_EMPLOYEE_NUMBER, CREATE_DATETIME, UPDATE_EMPLOYEE_NUMBER, UPDATE_DATETIME)
  VALUES(1702, NULL, NULL, NULL, NULL, 'D5120', 18, 'SBI', '1', 110840, TO_TIMESTAMP('2014-01-14 16:25:21:0','YYYY-MM-DD HH24:MI:SS:FF'), NULL, NULL);
INSERT INTO attachment_requirement(ATTACHMENT_REQUIREMENT_ID, PLAN_TYPE, INS_COMPANY_ID, INSURANCE_PLAN, PROVIDER_TAXONOMY, PROCEDURE_CODE, REQUIREMENT_ID, VENDOR_TYPE, IS_ACTIVE, CREATE_EMPLOYEE_NUMBER, CREATE_DATETIME, UPDATE_EMPLOYEE_NUMBER, UPDATE_DATETIME)
  VALUES(1705, NULL, NULL, NULL, NULL, 'D5120', 26, 'SBI', '1', 110840, TO_TIMESTAMP('2014-01-14 16:25:21:0','YYYY-MM-DD HH24:MI:SS:FF'), NULL, NULL);
INSERT INTO attachment_requirement(ATTACHMENT_REQUIREMENT_ID, PLAN_TYPE, INS_COMPANY_ID, INSURANCE_PLAN, PROVIDER_TAXONOMY, PROCEDURE_CODE, REQUIREMENT_ID, VENDOR_TYPE, IS_ACTIVE, CREATE_EMPLOYEE_NUMBER, CREATE_DATETIME, UPDATE_EMPLOYEE_NUMBER, UPDATE_DATETIME)
  VALUES(1708, NULL, NULL, 630, NULL, 'D5120', 22, 'SBI', '1', 110840, TO_TIMESTAMP('2014-01-14 16:25:21:0','YYYY-MM-DD HH24:MI:SS:FF'), NULL, NULL);
INSERT INTO attachment_requirement(ATTACHMENT_REQUIREMENT_ID, PLAN_TYPE, INS_COMPANY_ID, INSURANCE_PLAN, PROVIDER_TAXONOMY, PROCEDURE_CODE, REQUIREMENT_ID, VENDOR_TYPE, IS_ACTIVE, CREATE_EMPLOYEE_NUMBER, CREATE_DATETIME, UPDATE_EMPLOYEE_NUMBER, UPDATE_DATETIME)
  VALUES(1712, NULL, NULL, NULL, NULL, 'D5130', 26, 'SBI', '1', 110840, TO_TIMESTAMP('2014-01-14 16:25:21:0','YYYY-MM-DD HH24:MI:SS:FF'), NULL, NULL);
INSERT INTO attachment_requirement(ATTACHMENT_REQUIREMENT_ID, PLAN_TYPE, INS_COMPANY_ID, INSURANCE_PLAN, PROVIDER_TAXONOMY, PROCEDURE_CODE, REQUIREMENT_ID, VENDOR_TYPE, IS_ACTIVE, CREATE_EMPLOYEE_NUMBER, CREATE_DATETIME, UPDATE_EMPLOYEE_NUMBER, UPDATE_DATETIME)
  VALUES(1713, NULL, NULL, NULL, NULL, 'D5130', 18, 'SBI', '1', 110840, TO_TIMESTAMP('2014-01-14 16:25:21:0','YYYY-MM-DD HH24:MI:SS:FF'), NULL, NULL);
INSERT INTO attachment_requirement(ATTACHMENT_REQUIREMENT_ID, PLAN_TYPE, INS_COMPANY_ID, INSURANCE_PLAN, PROVIDER_TAXONOMY, PROCEDURE_CODE, REQUIREMENT_ID, VENDOR_TYPE, IS_ACTIVE, CREATE_EMPLOYEE_NUMBER, CREATE_DATETIME, UPDATE_EMPLOYEE_NUMBER, UPDATE_DATETIME)
  VALUES(1714, NULL, NULL, 630, NULL, 'D5130', 22, 'SBI', '1', 110840, TO_TIMESTAMP('2014-01-14 16:25:21:0','YYYY-MM-DD HH24:MI:SS:FF'), NULL, NULL);
INSERT INTO attachment_requirement(ATTACHMENT_REQUIREMENT_ID, PLAN_TYPE, INS_COMPANY_ID, INSURANCE_PLAN, PROVIDER_TAXONOMY, PROCEDURE_CODE, REQUIREMENT_ID, VENDOR_TYPE, IS_ACTIVE, CREATE_EMPLOYEE_NUMBER, CREATE_DATETIME, UPDATE_EMPLOYEE_NUMBER, UPDATE_DATETIME)
  VALUES(1716, NULL, NULL, NULL, NULL, 'D5140', 26, 'SBI', '1', 110840, TO_TIMESTAMP('2014-01-14 16:25:21:0','YYYY-MM-DD HH24:MI:SS:FF'), NULL, NULL);
INSERT INTO attachment_requirement(ATTACHMENT_REQUIREMENT_ID, PLAN_TYPE, INS_COMPANY_ID, INSURANCE_PLAN, PROVIDER_TAXONOMY, PROCEDURE_CODE, REQUIREMENT_ID, VENDOR_TYPE, IS_ACTIVE, CREATE_EMPLOYEE_NUMBER, CREATE_DATETIME, UPDATE_EMPLOYEE_NUMBER, UPDATE_DATETIME)
  VALUES(1717, NULL, NULL, NULL, NULL, 'D5140', 18, 'SBI', '1', 110840, TO_TIMESTAMP('2014-01-14 16:25:21:0','YYYY-MM-DD HH24:MI:SS:FF'), NULL, NULL);
INSERT INTO attachment_requirement(ATTACHMENT_REQUIREMENT_ID, PLAN_TYPE, INS_COMPANY_ID, INSURANCE_PLAN, PROVIDER_TAXONOMY, PROCEDURE_CODE, REQUIREMENT_ID, VENDOR_TYPE, IS_ACTIVE, CREATE_EMPLOYEE_NUMBER, CREATE_DATETIME, UPDATE_EMPLOYEE_NUMBER, UPDATE_DATETIME)
  VALUES(1718, NULL, NULL, 630, NULL, 'D5140', 22, 'SBI', '1', 110840, TO_TIMESTAMP('2014-01-14 16:25:21:0','YYYY-MM-DD HH24:MI:SS:FF'), NULL, NULL);
INSERT INTO attachment_requirement(ATTACHMENT_REQUIREMENT_ID, PLAN_TYPE, INS_COMPANY_ID, INSURANCE_PLAN, PROVIDER_TAXONOMY, PROCEDURE_CODE, REQUIREMENT_ID, VENDOR_TYPE, IS_ACTIVE, CREATE_EMPLOYEE_NUMBER, CREATE_DATETIME, UPDATE_EMPLOYEE_NUMBER, UPDATE_DATETIME)
  VALUES(1720, NULL, NULL, NULL, NULL, 'D5211', 18, 'SBI', '1', 110840, TO_TIMESTAMP('2014-01-14 16:25:21:0','YYYY-MM-DD HH24:MI:SS:FF'), NULL, NULL);
INSERT INTO attachment_requirement(ATTACHMENT_REQUIREMENT_ID, PLAN_TYPE, INS_COMPANY_ID, INSURANCE_PLAN, PROVIDER_TAXONOMY, PROCEDURE_CODE, REQUIREMENT_ID, VENDOR_TYPE, IS_ACTIVE, CREATE_EMPLOYEE_NUMBER, CREATE_DATETIME, UPDATE_EMPLOYEE_NUMBER, UPDATE_DATETIME)
  VALUES(1721, NULL, NULL, NULL, NULL, 'D5211', 26, 'SBI', '1', 110840, TO_TIMESTAMP('2014-01-14 16:25:21:0','YYYY-MM-DD HH24:MI:SS:FF'), NULL, NULL);
INSERT INTO attachment_requirement(ATTACHMENT_REQUIREMENT_ID, PLAN_TYPE, INS_COMPANY_ID, INSURANCE_PLAN, PROVIDER_TAXONOMY, PROCEDURE_CODE, REQUIREMENT_ID, VENDOR_TYPE, IS_ACTIVE, CREATE_EMPLOYEE_NUMBER, CREATE_DATETIME, UPDATE_EMPLOYEE_NUMBER, UPDATE_DATETIME)
  VALUES(1723, NULL, NULL, NULL, NULL, 'D5212', 18, 'SBI', '1', 110840, TO_TIMESTAMP('2014-01-14 16:25:21:0','YYYY-MM-DD HH24:MI:SS:FF'), NULL, NULL);
INSERT INTO attachment_requirement(ATTACHMENT_REQUIREMENT_ID, PLAN_TYPE, INS_COMPANY_ID, INSURANCE_PLAN, PROVIDER_TAXONOMY, PROCEDURE_CODE, REQUIREMENT_ID, VENDOR_TYPE, IS_ACTIVE, CREATE_EMPLOYEE_NUMBER, CREATE_DATETIME, UPDATE_EMPLOYEE_NUMBER, UPDATE_DATETIME)
  VALUES(1724, NULL, NULL, NULL, NULL, 'D5212', 26, 'SBI', '1', 110840, TO_TIMESTAMP('2014-01-14 16:25:21:0','YYYY-MM-DD HH24:MI:SS:FF'), NULL, NULL);
INSERT INTO attachment_requirement(ATTACHMENT_REQUIREMENT_ID, PLAN_TYPE, INS_COMPANY_ID, INSURANCE_PLAN, PROVIDER_TAXONOMY, PROCEDURE_CODE, REQUIREMENT_ID, VENDOR_TYPE, IS_ACTIVE, CREATE_EMPLOYEE_NUMBER, CREATE_DATETIME, UPDATE_EMPLOYEE_NUMBER, UPDATE_DATETIME)
  VALUES(1726, NULL, NULL, NULL, NULL, 'D5213', 18, 'SBI', '1', 110840, TO_TIMESTAMP('2014-01-14 16:25:21:0','YYYY-MM-DD HH24:MI:SS:FF'), NULL, NULL);
INSERT INTO attachment_requirement(ATTACHMENT_REQUIREMENT_ID, PLAN_TYPE, INS_COMPANY_ID, INSURANCE_PLAN, PROVIDER_TAXONOMY, PROCEDURE_CODE, REQUIREMENT_ID, VENDOR_TYPE, IS_ACTIVE, CREATE_EMPLOYEE_NUMBER, CREATE_DATETIME, UPDATE_EMPLOYEE_NUMBER, UPDATE_DATETIME)
  VALUES(1727, NULL, NULL, NULL, NULL, 'D5213', 26, 'SBI', '1', 110840, TO_TIMESTAMP('2014-01-14 16:25:21:0','YYYY-MM-DD HH24:MI:SS:FF'), NULL, NULL);
INSERT INTO attachment_requirement(ATTACHMENT_REQUIREMENT_ID, PLAN_TYPE, INS_COMPANY_ID, INSURANCE_PLAN, PROVIDER_TAXONOMY, PROCEDURE_CODE, REQUIREMENT_ID, VENDOR_TYPE, IS_ACTIVE, CREATE_EMPLOYEE_NUMBER, CREATE_DATETIME, UPDATE_EMPLOYEE_NUMBER, UPDATE_DATETIME)
  VALUES(1729, NULL, NULL, NULL, NULL, 'D5214', 18, 'SBI', '1', 110840, TO_TIMESTAMP('2014-01-14 16:25:21:0','YYYY-MM-DD HH24:MI:SS:FF'), NULL, NULL);
INSERT INTO attachment_requirement(ATTACHMENT_REQUIREMENT_ID, PLAN_TYPE, INS_COMPANY_ID, INSURANCE_PLAN, PROVIDER_TAXONOMY, PROCEDURE_CODE, REQUIREMENT_ID, VENDOR_TYPE, IS_ACTIVE, CREATE_EMPLOYEE_NUMBER, CREATE_DATETIME, UPDATE_EMPLOYEE_NUMBER, UPDATE_DATETIME)
  VALUES(1730, NULL, NULL, NULL, NULL, 'D5214', 26, 'SBI', '1', 110840, TO_TIMESTAMP('2014-01-14 16:25:21:0','YYYY-MM-DD HH24:MI:SS:FF'), NULL, NULL);
INSERT INTO attachment_requirement(ATTACHMENT_REQUIREMENT_ID, PLAN_TYPE, INS_COMPANY_ID, INSURANCE_PLAN, PROVIDER_TAXONOMY, PROCEDURE_CODE, REQUIREMENT_ID, VENDOR_TYPE, IS_ACTIVE, CREATE_EMPLOYEE_NUMBER, CREATE_DATETIME, UPDATE_EMPLOYEE_NUMBER, UPDATE_DATETIME)
  VALUES(1732, NULL, NULL, NULL, NULL, 'D5225', 18, 'SBI', '1', 110840, TO_TIMESTAMP('2014-01-14 16:25:21:0','YYYY-MM-DD HH24:MI:SS:FF'), NULL, NULL);
INSERT INTO attachment_requirement(ATTACHMENT_REQUIREMENT_ID, PLAN_TYPE, INS_COMPANY_ID, INSURANCE_PLAN, PROVIDER_TAXONOMY, PROCEDURE_CODE, REQUIREMENT_ID, VENDOR_TYPE, IS_ACTIVE, CREATE_EMPLOYEE_NUMBER, CREATE_DATETIME, UPDATE_EMPLOYEE_NUMBER, UPDATE_DATETIME)
  VALUES(1734, NULL, NULL, NULL, NULL, 'D5225', 26, 'SBI', '1', 110840, TO_TIMESTAMP('2014-01-14 16:25:21:0','YYYY-MM-DD HH24:MI:SS:FF'), NULL, NULL);
INSERT INTO attachment_requirement(ATTACHMENT_REQUIREMENT_ID, PLAN_TYPE, INS_COMPANY_ID, INSURANCE_PLAN, PROVIDER_TAXONOMY, PROCEDURE_CODE, REQUIREMENT_ID, VENDOR_TYPE, IS_ACTIVE, CREATE_EMPLOYEE_NUMBER, CREATE_DATETIME, UPDATE_EMPLOYEE_NUMBER, UPDATE_DATETIME)
  VALUES(1736, NULL, NULL, NULL, NULL, 'D5226', 18, 'SBI', '1', 110840, TO_TIMESTAMP('2014-01-14 16:25:21:0','YYYY-MM-DD HH24:MI:SS:FF'), NULL, NULL);
INSERT INTO attachment_requirement(ATTACHMENT_REQUIREMENT_ID, PLAN_TYPE, INS_COMPANY_ID, INSURANCE_PLAN, PROVIDER_TAXONOMY, PROCEDURE_CODE, REQUIREMENT_ID, VENDOR_TYPE, IS_ACTIVE, CREATE_EMPLOYEE_NUMBER, CREATE_DATETIME, UPDATE_EMPLOYEE_NUMBER, UPDATE_DATETIME)
  VALUES(1738, NULL, NULL, NULL, NULL, 'D5226', 26, 'SBI', '1', 110840, TO_TIMESTAMP('2014-01-14 16:25:21:0','YYYY-MM-DD HH24:MI:SS:FF'), NULL, NULL);
INSERT INTO attachment_requirement(ATTACHMENT_REQUIREMENT_ID, PLAN_TYPE, INS_COMPANY_ID, INSURANCE_PLAN, PROVIDER_TAXONOMY, PROCEDURE_CODE, REQUIREMENT_ID, VENDOR_TYPE, IS_ACTIVE, CREATE_EMPLOYEE_NUMBER, CREATE_DATETIME, UPDATE_EMPLOYEE_NUMBER, UPDATE_DATETIME)
  VALUES(1740, NULL, NULL, NULL, NULL, 'D5281', 18, 'SBI', '1', 110840, TO_TIMESTAMP('2014-01-14 16:25:21:0','YYYY-MM-DD HH24:MI:SS:FF'), NULL, NULL);
INSERT INTO attachment_requirement(ATTACHMENT_REQUIREMENT_ID, PLAN_TYPE, INS_COMPANY_ID, INSURANCE_PLAN, PROVIDER_TAXONOMY, PROCEDURE_CODE, REQUIREMENT_ID, VENDOR_TYPE, IS_ACTIVE, CREATE_EMPLOYEE_NUMBER, CREATE_DATETIME, UPDATE_EMPLOYEE_NUMBER, UPDATE_DATETIME)
  VALUES(1741, NULL, NULL, NULL, NULL, 'D5281', 26, 'SBI', '1', 110840, TO_TIMESTAMP('2014-01-14 16:25:21:0','YYYY-MM-DD HH24:MI:SS:FF'), NULL, NULL);
INSERT INTO attachment_requirement(ATTACHMENT_REQUIREMENT_ID, PLAN_TYPE, INS_COMPANY_ID, INSURANCE_PLAN, PROVIDER_TAXONOMY, PROCEDURE_CODE, REQUIREMENT_ID, VENDOR_TYPE, IS_ACTIVE, CREATE_EMPLOYEE_NUMBER, CREATE_DATETIME, UPDATE_EMPLOYEE_NUMBER, UPDATE_DATETIME)
  VALUES(1743, NULL, NULL, NULL, NULL, 'D5410', 26, 'SBI', '1', 110840, TO_TIMESTAMP('2014-01-14 16:25:21:0','YYYY-MM-DD HH24:MI:SS:FF'), NULL, NULL);
INSERT INTO attachment_requirement(ATTACHMENT_REQUIREMENT_ID, PLAN_TYPE, INS_COMPANY_ID, INSURANCE_PLAN, PROVIDER_TAXONOMY, PROCEDURE_CODE, REQUIREMENT_ID, VENDOR_TYPE, IS_ACTIVE, CREATE_EMPLOYEE_NUMBER, CREATE_DATETIME, UPDATE_EMPLOYEE_NUMBER, UPDATE_DATETIME)
  VALUES(1744, NULL, NULL, NULL, NULL, 'D5411', 26, 'SBI', '1', 110840, TO_TIMESTAMP('2014-01-14 16:25:21:0','YYYY-MM-DD HH24:MI:SS:FF'), NULL, NULL);
INSERT INTO attachment_requirement(ATTACHMENT_REQUIREMENT_ID, PLAN_TYPE, INS_COMPANY_ID, INSURANCE_PLAN, PROVIDER_TAXONOMY, PROCEDURE_CODE, REQUIREMENT_ID, VENDOR_TYPE, IS_ACTIVE, CREATE_EMPLOYEE_NUMBER, CREATE_DATETIME, UPDATE_EMPLOYEE_NUMBER, UPDATE_DATETIME)
  VALUES(1745, NULL, NULL, NULL, NULL, 'D5421', 26, 'SBI', '1', 110840, TO_TIMESTAMP('2014-01-14 16:25:21:0','YYYY-MM-DD HH24:MI:SS:FF'), NULL, NULL);
INSERT INTO attachment_requirement(ATTACHMENT_REQUIREMENT_ID, PLAN_TYPE, INS_COMPANY_ID, INSURANCE_PLAN, PROVIDER_TAXONOMY, PROCEDURE_CODE, REQUIREMENT_ID, VENDOR_TYPE, IS_ACTIVE, CREATE_EMPLOYEE_NUMBER, CREATE_DATETIME, UPDATE_EMPLOYEE_NUMBER, UPDATE_DATETIME)
  VALUES(1746, NULL, NULL, NULL, NULL, 'D5422', 26, 'SBI', '1', 110840, TO_TIMESTAMP('2014-01-14 16:25:21:0','YYYY-MM-DD HH24:MI:SS:FF'), NULL, NULL);
INSERT INTO attachment_requirement(ATTACHMENT_REQUIREMENT_ID, PLAN_TYPE, INS_COMPANY_ID, INSURANCE_PLAN, PROVIDER_TAXONOMY, PROCEDURE_CODE, REQUIREMENT_ID, VENDOR_TYPE, IS_ACTIVE, CREATE_EMPLOYEE_NUMBER, CREATE_DATETIME, UPDATE_EMPLOYEE_NUMBER, UPDATE_DATETIME)
  VALUES(1747, NULL, NULL, NULL, NULL, 'D5510', 26, 'SBI', '1', 110840, TO_TIMESTAMP('2014-01-14 16:25:21:0','YYYY-MM-DD HH24:MI:SS:FF'), NULL, NULL);
INSERT INTO attachment_requirement(ATTACHMENT_REQUIREMENT_ID, PLAN_TYPE, INS_COMPANY_ID, INSURANCE_PLAN, PROVIDER_TAXONOMY, PROCEDURE_CODE, REQUIREMENT_ID, VENDOR_TYPE, IS_ACTIVE, CREATE_EMPLOYEE_NUMBER, CREATE_DATETIME, UPDATE_EMPLOYEE_NUMBER, UPDATE_DATETIME)
  VALUES(1748, NULL, NULL, NULL, NULL, 'D5520', 26, 'SBI', '1', 110840, TO_TIMESTAMP('2014-01-14 16:25:21:0','YYYY-MM-DD HH24:MI:SS:FF'), NULL, NULL);
INSERT INTO attachment_requirement(ATTACHMENT_REQUIREMENT_ID, PLAN_TYPE, INS_COMPANY_ID, INSURANCE_PLAN, PROVIDER_TAXONOMY, PROCEDURE_CODE, REQUIREMENT_ID, VENDOR_TYPE, IS_ACTIVE, CREATE_EMPLOYEE_NUMBER, CREATE_DATETIME, UPDATE_EMPLOYEE_NUMBER, UPDATE_DATETIME)
  VALUES(1749, NULL, NULL, NULL, NULL, 'D5610', 26, 'SBI', '1', 110840, TO_TIMESTAMP('2014-01-14 16:25:21:0','YYYY-MM-DD HH24:MI:SS:FF'), NULL, NULL);
INSERT INTO attachment_requirement(ATTACHMENT_REQUIREMENT_ID, PLAN_TYPE, INS_COMPANY_ID, INSURANCE_PLAN, PROVIDER_TAXONOMY, PROCEDURE_CODE, REQUIREMENT_ID, VENDOR_TYPE, IS_ACTIVE, CREATE_EMPLOYEE_NUMBER, CREATE_DATETIME, UPDATE_EMPLOYEE_NUMBER, UPDATE_DATETIME)
  VALUES(1750, NULL, NULL, NULL, NULL, 'D5620', 26, 'SBI', '1', 110840, TO_TIMESTAMP('2014-01-14 16:25:21:0','YYYY-MM-DD HH24:MI:SS:FF'), NULL, NULL);
INSERT INTO attachment_requirement(ATTACHMENT_REQUIREMENT_ID, PLAN_TYPE, INS_COMPANY_ID, INSURANCE_PLAN, PROVIDER_TAXONOMY, PROCEDURE_CODE, REQUIREMENT_ID, VENDOR_TYPE, IS_ACTIVE, CREATE_EMPLOYEE_NUMBER, CREATE_DATETIME, UPDATE_EMPLOYEE_NUMBER, UPDATE_DATETIME)
  VALUES(1751, NULL, NULL, NULL, NULL, 'D5630', 26, 'SBI', '1', 110840, TO_TIMESTAMP('2014-01-14 16:25:21:0','YYYY-MM-DD HH24:MI:SS:FF'), NULL, NULL);
INSERT INTO attachment_requirement(ATTACHMENT_REQUIREMENT_ID, PLAN_TYPE, INS_COMPANY_ID, INSURANCE_PLAN, PROVIDER_TAXONOMY, PROCEDURE_CODE, REQUIREMENT_ID, VENDOR_TYPE, IS_ACTIVE, CREATE_EMPLOYEE_NUMBER, CREATE_DATETIME, UPDATE_EMPLOYEE_NUMBER, UPDATE_DATETIME)
  VALUES(1752, NULL, NULL, NULL, NULL, 'D5640', 26, 'SBI', '1', 110840, TO_TIMESTAMP('2014-01-14 16:25:21:0','YYYY-MM-DD HH24:MI:SS:FF'), NULL, NULL);
INSERT INTO attachment_requirement(ATTACHMENT_REQUIREMENT_ID, PLAN_TYPE, INS_COMPANY_ID, INSURANCE_PLAN, PROVIDER_TAXONOMY, PROCEDURE_CODE, REQUIREMENT_ID, VENDOR_TYPE, IS_ACTIVE, CREATE_EMPLOYEE_NUMBER, CREATE_DATETIME, UPDATE_EMPLOYEE_NUMBER, UPDATE_DATETIME)
  VALUES(1753, NULL, NULL, NULL, NULL, 'D5650', 26, 'SBI', '1', 110840, TO_TIMESTAMP('2014-01-14 16:25:21:0','YYYY-MM-DD HH24:MI:SS:FF'), NULL, NULL);
INSERT INTO attachment_requirement(ATTACHMENT_REQUIREMENT_ID, PLAN_TYPE, INS_COMPANY_ID, INSURANCE_PLAN, PROVIDER_TAXONOMY, PROCEDURE_CODE, REQUIREMENT_ID, VENDOR_TYPE, IS_ACTIVE, CREATE_EMPLOYEE_NUMBER, CREATE_DATETIME, UPDATE_EMPLOYEE_NUMBER, UPDATE_DATETIME)
  VALUES(1754, NULL, NULL, NULL, NULL, 'D5660', 26, 'SBI', '1', 110840, TO_TIMESTAMP('2014-01-14 16:25:21:0','YYYY-MM-DD HH24:MI:SS:FF'), NULL, NULL);
INSERT INTO attachment_requirement(ATTACHMENT_REQUIREMENT_ID, PLAN_TYPE, INS_COMPANY_ID, INSURANCE_PLAN, PROVIDER_TAXONOMY, PROCEDURE_CODE, REQUIREMENT_ID, VENDOR_TYPE, IS_ACTIVE, CREATE_EMPLOYEE_NUMBER, CREATE_DATETIME, UPDATE_EMPLOYEE_NUMBER, UPDATE_DATETIME)
  VALUES(1755, NULL, NULL, NULL, NULL, 'D5710', 26, 'SBI', '1', 110840, TO_TIMESTAMP('2014-01-14 16:25:21:0','YYYY-MM-DD HH24:MI:SS:FF'), NULL, NULL);
INSERT INTO attachment_requirement(ATTACHMENT_REQUIREMENT_ID, PLAN_TYPE, INS_COMPANY_ID, INSURANCE_PLAN, PROVIDER_TAXONOMY, PROCEDURE_CODE, REQUIREMENT_ID, VENDOR_TYPE, IS_ACTIVE, CREATE_EMPLOYEE_NUMBER, CREATE_DATETIME, UPDATE_EMPLOYEE_NUMBER, UPDATE_DATETIME)
  VALUES(1756, NULL, NULL, NULL, NULL, 'D5711', 26, 'SBI', '1', 110840, TO_TIMESTAMP('2014-01-14 16:25:21:0','YYYY-MM-DD HH24:MI:SS:FF'), NULL, NULL);
INSERT INTO attachment_requirement(ATTACHMENT_REQUIREMENT_ID, PLAN_TYPE, INS_COMPANY_ID, INSURANCE_PLAN, PROVIDER_TAXONOMY, PROCEDURE_CODE, REQUIREMENT_ID, VENDOR_TYPE, IS_ACTIVE, CREATE_EMPLOYEE_NUMBER, CREATE_DATETIME, UPDATE_EMPLOYEE_NUMBER, UPDATE_DATETIME)
  VALUES(1757, NULL, NULL, NULL, NULL, 'D5730', 26, 'SBI', '1', 110840, TO_TIMESTAMP('2014-01-14 16:25:21:0','YYYY-MM-DD HH24:MI:SS:FF'), NULL, NULL);
INSERT INTO attachment_requirement(ATTACHMENT_REQUIREMENT_ID, PLAN_TYPE, INS_COMPANY_ID, INSURANCE_PLAN, PROVIDER_TAXONOMY, PROCEDURE_CODE, REQUIREMENT_ID, VENDOR_TYPE, IS_ACTIVE, CREATE_EMPLOYEE_NUMBER, CREATE_DATETIME, UPDATE_EMPLOYEE_NUMBER, UPDATE_DATETIME)
  VALUES(1758, NULL, NULL, NULL, NULL, 'D5731', 26, 'SBI', '1', 110840, TO_TIMESTAMP('2014-01-14 16:25:21:0','YYYY-MM-DD HH24:MI:SS:FF'), NULL, NULL);
INSERT INTO attachment_requirement(ATTACHMENT_REQUIREMENT_ID, PLAN_TYPE, INS_COMPANY_ID, INSURANCE_PLAN, PROVIDER_TAXONOMY, PROCEDURE_CODE, REQUIREMENT_ID, VENDOR_TYPE, IS_ACTIVE, CREATE_EMPLOYEE_NUMBER, CREATE_DATETIME, UPDATE_EMPLOYEE_NUMBER, UPDATE_DATETIME)
  VALUES(1759, NULL, NULL, NULL, NULL, 'D5740', 26, 'SBI', '1', 110840, TO_TIMESTAMP('2014-01-14 16:25:21:0','YYYY-MM-DD HH24:MI:SS:FF'), NULL, NULL);
INSERT INTO attachment_requirement(ATTACHMENT_REQUIREMENT_ID, PLAN_TYPE, INS_COMPANY_ID, INSURANCE_PLAN, PROVIDER_TAXONOMY, PROCEDURE_CODE, REQUIREMENT_ID, VENDOR_TYPE, IS_ACTIVE, CREATE_EMPLOYEE_NUMBER, CREATE_DATETIME, UPDATE_EMPLOYEE_NUMBER, UPDATE_DATETIME)
  VALUES(1760, NULL, NULL, NULL, NULL, 'D5741', 26, 'SBI', '1', 110840, TO_TIMESTAMP('2014-01-14 16:25:21:0','YYYY-MM-DD HH24:MI:SS:FF'), NULL, NULL);
INSERT INTO attachment_requirement(ATTACHMENT_REQUIREMENT_ID, PLAN_TYPE, INS_COMPANY_ID, INSURANCE_PLAN, PROVIDER_TAXONOMY, PROCEDURE_CODE, REQUIREMENT_ID, VENDOR_TYPE, IS_ACTIVE, CREATE_EMPLOYEE_NUMBER, CREATE_DATETIME, UPDATE_EMPLOYEE_NUMBER, UPDATE_DATETIME)
  VALUES(1761, NULL, NULL, NULL, NULL, 'D5750', 26, 'SBI', '1', 110840, TO_TIMESTAMP('2014-01-14 16:25:21:0','YYYY-MM-DD HH24:MI:SS:FF'), NULL, NULL);
INSERT INTO attachment_requirement(ATTACHMENT_REQUIREMENT_ID, PLAN_TYPE, INS_COMPANY_ID, INSURANCE_PLAN, PROVIDER_TAXONOMY, PROCEDURE_CODE, REQUIREMENT_ID, VENDOR_TYPE, IS_ACTIVE, CREATE_EMPLOYEE_NUMBER, CREATE_DATETIME, UPDATE_EMPLOYEE_NUMBER, UPDATE_DATETIME)
  VALUES(1762, NULL, NULL, NULL, NULL, 'D5751', 26, 'SBI', '1', 110840, TO_TIMESTAMP('2014-01-14 16:25:21:0','YYYY-MM-DD HH24:MI:SS:FF'), NULL, NULL);
INSERT INTO attachment_requirement(ATTACHMENT_REQUIREMENT_ID, PLAN_TYPE, INS_COMPANY_ID, INSURANCE_PLAN, PROVIDER_TAXONOMY, PROCEDURE_CODE, REQUIREMENT_ID, VENDOR_TYPE, IS_ACTIVE, CREATE_EMPLOYEE_NUMBER, CREATE_DATETIME, UPDATE_EMPLOYEE_NUMBER, UPDATE_DATETIME)
  VALUES(1763, NULL, NULL, NULL, NULL, 'D5760', 26, 'SBI', '1', 110840, TO_TIMESTAMP('2014-01-14 16:25:21:0','YYYY-MM-DD HH24:MI:SS:FF'), NULL, NULL);
INSERT INTO attachment_requirement(ATTACHMENT_REQUIREMENT_ID, PLAN_TYPE, INS_COMPANY_ID, INSURANCE_PLAN, PROVIDER_TAXONOMY, PROCEDURE_CODE, REQUIREMENT_ID, VENDOR_TYPE, IS_ACTIVE, CREATE_EMPLOYEE_NUMBER, CREATE_DATETIME, UPDATE_EMPLOYEE_NUMBER, UPDATE_DATETIME)
  VALUES(1764, NULL, NULL, NULL, NULL, 'D5761', 26, 'SBI', '1', 110840, TO_TIMESTAMP('2014-01-14 16:25:21:0','YYYY-MM-DD HH24:MI:SS:FF'), NULL, NULL);
INSERT INTO attachment_requirement(ATTACHMENT_REQUIREMENT_ID, PLAN_TYPE, INS_COMPANY_ID, INSURANCE_PLAN, PROVIDER_TAXONOMY, PROCEDURE_CODE, REQUIREMENT_ID, VENDOR_TYPE, IS_ACTIVE, CREATE_EMPLOYEE_NUMBER, CREATE_DATETIME, UPDATE_EMPLOYEE_NUMBER, UPDATE_DATETIME)
  VALUES(1765, NULL, NULL, NULL, NULL, 'D5810', 18, 'SBI', '1', 110840, TO_TIMESTAMP('2014-01-14 16:25:21:0','YYYY-MM-DD HH24:MI:SS:FF'), NULL, NULL);
INSERT INTO attachment_requirement(ATTACHMENT_REQUIREMENT_ID, PLAN_TYPE, INS_COMPANY_ID, INSURANCE_PLAN, PROVIDER_TAXONOMY, PROCEDURE_CODE, REQUIREMENT_ID, VENDOR_TYPE, IS_ACTIVE, CREATE_EMPLOYEE_NUMBER, CREATE_DATETIME, UPDATE_EMPLOYEE_NUMBER, UPDATE_DATETIME)
  VALUES(1767, NULL, NULL, NULL, NULL, 'D5810', 26, 'SBI', '1', 110840, TO_TIMESTAMP('2014-01-14 16:25:21:0','YYYY-MM-DD HH24:MI:SS:FF'), NULL, NULL);
INSERT INTO attachment_requirement(ATTACHMENT_REQUIREMENT_ID, PLAN_TYPE, INS_COMPANY_ID, INSURANCE_PLAN, PROVIDER_TAXONOMY, PROCEDURE_CODE, REQUIREMENT_ID, VENDOR_TYPE, IS_ACTIVE, CREATE_EMPLOYEE_NUMBER, CREATE_DATETIME, UPDATE_EMPLOYEE_NUMBER, UPDATE_DATETIME)
  VALUES(1768, NULL, NULL, NULL, NULL, 'D5811', 18, 'SBI', '1', 110840, TO_TIMESTAMP('2014-01-14 16:25:21:0','YYYY-MM-DD HH24:MI:SS:FF'), NULL, NULL);
INSERT INTO attachment_requirement(ATTACHMENT_REQUIREMENT_ID, PLAN_TYPE, INS_COMPANY_ID, INSURANCE_PLAN, PROVIDER_TAXONOMY, PROCEDURE_CODE, REQUIREMENT_ID, VENDOR_TYPE, IS_ACTIVE, CREATE_EMPLOYEE_NUMBER, CREATE_DATETIME, UPDATE_EMPLOYEE_NUMBER, UPDATE_DATETIME)
  VALUES(1770, NULL, NULL, NULL, NULL, 'D5811', 26, 'SBI', '1', 110840, TO_TIMESTAMP('2014-01-14 16:25:21:0','YYYY-MM-DD HH24:MI:SS:FF'), NULL, NULL);
INSERT INTO attachment_requirement(ATTACHMENT_REQUIREMENT_ID, PLAN_TYPE, INS_COMPANY_ID, INSURANCE_PLAN, PROVIDER_TAXONOMY, PROCEDURE_CODE, REQUIREMENT_ID, VENDOR_TYPE, IS_ACTIVE, CREATE_EMPLOYEE_NUMBER, CREATE_DATETIME, UPDATE_EMPLOYEE_NUMBER, UPDATE_DATETIME)
  VALUES(1771, NULL, NULL, NULL, NULL, 'D5820', 18, 'SBI', '1', 110840, TO_TIMESTAMP('2014-01-14 16:25:21:0','YYYY-MM-DD HH24:MI:SS:FF'), NULL, NULL);
INSERT INTO attachment_requirement(ATTACHMENT_REQUIREMENT_ID, PLAN_TYPE, INS_COMPANY_ID, INSURANCE_PLAN, PROVIDER_TAXONOMY, PROCEDURE_CODE, REQUIREMENT_ID, VENDOR_TYPE, IS_ACTIVE, CREATE_EMPLOYEE_NUMBER, CREATE_DATETIME, UPDATE_EMPLOYEE_NUMBER, UPDATE_DATETIME)
  VALUES(1772, NULL, NULL, NULL, NULL, 'D5820', 26, 'SBI', '1', 110840, TO_TIMESTAMP('2014-01-14 16:25:21:0','YYYY-MM-DD HH24:MI:SS:FF'), NULL, NULL);
INSERT INTO attachment_requirement(ATTACHMENT_REQUIREMENT_ID, PLAN_TYPE, INS_COMPANY_ID, INSURANCE_PLAN, PROVIDER_TAXONOMY, PROCEDURE_CODE, REQUIREMENT_ID, VENDOR_TYPE, IS_ACTIVE, CREATE_EMPLOYEE_NUMBER, CREATE_DATETIME, UPDATE_EMPLOYEE_NUMBER, UPDATE_DATETIME)
  VALUES(1775, NULL, NULL, NULL, NULL, 'D5821', 18, 'SBI', '1', 110840, TO_TIMESTAMP('2014-01-14 16:25:21:0','YYYY-MM-DD HH24:MI:SS:FF'), NULL, NULL);
INSERT INTO attachment_requirement(ATTACHMENT_REQUIREMENT_ID, PLAN_TYPE, INS_COMPANY_ID, INSURANCE_PLAN, PROVIDER_TAXONOMY, PROCEDURE_CODE, REQUIREMENT_ID, VENDOR_TYPE, IS_ACTIVE, CREATE_EMPLOYEE_NUMBER, CREATE_DATETIME, UPDATE_EMPLOYEE_NUMBER, UPDATE_DATETIME)
  VALUES(1776, NULL, NULL, NULL, NULL, 'D5821', 26, 'SBI', '1', 110840, TO_TIMESTAMP('2014-01-14 16:25:21:0','YYYY-MM-DD HH24:MI:SS:FF'), NULL, NULL);
INSERT INTO attachment_requirement(ATTACHMENT_REQUIREMENT_ID, PLAN_TYPE, INS_COMPANY_ID, INSURANCE_PLAN, PROVIDER_TAXONOMY, PROCEDURE_CODE, REQUIREMENT_ID, VENDOR_TYPE, IS_ACTIVE, CREATE_EMPLOYEE_NUMBER, CREATE_DATETIME, UPDATE_EMPLOYEE_NUMBER, UPDATE_DATETIME)
  VALUES(1779, NULL, NULL, NULL, NULL, 'D5850', 26, 'SBI', '1', 110840, TO_TIMESTAMP('2014-01-14 16:25:21:0','YYYY-MM-DD HH24:MI:SS:FF'), NULL, NULL);
INSERT INTO attachment_requirement(ATTACHMENT_REQUIREMENT_ID, PLAN_TYPE, INS_COMPANY_ID, INSURANCE_PLAN, PROVIDER_TAXONOMY, PROCEDURE_CODE, REQUIREMENT_ID, VENDOR_TYPE, IS_ACTIVE, CREATE_EMPLOYEE_NUMBER, CREATE_DATETIME, UPDATE_EMPLOYEE_NUMBER, UPDATE_DATETIME)
  VALUES(1780, NULL, NULL, NULL, NULL, 'D5851', 26, 'SBI', '1', 110840, TO_TIMESTAMP('2014-01-14 16:25:21:0','YYYY-MM-DD HH24:MI:SS:FF'), NULL, NULL);
INSERT INTO attachment_requirement(ATTACHMENT_REQUIREMENT_ID, PLAN_TYPE, INS_COMPANY_ID, INSURANCE_PLAN, PROVIDER_TAXONOMY, PROCEDURE_CODE, REQUIREMENT_ID, VENDOR_TYPE, IS_ACTIVE, CREATE_EMPLOYEE_NUMBER, CREATE_DATETIME, UPDATE_EMPLOYEE_NUMBER, UPDATE_DATETIME)
  VALUES(1781, NULL, NULL, NULL, NULL, 'D5860', 26, 'SBI', '1', 110840, TO_TIMESTAMP('2014-01-14 16:25:21:0','YYYY-MM-DD HH24:MI:SS:FF'), NULL, NULL);
INSERT INTO attachment_requirement(ATTACHMENT_REQUIREMENT_ID, PLAN_TYPE, INS_COMPANY_ID, INSURANCE_PLAN, PROVIDER_TAXONOMY, PROCEDURE_CODE, REQUIREMENT_ID, VENDOR_TYPE, IS_ACTIVE, CREATE_EMPLOYEE_NUMBER, CREATE_DATETIME, UPDATE_EMPLOYEE_NUMBER, UPDATE_DATETIME)
  VALUES(1782, NULL, NULL, NULL, NULL, 'D5860', 18, 'SBI', '1', 110840, TO_TIMESTAMP('2014-01-14 16:25:21:0','YYYY-MM-DD HH24:MI:SS:FF'), NULL, NULL);
INSERT INTO attachment_requirement(ATTACHMENT_REQUIREMENT_ID, PLAN_TYPE, INS_COMPANY_ID, INSURANCE_PLAN, PROVIDER_TAXONOMY, PROCEDURE_CODE, REQUIREMENT_ID, VENDOR_TYPE, IS_ACTIVE, CREATE_EMPLOYEE_NUMBER, CREATE_DATETIME, UPDATE_EMPLOYEE_NUMBER, UPDATE_DATETIME)
  VALUES(1783, NULL, NULL, NULL, NULL, 'D5861', 26, 'SBI', '1', 110840, TO_TIMESTAMP('2014-01-14 16:25:21:0','YYYY-MM-DD HH24:MI:SS:FF'), NULL, NULL);
INSERT INTO attachment_requirement(ATTACHMENT_REQUIREMENT_ID, PLAN_TYPE, INS_COMPANY_ID, INSURANCE_PLAN, PROVIDER_TAXONOMY, PROCEDURE_CODE, REQUIREMENT_ID, VENDOR_TYPE, IS_ACTIVE, CREATE_EMPLOYEE_NUMBER, CREATE_DATETIME, UPDATE_EMPLOYEE_NUMBER, UPDATE_DATETIME)
  VALUES(1784, NULL, NULL, NULL, NULL, 'D5861', 18, 'SBI', '1', 110840, TO_TIMESTAMP('2014-01-14 16:25:21:0','YYYY-MM-DD HH24:MI:SS:FF'), NULL, NULL);
INSERT INTO attachment_requirement(ATTACHMENT_REQUIREMENT_ID, PLAN_TYPE, INS_COMPANY_ID, INSURANCE_PLAN, PROVIDER_TAXONOMY, PROCEDURE_CODE, REQUIREMENT_ID, VENDOR_TYPE, IS_ACTIVE, CREATE_EMPLOYEE_NUMBER, CREATE_DATETIME, UPDATE_EMPLOYEE_NUMBER, UPDATE_DATETIME)
  VALUES(1785, NULL, NULL, NULL, NULL, 'D5862', 2, 'SBI', '1', 110840, TO_TIMESTAMP('2014-01-14 16:25:21:0','YYYY-MM-DD HH24:MI:SS:FF'), NULL, NULL);
INSERT INTO attachment_requirement(ATTACHMENT_REQUIREMENT_ID, PLAN_TYPE, INS_COMPANY_ID, INSURANCE_PLAN, PROVIDER_TAXONOMY, PROCEDURE_CODE, REQUIREMENT_ID, VENDOR_TYPE, IS_ACTIVE, CREATE_EMPLOYEE_NUMBER, CREATE_DATETIME, UPDATE_EMPLOYEE_NUMBER, UPDATE_DATETIME)
  VALUES(1786, NULL, NULL, NULL, NULL, 'D5862', 26, 'SBI', '1', 110840, TO_TIMESTAMP('2014-01-14 16:25:21:0','YYYY-MM-DD HH24:MI:SS:FF'), NULL, NULL);
INSERT INTO attachment_requirement(ATTACHMENT_REQUIREMENT_ID, PLAN_TYPE, INS_COMPANY_ID, INSURANCE_PLAN, PROVIDER_TAXONOMY, PROCEDURE_CODE, REQUIREMENT_ID, VENDOR_TYPE, IS_ACTIVE, CREATE_EMPLOYEE_NUMBER, CREATE_DATETIME, UPDATE_EMPLOYEE_NUMBER, UPDATE_DATETIME)
  VALUES(1787, NULL, NULL, NULL, NULL, 'D5863', 18, 'SBI', '1', 110840, TO_TIMESTAMP('2014-01-14 16:25:21:0','YYYY-MM-DD HH24:MI:SS:FF'), NULL, NULL);
INSERT INTO attachment_requirement(ATTACHMENT_REQUIREMENT_ID, PLAN_TYPE, INS_COMPANY_ID, INSURANCE_PLAN, PROVIDER_TAXONOMY, PROCEDURE_CODE, REQUIREMENT_ID, VENDOR_TYPE, IS_ACTIVE, CREATE_EMPLOYEE_NUMBER, CREATE_DATETIME, UPDATE_EMPLOYEE_NUMBER, UPDATE_DATETIME)
  VALUES(1789, NULL, NULL, NULL, NULL, 'D5863', 26, 'SBI', '1', 110840, TO_TIMESTAMP('2014-01-14 16:25:21:0','YYYY-MM-DD HH24:MI:SS:FF'), NULL, NULL);
INSERT INTO attachment_requirement(ATTACHMENT_REQUIREMENT_ID, PLAN_TYPE, INS_COMPANY_ID, INSURANCE_PLAN, PROVIDER_TAXONOMY, PROCEDURE_CODE, REQUIREMENT_ID, VENDOR_TYPE, IS_ACTIVE, CREATE_EMPLOYEE_NUMBER, CREATE_DATETIME, UPDATE_EMPLOYEE_NUMBER, UPDATE_DATETIME)
  VALUES(1790, NULL, NULL, NULL, NULL, 'D5864', 18, 'SBI', '1', 110840, TO_TIMESTAMP('2014-01-14 16:25:21:0','YYYY-MM-DD HH24:MI:SS:FF'), NULL, NULL);
INSERT INTO attachment_requirement(ATTACHMENT_REQUIREMENT_ID, PLAN_TYPE, INS_COMPANY_ID, INSURANCE_PLAN, PROVIDER_TAXONOMY, PROCEDURE_CODE, REQUIREMENT_ID, VENDOR_TYPE, IS_ACTIVE, CREATE_EMPLOYEE_NUMBER, CREATE_DATETIME, UPDATE_EMPLOYEE_NUMBER, UPDATE_DATETIME)
  VALUES(1792, NULL, NULL, NULL, NULL, 'D5864', 26, 'SBI', '1', 110840, TO_TIMESTAMP('2014-01-14 16:25:21:0','YYYY-MM-DD HH24:MI:SS:FF'), NULL, NULL);
INSERT INTO attachment_requirement(ATTACHMENT_REQUIREMENT_ID, PLAN_TYPE, INS_COMPANY_ID, INSURANCE_PLAN, PROVIDER_TAXONOMY, PROCEDURE_CODE, REQUIREMENT_ID, VENDOR_TYPE, IS_ACTIVE, CREATE_EMPLOYEE_NUMBER, CREATE_DATETIME, UPDATE_EMPLOYEE_NUMBER, UPDATE_DATETIME)
  VALUES(1793, NULL, NULL, NULL, NULL, 'D5865', 18, 'SBI', '1', 110840, TO_TIMESTAMP('2014-01-14 16:25:21:0','YYYY-MM-DD HH24:MI:SS:FF'), NULL, NULL);
INSERT INTO attachment_requirement(ATTACHMENT_REQUIREMENT_ID, PLAN_TYPE, INS_COMPANY_ID, INSURANCE_PLAN, PROVIDER_TAXONOMY, PROCEDURE_CODE, REQUIREMENT_ID, VENDOR_TYPE, IS_ACTIVE, CREATE_EMPLOYEE_NUMBER, CREATE_DATETIME, UPDATE_EMPLOYEE_NUMBER, UPDATE_DATETIME)
  VALUES(1795, NULL, NULL, NULL, NULL, 'D5865', 26, 'SBI', '1', 110840, TO_TIMESTAMP('2014-01-14 16:25:21:0','YYYY-MM-DD HH24:MI:SS:FF'), NULL, NULL);
INSERT INTO attachment_requirement(ATTACHMENT_REQUIREMENT_ID, PLAN_TYPE, INS_COMPANY_ID, INSURANCE_PLAN, PROVIDER_TAXONOMY, PROCEDURE_CODE, REQUIREMENT_ID, VENDOR_TYPE, IS_ACTIVE, CREATE_EMPLOYEE_NUMBER, CREATE_DATETIME, UPDATE_EMPLOYEE_NUMBER, UPDATE_DATETIME)
  VALUES(1796, NULL, NULL, NULL, NULL, 'D5866', 18, 'SBI', '1', 110840, TO_TIMESTAMP('2014-01-14 16:25:21:0','YYYY-MM-DD HH24:MI:SS:FF'), NULL, NULL);
INSERT INTO attachment_requirement(ATTACHMENT_REQUIREMENT_ID, PLAN_TYPE, INS_COMPANY_ID, INSURANCE_PLAN, PROVIDER_TAXONOMY, PROCEDURE_CODE, REQUIREMENT_ID, VENDOR_TYPE, IS_ACTIVE, CREATE_EMPLOYEE_NUMBER, CREATE_DATETIME, UPDATE_EMPLOYEE_NUMBER, UPDATE_DATETIME)
  VALUES(1798, NULL, NULL, NULL, NULL, 'D5866', 26, 'SBI', '1', 110840, TO_TIMESTAMP('2014-01-14 16:25:21:0','YYYY-MM-DD HH24:MI:SS:FF'), NULL, NULL);
INSERT INTO attachment_requirement(ATTACHMENT_REQUIREMENT_ID, PLAN_TYPE, INS_COMPANY_ID, INSURANCE_PLAN, PROVIDER_TAXONOMY, PROCEDURE_CODE, REQUIREMENT_ID, VENDOR_TYPE, IS_ACTIVE, CREATE_EMPLOYEE_NUMBER, CREATE_DATETIME, UPDATE_EMPLOYEE_NUMBER, UPDATE_DATETIME)
  VALUES(1799, NULL, NULL, NULL, NULL, 'D6010', 18, 'SBI', '1', 110840, TO_TIMESTAMP('2014-01-14 16:25:21:0','YYYY-MM-DD HH24:MI:SS:FF'), NULL, NULL);
INSERT INTO attachment_requirement(ATTACHMENT_REQUIREMENT_ID, PLAN_TYPE, INS_COMPANY_ID, INSURANCE_PLAN, PROVIDER_TAXONOMY, PROCEDURE_CODE, REQUIREMENT_ID, VENDOR_TYPE, IS_ACTIVE, CREATE_EMPLOYEE_NUMBER, CREATE_DATETIME, UPDATE_EMPLOYEE_NUMBER, UPDATE_DATETIME)
  VALUES(1801, NULL, NULL, NULL, NULL, 'D6010', 26, 'SBI', '1', 110840, TO_TIMESTAMP('2014-01-14 16:25:21:0','YYYY-MM-DD HH24:MI:SS:FF'), NULL, NULL);
INSERT INTO attachment_requirement(ATTACHMENT_REQUIREMENT_ID, PLAN_TYPE, INS_COMPANY_ID, INSURANCE_PLAN, PROVIDER_TAXONOMY, PROCEDURE_CODE, REQUIREMENT_ID, VENDOR_TYPE, IS_ACTIVE, CREATE_EMPLOYEE_NUMBER, CREATE_DATETIME, UPDATE_EMPLOYEE_NUMBER, UPDATE_DATETIME)
  VALUES(1803, NULL, NULL, NULL, NULL, 'D6040', 18, 'SBI', '1', 110840, TO_TIMESTAMP('2014-01-14 16:25:21:0','YYYY-MM-DD HH24:MI:SS:FF'), NULL, NULL);
INSERT INTO attachment_requirement(ATTACHMENT_REQUIREMENT_ID, PLAN_TYPE, INS_COMPANY_ID, INSURANCE_PLAN, PROVIDER_TAXONOMY, PROCEDURE_CODE, REQUIREMENT_ID, VENDOR_TYPE, IS_ACTIVE, CREATE_EMPLOYEE_NUMBER, CREATE_DATETIME, UPDATE_EMPLOYEE_NUMBER, UPDATE_DATETIME)
  VALUES(1804, NULL, NULL, NULL, NULL, 'D6040', 26, 'SBI', '1', 110840, TO_TIMESTAMP('2014-01-14 16:25:21:0','YYYY-MM-DD HH24:MI:SS:FF'), NULL, NULL);
INSERT INTO attachment_requirement(ATTACHMENT_REQUIREMENT_ID, PLAN_TYPE, INS_COMPANY_ID, INSURANCE_PLAN, PROVIDER_TAXONOMY, PROCEDURE_CODE, REQUIREMENT_ID, VENDOR_TYPE, IS_ACTIVE, CREATE_EMPLOYEE_NUMBER, CREATE_DATETIME, UPDATE_EMPLOYEE_NUMBER, UPDATE_DATETIME)
  VALUES(1805, NULL, NULL, NULL, NULL, 'D6053', 18, 'SBI', '1', 110840, TO_TIMESTAMP('2014-01-14 16:25:21:0','YYYY-MM-DD HH24:MI:SS:FF'), NULL, NULL);
INSERT INTO attachment_requirement(ATTACHMENT_REQUIREMENT_ID, PLAN_TYPE, INS_COMPANY_ID, INSURANCE_PLAN, PROVIDER_TAXONOMY, PROCEDURE_CODE, REQUIREMENT_ID, VENDOR_TYPE, IS_ACTIVE, CREATE_EMPLOYEE_NUMBER, CREATE_DATETIME, UPDATE_EMPLOYEE_NUMBER, UPDATE_DATETIME)
  VALUES(1806, NULL, NULL, NULL, NULL, 'D6053', 26, 'SBI', '1', 110840, TO_TIMESTAMP('2014-01-14 16:25:21:0','YYYY-MM-DD HH24:MI:SS:FF'), NULL, NULL);
INSERT INTO attachment_requirement(ATTACHMENT_REQUIREMENT_ID, PLAN_TYPE, INS_COMPANY_ID, INSURANCE_PLAN, PROVIDER_TAXONOMY, PROCEDURE_CODE, REQUIREMENT_ID, VENDOR_TYPE, IS_ACTIVE, CREATE_EMPLOYEE_NUMBER, CREATE_DATETIME, UPDATE_EMPLOYEE_NUMBER, UPDATE_DATETIME)
  VALUES(1807, NULL, NULL, NULL, NULL, 'D6054', 18, 'SBI', '1', 110840, TO_TIMESTAMP('2014-01-14 16:25:21:0','YYYY-MM-DD HH24:MI:SS:FF'), NULL, NULL);
INSERT INTO attachment_requirement(ATTACHMENT_REQUIREMENT_ID, PLAN_TYPE, INS_COMPANY_ID, INSURANCE_PLAN, PROVIDER_TAXONOMY, PROCEDURE_CODE, REQUIREMENT_ID, VENDOR_TYPE, IS_ACTIVE, CREATE_EMPLOYEE_NUMBER, CREATE_DATETIME, UPDATE_EMPLOYEE_NUMBER, UPDATE_DATETIME)
  VALUES(1808, NULL, NULL, NULL, NULL, 'D6055', 18, 'SBI', '1', 110840, TO_TIMESTAMP('2014-01-14 16:25:21:0','YYYY-MM-DD HH24:MI:SS:FF'), NULL, NULL);
INSERT INTO attachment_requirement(ATTACHMENT_REQUIREMENT_ID, PLAN_TYPE, INS_COMPANY_ID, INSURANCE_PLAN, PROVIDER_TAXONOMY, PROCEDURE_CODE, REQUIREMENT_ID, VENDOR_TYPE, IS_ACTIVE, CREATE_EMPLOYEE_NUMBER, CREATE_DATETIME, UPDATE_EMPLOYEE_NUMBER, UPDATE_DATETIME)
  VALUES(1809, NULL, NULL, NULL, NULL, 'D6055', 26, 'SBI', '1', 110840, TO_TIMESTAMP('2014-01-14 16:25:21:0','YYYY-MM-DD HH24:MI:SS:FF'), NULL, NULL);
INSERT INTO attachment_requirement(ATTACHMENT_REQUIREMENT_ID, PLAN_TYPE, INS_COMPANY_ID, INSURANCE_PLAN, PROVIDER_TAXONOMY, PROCEDURE_CODE, REQUIREMENT_ID, VENDOR_TYPE, IS_ACTIVE, CREATE_EMPLOYEE_NUMBER, CREATE_DATETIME, UPDATE_EMPLOYEE_NUMBER, UPDATE_DATETIME)
  VALUES(1810, NULL, NULL, NULL, NULL, 'D6055', 2, 'SBI', '1', 110840, TO_TIMESTAMP('2014-01-14 16:25:21:0','YYYY-MM-DD HH24:MI:SS:FF'), NULL, NULL);
INSERT INTO attachment_requirement(ATTACHMENT_REQUIREMENT_ID, PLAN_TYPE, INS_COMPANY_ID, INSURANCE_PLAN, PROVIDER_TAXONOMY, PROCEDURE_CODE, REQUIREMENT_ID, VENDOR_TYPE, IS_ACTIVE, CREATE_EMPLOYEE_NUMBER, CREATE_DATETIME, UPDATE_EMPLOYEE_NUMBER, UPDATE_DATETIME)
  VALUES(1811, NULL, NULL, NULL, NULL, 'D6056', 18, 'SBI', '1', 110840, TO_TIMESTAMP('2014-01-14 16:25:21:0','YYYY-MM-DD HH24:MI:SS:FF'), NULL, NULL);
INSERT INTO attachment_requirement(ATTACHMENT_REQUIREMENT_ID, PLAN_TYPE, INS_COMPANY_ID, INSURANCE_PLAN, PROVIDER_TAXONOMY, PROCEDURE_CODE, REQUIREMENT_ID, VENDOR_TYPE, IS_ACTIVE, CREATE_EMPLOYEE_NUMBER, CREATE_DATETIME, UPDATE_EMPLOYEE_NUMBER, UPDATE_DATETIME)
  VALUES(1812, NULL, NULL, NULL, NULL, 'D6056', 26, 'SBI', '1', 110840, TO_TIMESTAMP('2014-01-14 16:25:21:0','YYYY-MM-DD HH24:MI:SS:FF'), NULL, NULL);
INSERT INTO attachment_requirement(ATTACHMENT_REQUIREMENT_ID, PLAN_TYPE, INS_COMPANY_ID, INSURANCE_PLAN, PROVIDER_TAXONOMY, PROCEDURE_CODE, REQUIREMENT_ID, VENDOR_TYPE, IS_ACTIVE, CREATE_EMPLOYEE_NUMBER, CREATE_DATETIME, UPDATE_EMPLOYEE_NUMBER, UPDATE_DATETIME)
  VALUES(1813, NULL, NULL, NULL, NULL, 'D6057', 18, 'SBI', '1', 110840, TO_TIMESTAMP('2014-01-14 16:25:21:0','YYYY-MM-DD HH24:MI:SS:FF'), NULL, NULL);
INSERT INTO attachment_requirement(ATTACHMENT_REQUIREMENT_ID, PLAN_TYPE, INS_COMPANY_ID, INSURANCE_PLAN, PROVIDER_TAXONOMY, PROCEDURE_CODE, REQUIREMENT_ID, VENDOR_TYPE, IS_ACTIVE, CREATE_EMPLOYEE_NUMBER, CREATE_DATETIME, UPDATE_EMPLOYEE_NUMBER, UPDATE_DATETIME)
  VALUES(1814, NULL, NULL, NULL, NULL, 'D6057', 26, 'SBI', '1', 110840, TO_TIMESTAMP('2014-01-14 16:25:21:0','YYYY-MM-DD HH24:MI:SS:FF'), NULL, NULL);
INSERT INTO attachment_requirement(ATTACHMENT_REQUIREMENT_ID, PLAN_TYPE, INS_COMPANY_ID, INSURANCE_PLAN, PROVIDER_TAXONOMY, PROCEDURE_CODE, REQUIREMENT_ID, VENDOR_TYPE, IS_ACTIVE, CREATE_EMPLOYEE_NUMBER, CREATE_DATETIME, UPDATE_EMPLOYEE_NUMBER, UPDATE_DATETIME)
  VALUES(1815, NULL, NULL, NULL, NULL, 'D6058', 18, 'SBI', '1', 110840, TO_TIMESTAMP('2014-01-14 16:25:21:0','YYYY-MM-DD HH24:MI:SS:FF'), NULL, NULL);
INSERT INTO attachment_requirement(ATTACHMENT_REQUIREMENT_ID, PLAN_TYPE, INS_COMPANY_ID, INSURANCE_PLAN, PROVIDER_TAXONOMY, PROCEDURE_CODE, REQUIREMENT_ID, VENDOR_TYPE, IS_ACTIVE, CREATE_EMPLOYEE_NUMBER, CREATE_DATETIME, UPDATE_EMPLOYEE_NUMBER, UPDATE_DATETIME)
  VALUES(1816, NULL, NULL, NULL, NULL, 'D6058', 26, 'SBI', '1', 110840, TO_TIMESTAMP('2014-01-14 16:25:21:0','YYYY-MM-DD HH24:MI:SS:FF'), NULL, NULL);
INSERT INTO attachment_requirement(ATTACHMENT_REQUIREMENT_ID, PLAN_TYPE, INS_COMPANY_ID, INSURANCE_PLAN, PROVIDER_TAXONOMY, PROCEDURE_CODE, REQUIREMENT_ID, VENDOR_TYPE, IS_ACTIVE, CREATE_EMPLOYEE_NUMBER, CREATE_DATETIME, UPDATE_EMPLOYEE_NUMBER, UPDATE_DATETIME)
  VALUES(1818, NULL, NULL, NULL, NULL, 'D6059', 18, 'SBI', '1', 110840, TO_TIMESTAMP('2014-01-14 16:25:21:0','YYYY-MM-DD HH24:MI:SS:FF'), NULL, NULL);
INSERT INTO attachment_requirement(ATTACHMENT_REQUIREMENT_ID, PLAN_TYPE, INS_COMPANY_ID, INSURANCE_PLAN, PROVIDER_TAXONOMY, PROCEDURE_CODE, REQUIREMENT_ID, VENDOR_TYPE, IS_ACTIVE, CREATE_EMPLOYEE_NUMBER, CREATE_DATETIME, UPDATE_EMPLOYEE_NUMBER, UPDATE_DATETIME)
  VALUES(1819, NULL, NULL, NULL, NULL, 'D6060', 18, 'SBI', '1', 110840, TO_TIMESTAMP('2014-01-14 16:25:21:0','YYYY-MM-DD HH24:MI:SS:FF'), NULL, NULL);
INSERT INTO attachment_requirement(ATTACHMENT_REQUIREMENT_ID, PLAN_TYPE, INS_COMPANY_ID, INSURANCE_PLAN, PROVIDER_TAXONOMY, PROCEDURE_CODE, REQUIREMENT_ID, VENDOR_TYPE, IS_ACTIVE, CREATE_EMPLOYEE_NUMBER, CREATE_DATETIME, UPDATE_EMPLOYEE_NUMBER, UPDATE_DATETIME)
  VALUES(1820, NULL, NULL, NULL, NULL, 'D6060', 26, 'SBI', '1', 110840, TO_TIMESTAMP('2014-01-14 16:25:21:0','YYYY-MM-DD HH24:MI:SS:FF'), NULL, NULL);
INSERT INTO attachment_requirement(ATTACHMENT_REQUIREMENT_ID, PLAN_TYPE, INS_COMPANY_ID, INSURANCE_PLAN, PROVIDER_TAXONOMY, PROCEDURE_CODE, REQUIREMENT_ID, VENDOR_TYPE, IS_ACTIVE, CREATE_EMPLOYEE_NUMBER, CREATE_DATETIME, UPDATE_EMPLOYEE_NUMBER, UPDATE_DATETIME)
  VALUES(1822, NULL, NULL, NULL, NULL, 'D6061', 18, 'SBI', '1', 110840, TO_TIMESTAMP('2014-01-14 16:25:21:0','YYYY-MM-DD HH24:MI:SS:FF'), NULL, NULL);
INSERT INTO attachment_requirement(ATTACHMENT_REQUIREMENT_ID, PLAN_TYPE, INS_COMPANY_ID, INSURANCE_PLAN, PROVIDER_TAXONOMY, PROCEDURE_CODE, REQUIREMENT_ID, VENDOR_TYPE, IS_ACTIVE, CREATE_EMPLOYEE_NUMBER, CREATE_DATETIME, UPDATE_EMPLOYEE_NUMBER, UPDATE_DATETIME)
  VALUES(1823, NULL, NULL, NULL, NULL, 'D6062', 18, 'SBI', '1', 110840, TO_TIMESTAMP('2014-01-14 16:25:21:0','YYYY-MM-DD HH24:MI:SS:FF'), NULL, NULL);
INSERT INTO attachment_requirement(ATTACHMENT_REQUIREMENT_ID, PLAN_TYPE, INS_COMPANY_ID, INSURANCE_PLAN, PROVIDER_TAXONOMY, PROCEDURE_CODE, REQUIREMENT_ID, VENDOR_TYPE, IS_ACTIVE, CREATE_EMPLOYEE_NUMBER, CREATE_DATETIME, UPDATE_EMPLOYEE_NUMBER, UPDATE_DATETIME)
  VALUES(1824, NULL, NULL, NULL, NULL, 'D6063', 18, 'SBI', '1', 110840, TO_TIMESTAMP('2014-01-14 16:25:21:0','YYYY-MM-DD HH24:MI:SS:FF'), NULL, NULL);
INSERT INTO attachment_requirement(ATTACHMENT_REQUIREMENT_ID, PLAN_TYPE, INS_COMPANY_ID, INSURANCE_PLAN, PROVIDER_TAXONOMY, PROCEDURE_CODE, REQUIREMENT_ID, VENDOR_TYPE, IS_ACTIVE, CREATE_EMPLOYEE_NUMBER, CREATE_DATETIME, UPDATE_EMPLOYEE_NUMBER, UPDATE_DATETIME)
  VALUES(1825, NULL, NULL, NULL, NULL, 'D6064', 18, 'SBI', '1', 110840, TO_TIMESTAMP('2014-01-14 16:25:21:0','YYYY-MM-DD HH24:MI:SS:FF'), NULL, NULL);
INSERT INTO attachment_requirement(ATTACHMENT_REQUIREMENT_ID, PLAN_TYPE, INS_COMPANY_ID, INSURANCE_PLAN, PROVIDER_TAXONOMY, PROCEDURE_CODE, REQUIREMENT_ID, VENDOR_TYPE, IS_ACTIVE, CREATE_EMPLOYEE_NUMBER, CREATE_DATETIME, UPDATE_EMPLOYEE_NUMBER, UPDATE_DATETIME)
  VALUES(1826, NULL, NULL, NULL, NULL, 'D6065', 18, 'SBI', '1', 110840, TO_TIMESTAMP('2014-01-14 16:25:21:0','YYYY-MM-DD HH24:MI:SS:FF'), NULL, NULL);
INSERT INTO attachment_requirement(ATTACHMENT_REQUIREMENT_ID, PLAN_TYPE, INS_COMPANY_ID, INSURANCE_PLAN, PROVIDER_TAXONOMY, PROCEDURE_CODE, REQUIREMENT_ID, VENDOR_TYPE, IS_ACTIVE, CREATE_EMPLOYEE_NUMBER, CREATE_DATETIME, UPDATE_EMPLOYEE_NUMBER, UPDATE_DATETIME)
  VALUES(1827, NULL, NULL, NULL, NULL, 'D6065', 26, 'SBI', '1', 110840, TO_TIMESTAMP('2014-01-14 16:25:21:0','YYYY-MM-DD HH24:MI:SS:FF'), NULL, NULL);
INSERT INTO attachment_requirement(ATTACHMENT_REQUIREMENT_ID, PLAN_TYPE, INS_COMPANY_ID, INSURANCE_PLAN, PROVIDER_TAXONOMY, PROCEDURE_CODE, REQUIREMENT_ID, VENDOR_TYPE, IS_ACTIVE, CREATE_EMPLOYEE_NUMBER, CREATE_DATETIME, UPDATE_EMPLOYEE_NUMBER, UPDATE_DATETIME)
  VALUES(1828, NULL, NULL, NULL, NULL, 'D6066', 18, 'SBI', '1', 110840, TO_TIMESTAMP('2014-01-14 16:25:21:0','YYYY-MM-DD HH24:MI:SS:FF'), NULL, NULL);
INSERT INTO attachment_requirement(ATTACHMENT_REQUIREMENT_ID, PLAN_TYPE, INS_COMPANY_ID, INSURANCE_PLAN, PROVIDER_TAXONOMY, PROCEDURE_CODE, REQUIREMENT_ID, VENDOR_TYPE, IS_ACTIVE, CREATE_EMPLOYEE_NUMBER, CREATE_DATETIME, UPDATE_EMPLOYEE_NUMBER, UPDATE_DATETIME)
  VALUES(1829, NULL, NULL, NULL, NULL, 'D6066', 26, 'SBI', '1', 110840, TO_TIMESTAMP('2014-01-14 16:25:21:0','YYYY-MM-DD HH24:MI:SS:FF'), NULL, NULL);
INSERT INTO attachment_requirement(ATTACHMENT_REQUIREMENT_ID, PLAN_TYPE, INS_COMPANY_ID, INSURANCE_PLAN, PROVIDER_TAXONOMY, PROCEDURE_CODE, REQUIREMENT_ID, VENDOR_TYPE, IS_ACTIVE, CREATE_EMPLOYEE_NUMBER, CREATE_DATETIME, UPDATE_EMPLOYEE_NUMBER, UPDATE_DATETIME)
  VALUES(1830, NULL, NULL, NULL, NULL, 'D6067', 18, 'SBI', '1', 110840, TO_TIMESTAMP('2014-01-14 16:25:21:0','YYYY-MM-DD HH24:MI:SS:FF'), NULL, NULL);
INSERT INTO attachment_requirement(ATTACHMENT_REQUIREMENT_ID, PLAN_TYPE, INS_COMPANY_ID, INSURANCE_PLAN, PROVIDER_TAXONOMY, PROCEDURE_CODE, REQUIREMENT_ID, VENDOR_TYPE, IS_ACTIVE, CREATE_EMPLOYEE_NUMBER, CREATE_DATETIME, UPDATE_EMPLOYEE_NUMBER, UPDATE_DATETIME)
  VALUES(1831, NULL, NULL, NULL, NULL, 'D6068', 18, 'SBI', '1', 110840, TO_TIMESTAMP('2014-01-14 16:25:21:0','YYYY-MM-DD HH24:MI:SS:FF'), NULL, NULL);
INSERT INTO attachment_requirement(ATTACHMENT_REQUIREMENT_ID, PLAN_TYPE, INS_COMPANY_ID, INSURANCE_PLAN, PROVIDER_TAXONOMY, PROCEDURE_CODE, REQUIREMENT_ID, VENDOR_TYPE, IS_ACTIVE, CREATE_EMPLOYEE_NUMBER, CREATE_DATETIME, UPDATE_EMPLOYEE_NUMBER, UPDATE_DATETIME)
  VALUES(1832, NULL, NULL, NULL, NULL, 'D6069', 18, 'SBI', '1', 110840, TO_TIMESTAMP('2014-01-14 16:25:21:0','YYYY-MM-DD HH24:MI:SS:FF'), NULL, NULL);
INSERT INTO attachment_requirement(ATTACHMENT_REQUIREMENT_ID, PLAN_TYPE, INS_COMPANY_ID, INSURANCE_PLAN, PROVIDER_TAXONOMY, PROCEDURE_CODE, REQUIREMENT_ID, VENDOR_TYPE, IS_ACTIVE, CREATE_EMPLOYEE_NUMBER, CREATE_DATETIME, UPDATE_EMPLOYEE_NUMBER, UPDATE_DATETIME)
  VALUES(1833, NULL, NULL, NULL, NULL, 'D6070', 18, 'SBI', '1', 110840, TO_TIMESTAMP('2014-01-14 16:25:21:0','YYYY-MM-DD HH24:MI:SS:FF'), NULL, NULL);
INSERT INTO attachment_requirement(ATTACHMENT_REQUIREMENT_ID, PLAN_TYPE, INS_COMPANY_ID, INSURANCE_PLAN, PROVIDER_TAXONOMY, PROCEDURE_CODE, REQUIREMENT_ID, VENDOR_TYPE, IS_ACTIVE, CREATE_EMPLOYEE_NUMBER, CREATE_DATETIME, UPDATE_EMPLOYEE_NUMBER, UPDATE_DATETIME)
  VALUES(1834, NULL, NULL, NULL, NULL, 'D6070', 26, 'SBI', '1', 110840, TO_TIMESTAMP('2014-01-14 16:25:21:0','YYYY-MM-DD HH24:MI:SS:FF'), NULL, NULL);
INSERT INTO attachment_requirement(ATTACHMENT_REQUIREMENT_ID, PLAN_TYPE, INS_COMPANY_ID, INSURANCE_PLAN, PROVIDER_TAXONOMY, PROCEDURE_CODE, REQUIREMENT_ID, VENDOR_TYPE, IS_ACTIVE, CREATE_EMPLOYEE_NUMBER, CREATE_DATETIME, UPDATE_EMPLOYEE_NUMBER, UPDATE_DATETIME)
  VALUES(1837, NULL, NULL, NULL, NULL, 'D6071', 18, 'SBI', '1', 110840, TO_TIMESTAMP('2014-01-14 16:25:21:0','YYYY-MM-DD HH24:MI:SS:FF'), NULL, NULL);
INSERT INTO attachment_requirement(ATTACHMENT_REQUIREMENT_ID, PLAN_TYPE, INS_COMPANY_ID, INSURANCE_PLAN, PROVIDER_TAXONOMY, PROCEDURE_CODE, REQUIREMENT_ID, VENDOR_TYPE, IS_ACTIVE, CREATE_EMPLOYEE_NUMBER, CREATE_DATETIME, UPDATE_EMPLOYEE_NUMBER, UPDATE_DATETIME)
  VALUES(1838, NULL, NULL, NULL, NULL, 'D6072', 18, 'SBI', '1', 110840, TO_TIMESTAMP('2014-01-14 16:25:21:0','YYYY-MM-DD HH24:MI:SS:FF'), NULL, NULL);
INSERT INTO attachment_requirement(ATTACHMENT_REQUIREMENT_ID, PLAN_TYPE, INS_COMPANY_ID, INSURANCE_PLAN, PROVIDER_TAXONOMY, PROCEDURE_CODE, REQUIREMENT_ID, VENDOR_TYPE, IS_ACTIVE, CREATE_EMPLOYEE_NUMBER, CREATE_DATETIME, UPDATE_EMPLOYEE_NUMBER, UPDATE_DATETIME)
  VALUES(1839, NULL, NULL, NULL, NULL, 'D6073', 18, 'SBI', '1', 110840, TO_TIMESTAMP('2014-01-14 16:25:21:0','YYYY-MM-DD HH24:MI:SS:FF'), NULL, NULL);
INSERT INTO attachment_requirement(ATTACHMENT_REQUIREMENT_ID, PLAN_TYPE, INS_COMPANY_ID, INSURANCE_PLAN, PROVIDER_TAXONOMY, PROCEDURE_CODE, REQUIREMENT_ID, VENDOR_TYPE, IS_ACTIVE, CREATE_EMPLOYEE_NUMBER, CREATE_DATETIME, UPDATE_EMPLOYEE_NUMBER, UPDATE_DATETIME)
  VALUES(1840, NULL, NULL, NULL, NULL, 'D6074', 18, 'SBI', '1', 110840, TO_TIMESTAMP('2014-01-14 16:25:21:0','YYYY-MM-DD HH24:MI:SS:FF'), NULL, NULL);
INSERT INTO attachment_requirement(ATTACHMENT_REQUIREMENT_ID, PLAN_TYPE, INS_COMPANY_ID, INSURANCE_PLAN, PROVIDER_TAXONOMY, PROCEDURE_CODE, REQUIREMENT_ID, VENDOR_TYPE, IS_ACTIVE, CREATE_EMPLOYEE_NUMBER, CREATE_DATETIME, UPDATE_EMPLOYEE_NUMBER, UPDATE_DATETIME)
  VALUES(1841, NULL, NULL, NULL, NULL, 'D6075', 2, 'SBI', '1', 110840, TO_TIMESTAMP('2014-01-14 16:25:21:0','YYYY-MM-DD HH24:MI:SS:FF'), NULL, NULL);
INSERT INTO attachment_requirement(ATTACHMENT_REQUIREMENT_ID, PLAN_TYPE, INS_COMPANY_ID, INSURANCE_PLAN, PROVIDER_TAXONOMY, PROCEDURE_CODE, REQUIREMENT_ID, VENDOR_TYPE, IS_ACTIVE, CREATE_EMPLOYEE_NUMBER, CREATE_DATETIME, UPDATE_EMPLOYEE_NUMBER, UPDATE_DATETIME)
  VALUES(1842, NULL, NULL, NULL, NULL, 'D6076', 2, 'SBI', '1', 110840, TO_TIMESTAMP('2014-01-14 16:25:21:0','YYYY-MM-DD HH24:MI:SS:FF'), NULL, NULL);
INSERT INTO attachment_requirement(ATTACHMENT_REQUIREMENT_ID, PLAN_TYPE, INS_COMPANY_ID, INSURANCE_PLAN, PROVIDER_TAXONOMY, PROCEDURE_CODE, REQUIREMENT_ID, VENDOR_TYPE, IS_ACTIVE, CREATE_EMPLOYEE_NUMBER, CREATE_DATETIME, UPDATE_EMPLOYEE_NUMBER, UPDATE_DATETIME)
  VALUES(1843, NULL, NULL, NULL, NULL, 'D6077', 2, 'SBI', '1', 110840, TO_TIMESTAMP('2014-01-14 16:25:21:0','YYYY-MM-DD HH24:MI:SS:FF'), NULL, NULL);
INSERT INTO attachment_requirement(ATTACHMENT_REQUIREMENT_ID, PLAN_TYPE, INS_COMPANY_ID, INSURANCE_PLAN, PROVIDER_TAXONOMY, PROCEDURE_CODE, REQUIREMENT_ID, VENDOR_TYPE, IS_ACTIVE, CREATE_EMPLOYEE_NUMBER, CREATE_DATETIME, UPDATE_EMPLOYEE_NUMBER, UPDATE_DATETIME)
  VALUES(1844, NULL, NULL, NULL, NULL, 'D6078', 18, 'SBI', '1', 110840, TO_TIMESTAMP('2014-01-14 16:25:21:0','YYYY-MM-DD HH24:MI:SS:FF'), NULL, NULL);
INSERT INTO attachment_requirement(ATTACHMENT_REQUIREMENT_ID, PLAN_TYPE, INS_COMPANY_ID, INSURANCE_PLAN, PROVIDER_TAXONOMY, PROCEDURE_CODE, REQUIREMENT_ID, VENDOR_TYPE, IS_ACTIVE, CREATE_EMPLOYEE_NUMBER, CREATE_DATETIME, UPDATE_EMPLOYEE_NUMBER, UPDATE_DATETIME)
  VALUES(1845, NULL, NULL, NULL, NULL, 'D6079', 18, 'SBI', '1', 110840, TO_TIMESTAMP('2014-01-14 16:25:21:0','YYYY-MM-DD HH24:MI:SS:FF'), NULL, NULL);
INSERT INTO attachment_requirement(ATTACHMENT_REQUIREMENT_ID, PLAN_TYPE, INS_COMPANY_ID, INSURANCE_PLAN, PROVIDER_TAXONOMY, PROCEDURE_CODE, REQUIREMENT_ID, VENDOR_TYPE, IS_ACTIVE, CREATE_EMPLOYEE_NUMBER, CREATE_DATETIME, UPDATE_EMPLOYEE_NUMBER, UPDATE_DATETIME)
  VALUES(1846, NULL, NULL, NULL, NULL, 'D6080', 18, 'SBI', '1', 110840, TO_TIMESTAMP('2014-01-14 16:25:21:0','YYYY-MM-DD HH24:MI:SS:FF'), NULL, NULL);
INSERT INTO attachment_requirement(ATTACHMENT_REQUIREMENT_ID, PLAN_TYPE, INS_COMPANY_ID, INSURANCE_PLAN, PROVIDER_TAXONOMY, PROCEDURE_CODE, REQUIREMENT_ID, VENDOR_TYPE, IS_ACTIVE, CREATE_EMPLOYEE_NUMBER, CREATE_DATETIME, UPDATE_EMPLOYEE_NUMBER, UPDATE_DATETIME)
  VALUES(1847, NULL, NULL, NULL, NULL, 'D6080', 26, 'SBI', '1', 110840, TO_TIMESTAMP('2014-01-14 16:25:21:0','YYYY-MM-DD HH24:MI:SS:FF'), NULL, NULL);
INSERT INTO attachment_requirement(ATTACHMENT_REQUIREMENT_ID, PLAN_TYPE, INS_COMPANY_ID, INSURANCE_PLAN, PROVIDER_TAXONOMY, PROCEDURE_CODE, REQUIREMENT_ID, VENDOR_TYPE, IS_ACTIVE, CREATE_EMPLOYEE_NUMBER, CREATE_DATETIME, UPDATE_EMPLOYEE_NUMBER, UPDATE_DATETIME)
  VALUES(1848, NULL, NULL, NULL, NULL, 'D6090', 26, 'SBI', '1', 110840, TO_TIMESTAMP('2014-01-14 16:25:21:0','YYYY-MM-DD HH24:MI:SS:FF'), NULL, NULL);
INSERT INTO attachment_requirement(ATTACHMENT_REQUIREMENT_ID, PLAN_TYPE, INS_COMPANY_ID, INSURANCE_PLAN, PROVIDER_TAXONOMY, PROCEDURE_CODE, REQUIREMENT_ID, VENDOR_TYPE, IS_ACTIVE, CREATE_EMPLOYEE_NUMBER, CREATE_DATETIME, UPDATE_EMPLOYEE_NUMBER, UPDATE_DATETIME)
  VALUES(1849, NULL, NULL, NULL, NULL, 'D6090', 2, 'SBI', '1', 110840, TO_TIMESTAMP('2014-01-14 16:25:21:0','YYYY-MM-DD HH24:MI:SS:FF'), NULL, NULL);
INSERT INTO attachment_requirement(ATTACHMENT_REQUIREMENT_ID, PLAN_TYPE, INS_COMPANY_ID, INSURANCE_PLAN, PROVIDER_TAXONOMY, PROCEDURE_CODE, REQUIREMENT_ID, VENDOR_TYPE, IS_ACTIVE, CREATE_EMPLOYEE_NUMBER, CREATE_DATETIME, UPDATE_EMPLOYEE_NUMBER, UPDATE_DATETIME)
  VALUES(1850, NULL, NULL, NULL, NULL, 'D6091', 2, 'SBI', '1', 110840, TO_TIMESTAMP('2014-01-14 16:25:21:0','YYYY-MM-DD HH24:MI:SS:FF'), NULL, NULL);
INSERT INTO attachment_requirement(ATTACHMENT_REQUIREMENT_ID, PLAN_TYPE, INS_COMPANY_ID, INSURANCE_PLAN, PROVIDER_TAXONOMY, PROCEDURE_CODE, REQUIREMENT_ID, VENDOR_TYPE, IS_ACTIVE, CREATE_EMPLOYEE_NUMBER, CREATE_DATETIME, UPDATE_EMPLOYEE_NUMBER, UPDATE_DATETIME)
  VALUES(1851, NULL, NULL, NULL, NULL, 'D6092', 2, 'SBI', '1', 110840, TO_TIMESTAMP('2014-01-14 16:25:21:0','YYYY-MM-DD HH24:MI:SS:FF'), NULL, NULL);
INSERT INTO attachment_requirement(ATTACHMENT_REQUIREMENT_ID, PLAN_TYPE, INS_COMPANY_ID, INSURANCE_PLAN, PROVIDER_TAXONOMY, PROCEDURE_CODE, REQUIREMENT_ID, VENDOR_TYPE, IS_ACTIVE, CREATE_EMPLOYEE_NUMBER, CREATE_DATETIME, UPDATE_EMPLOYEE_NUMBER, UPDATE_DATETIME)
  VALUES(1852, NULL, NULL, NULL, NULL, 'D6093', 2, 'SBI', '1', 110840, TO_TIMESTAMP('2014-01-14 16:25:21:0','YYYY-MM-DD HH24:MI:SS:FF'), NULL, NULL);
INSERT INTO attachment_requirement(ATTACHMENT_REQUIREMENT_ID, PLAN_TYPE, INS_COMPANY_ID, INSURANCE_PLAN, PROVIDER_TAXONOMY, PROCEDURE_CODE, REQUIREMENT_ID, VENDOR_TYPE, IS_ACTIVE, CREATE_EMPLOYEE_NUMBER, CREATE_DATETIME, UPDATE_EMPLOYEE_NUMBER, UPDATE_DATETIME)
  VALUES(1853, NULL, NULL, NULL, NULL, 'D6094', 18, 'SBI', '1', 110840, TO_TIMESTAMP('2014-01-14 16:25:21:0','YYYY-MM-DD HH24:MI:SS:FF'), NULL, NULL);
INSERT INTO attachment_requirement(ATTACHMENT_REQUIREMENT_ID, PLAN_TYPE, INS_COMPANY_ID, INSURANCE_PLAN, PROVIDER_TAXONOMY, PROCEDURE_CODE, REQUIREMENT_ID, VENDOR_TYPE, IS_ACTIVE, CREATE_EMPLOYEE_NUMBER, CREATE_DATETIME, UPDATE_EMPLOYEE_NUMBER, UPDATE_DATETIME)
  VALUES(1854, NULL, NULL, NULL, NULL, 'D6095', 2, 'SBI', '1', 110840, TO_TIMESTAMP('2014-01-14 16:25:21:0','YYYY-MM-DD HH24:MI:SS:FF'), NULL, NULL);
INSERT INTO attachment_requirement(ATTACHMENT_REQUIREMENT_ID, PLAN_TYPE, INS_COMPANY_ID, INSURANCE_PLAN, PROVIDER_TAXONOMY, PROCEDURE_CODE, REQUIREMENT_ID, VENDOR_TYPE, IS_ACTIVE, CREATE_EMPLOYEE_NUMBER, CREATE_DATETIME, UPDATE_EMPLOYEE_NUMBER, UPDATE_DATETIME)
  VALUES(1855, NULL, NULL, NULL, NULL, 'D6100', 18, 'SBI', '1', 110840, TO_TIMESTAMP('2014-01-14 16:25:21:0','YYYY-MM-DD HH24:MI:SS:FF'), NULL, NULL);
INSERT INTO attachment_requirement(ATTACHMENT_REQUIREMENT_ID, PLAN_TYPE, INS_COMPANY_ID, INSURANCE_PLAN, PROVIDER_TAXONOMY, PROCEDURE_CODE, REQUIREMENT_ID, VENDOR_TYPE, IS_ACTIVE, CREATE_EMPLOYEE_NUMBER, CREATE_DATETIME, UPDATE_EMPLOYEE_NUMBER, UPDATE_DATETIME)
  VALUES(1856, NULL, NULL, NULL, NULL, 'D6100', 26, 'SBI', '1', 110840, TO_TIMESTAMP('2014-01-14 16:25:21:0','YYYY-MM-DD HH24:MI:SS:FF'), NULL, NULL);
INSERT INTO attachment_requirement(ATTACHMENT_REQUIREMENT_ID, PLAN_TYPE, INS_COMPANY_ID, INSURANCE_PLAN, PROVIDER_TAXONOMY, PROCEDURE_CODE, REQUIREMENT_ID, VENDOR_TYPE, IS_ACTIVE, CREATE_EMPLOYEE_NUMBER, CREATE_DATETIME, UPDATE_EMPLOYEE_NUMBER, UPDATE_DATETIME)
  VALUES(1857, NULL, NULL, NULL, NULL, 'D6194', 18, 'SBI', '1', 110840, TO_TIMESTAMP('2014-01-14 16:25:21:0','YYYY-MM-DD HH24:MI:SS:FF'), NULL, NULL);
INSERT INTO attachment_requirement(ATTACHMENT_REQUIREMENT_ID, PLAN_TYPE, INS_COMPANY_ID, INSURANCE_PLAN, PROVIDER_TAXONOMY, PROCEDURE_CODE, REQUIREMENT_ID, VENDOR_TYPE, IS_ACTIVE, CREATE_EMPLOYEE_NUMBER, CREATE_DATETIME, UPDATE_EMPLOYEE_NUMBER, UPDATE_DATETIME)
  VALUES(1858, NULL, NULL, NULL, NULL, 'D6199', 26, 'SBI', '1', 110840, TO_TIMESTAMP('2014-01-14 16:25:21:0','YYYY-MM-DD HH24:MI:SS:FF'), NULL, NULL);
INSERT INTO attachment_requirement(ATTACHMENT_REQUIREMENT_ID, PLAN_TYPE, INS_COMPANY_ID, INSURANCE_PLAN, PROVIDER_TAXONOMY, PROCEDURE_CODE, REQUIREMENT_ID, VENDOR_TYPE, IS_ACTIVE, CREATE_EMPLOYEE_NUMBER, CREATE_DATETIME, UPDATE_EMPLOYEE_NUMBER, UPDATE_DATETIME)
  VALUES(1860, NULL, NULL, NULL, NULL, 'D6205', 18, 'SBI', '1', 110840, TO_TIMESTAMP('2014-01-14 16:25:21:0','YYYY-MM-DD HH24:MI:SS:FF'), NULL, NULL);
INSERT INTO attachment_requirement(ATTACHMENT_REQUIREMENT_ID, PLAN_TYPE, INS_COMPANY_ID, INSURANCE_PLAN, PROVIDER_TAXONOMY, PROCEDURE_CODE, REQUIREMENT_ID, VENDOR_TYPE, IS_ACTIVE, CREATE_EMPLOYEE_NUMBER, CREATE_DATETIME, UPDATE_EMPLOYEE_NUMBER, UPDATE_DATETIME)
  VALUES(1861, NULL, NULL, NULL, NULL, 'D6210', 18, 'SBI', '1', 110840, TO_TIMESTAMP('2014-01-14 16:25:21:0','YYYY-MM-DD HH24:MI:SS:FF'), NULL, NULL);
INSERT INTO attachment_requirement(ATTACHMENT_REQUIREMENT_ID, PLAN_TYPE, INS_COMPANY_ID, INSURANCE_PLAN, PROVIDER_TAXONOMY, PROCEDURE_CODE, REQUIREMENT_ID, VENDOR_TYPE, IS_ACTIVE, CREATE_EMPLOYEE_NUMBER, CREATE_DATETIME, UPDATE_EMPLOYEE_NUMBER, UPDATE_DATETIME)
  VALUES(1862, NULL, NULL, NULL, NULL, 'D6210', 26, 'SBI', '1', 110840, TO_TIMESTAMP('2014-01-14 16:25:21:0','YYYY-MM-DD HH24:MI:SS:FF'), NULL, NULL);
INSERT INTO attachment_requirement(ATTACHMENT_REQUIREMENT_ID, PLAN_TYPE, INS_COMPANY_ID, INSURANCE_PLAN, PROVIDER_TAXONOMY, PROCEDURE_CODE, REQUIREMENT_ID, VENDOR_TYPE, IS_ACTIVE, CREATE_EMPLOYEE_NUMBER, CREATE_DATETIME, UPDATE_EMPLOYEE_NUMBER, UPDATE_DATETIME)
  VALUES(1864, NULL, NULL, NULL, NULL, 'D6211', 18, 'SBI', '1', 110840, TO_TIMESTAMP('2014-01-14 16:25:21:0','YYYY-MM-DD HH24:MI:SS:FF'), NULL, NULL);
INSERT INTO attachment_requirement(ATTACHMENT_REQUIREMENT_ID, PLAN_TYPE, INS_COMPANY_ID, INSURANCE_PLAN, PROVIDER_TAXONOMY, PROCEDURE_CODE, REQUIREMENT_ID, VENDOR_TYPE, IS_ACTIVE, CREATE_EMPLOYEE_NUMBER, CREATE_DATETIME, UPDATE_EMPLOYEE_NUMBER, UPDATE_DATETIME)
  VALUES(1865, NULL, NULL, NULL, NULL, 'D6211', 26, 'SBI', '1', 110840, TO_TIMESTAMP('2014-01-14 16:25:21:0','YYYY-MM-DD HH24:MI:SS:FF'), NULL, NULL);
INSERT INTO attachment_requirement(ATTACHMENT_REQUIREMENT_ID, PLAN_TYPE, INS_COMPANY_ID, INSURANCE_PLAN, PROVIDER_TAXONOMY, PROCEDURE_CODE, REQUIREMENT_ID, VENDOR_TYPE, IS_ACTIVE, CREATE_EMPLOYEE_NUMBER, CREATE_DATETIME, UPDATE_EMPLOYEE_NUMBER, UPDATE_DATETIME)
  VALUES(1867, NULL, NULL, NULL, NULL, 'D6212', 18, 'SBI', '1', 110840, TO_TIMESTAMP('2014-01-14 16:25:21:0','YYYY-MM-DD HH24:MI:SS:FF'), NULL, NULL);
INSERT INTO attachment_requirement(ATTACHMENT_REQUIREMENT_ID, PLAN_TYPE, INS_COMPANY_ID, INSURANCE_PLAN, PROVIDER_TAXONOMY, PROCEDURE_CODE, REQUIREMENT_ID, VENDOR_TYPE, IS_ACTIVE, CREATE_EMPLOYEE_NUMBER, CREATE_DATETIME, UPDATE_EMPLOYEE_NUMBER, UPDATE_DATETIME)
  VALUES(1868, NULL, NULL, NULL, NULL, 'D6214', 18, 'SBI', '1', 110840, TO_TIMESTAMP('2014-01-14 16:25:21:0','YYYY-MM-DD HH24:MI:SS:FF'), NULL, NULL);
INSERT INTO attachment_requirement(ATTACHMENT_REQUIREMENT_ID, PLAN_TYPE, INS_COMPANY_ID, INSURANCE_PLAN, PROVIDER_TAXONOMY, PROCEDURE_CODE, REQUIREMENT_ID, VENDOR_TYPE, IS_ACTIVE, CREATE_EMPLOYEE_NUMBER, CREATE_DATETIME, UPDATE_EMPLOYEE_NUMBER, UPDATE_DATETIME)
  VALUES(1869, NULL, NULL, NULL, NULL, 'D6240', 18, 'SBI', '1', 110840, TO_TIMESTAMP('2014-01-14 16:25:21:0','YYYY-MM-DD HH24:MI:SS:FF'), NULL, NULL);
INSERT INTO attachment_requirement(ATTACHMENT_REQUIREMENT_ID, PLAN_TYPE, INS_COMPANY_ID, INSURANCE_PLAN, PROVIDER_TAXONOMY, PROCEDURE_CODE, REQUIREMENT_ID, VENDOR_TYPE, IS_ACTIVE, CREATE_EMPLOYEE_NUMBER, CREATE_DATETIME, UPDATE_EMPLOYEE_NUMBER, UPDATE_DATETIME)
  VALUES(1870, NULL, NULL, NULL, NULL, 'D6240', 26, 'SBI', '1', 110840, TO_TIMESTAMP('2014-01-14 16:25:21:0','YYYY-MM-DD HH24:MI:SS:FF'), NULL, NULL);
INSERT INTO attachment_requirement(ATTACHMENT_REQUIREMENT_ID, PLAN_TYPE, INS_COMPANY_ID, INSURANCE_PLAN, PROVIDER_TAXONOMY, PROCEDURE_CODE, REQUIREMENT_ID, VENDOR_TYPE, IS_ACTIVE, CREATE_EMPLOYEE_NUMBER, CREATE_DATETIME, UPDATE_EMPLOYEE_NUMBER, UPDATE_DATETIME)
  VALUES(1872, NULL, NULL, NULL, NULL, 'D6241', 18, 'SBI', '1', 110840, TO_TIMESTAMP('2014-01-14 16:25:21:0','YYYY-MM-DD HH24:MI:SS:FF'), NULL, NULL);
INSERT INTO attachment_requirement(ATTACHMENT_REQUIREMENT_ID, PLAN_TYPE, INS_COMPANY_ID, INSURANCE_PLAN, PROVIDER_TAXONOMY, PROCEDURE_CODE, REQUIREMENT_ID, VENDOR_TYPE, IS_ACTIVE, CREATE_EMPLOYEE_NUMBER, CREATE_DATETIME, UPDATE_EMPLOYEE_NUMBER, UPDATE_DATETIME)
  VALUES(1873, NULL, NULL, NULL, NULL, 'D6241', 26, 'SBI', '1', 110840, TO_TIMESTAMP('2014-01-14 16:25:21:0','YYYY-MM-DD HH24:MI:SS:FF'), NULL, NULL);
INSERT INTO attachment_requirement(ATTACHMENT_REQUIREMENT_ID, PLAN_TYPE, INS_COMPANY_ID, INSURANCE_PLAN, PROVIDER_TAXONOMY, PROCEDURE_CODE, REQUIREMENT_ID, VENDOR_TYPE, IS_ACTIVE, CREATE_EMPLOYEE_NUMBER, CREATE_DATETIME, UPDATE_EMPLOYEE_NUMBER, UPDATE_DATETIME)
  VALUES(1874, NULL, NULL, NULL, NULL, 'D4264', 1, 'SBI', '1', 110840, TO_TIMESTAMP('2014-01-14 16:25:21:0','YYYY-MM-DD HH24:MI:SS:FF'), NULL, NULL);
INSERT INTO attachment_requirement(ATTACHMENT_REQUIREMENT_ID, PLAN_TYPE, INS_COMPANY_ID, INSURANCE_PLAN, PROVIDER_TAXONOMY, PROCEDURE_CODE, REQUIREMENT_ID, VENDOR_TYPE, IS_ACTIVE, CREATE_EMPLOYEE_NUMBER, CREATE_DATETIME, UPDATE_EMPLOYEE_NUMBER, UPDATE_DATETIME)
  VALUES(1875, NULL, NULL, NULL, NULL, 'D4264', 18, 'SBI', '1', 110840, TO_TIMESTAMP('2014-01-14 16:25:21:0','YYYY-MM-DD HH24:MI:SS:FF'), NULL, NULL);
INSERT INTO attachment_requirement(ATTACHMENT_REQUIREMENT_ID, PLAN_TYPE, INS_COMPANY_ID, INSURANCE_PLAN, PROVIDER_TAXONOMY, PROCEDURE_CODE, REQUIREMENT_ID, VENDOR_TYPE, IS_ACTIVE, CREATE_EMPLOYEE_NUMBER, CREATE_DATETIME, UPDATE_EMPLOYEE_NUMBER, UPDATE_DATETIME)
  VALUES(1877, NULL, NULL, NULL, NULL, 'D6242', 18, 'SBI', '1', 110840, TO_TIMESTAMP('2014-01-14 16:25:21:0','YYYY-MM-DD HH24:MI:SS:FF'), NULL, NULL);
INSERT INTO attachment_requirement(ATTACHMENT_REQUIREMENT_ID, PLAN_TYPE, INS_COMPANY_ID, INSURANCE_PLAN, PROVIDER_TAXONOMY, PROCEDURE_CODE, REQUIREMENT_ID, VENDOR_TYPE, IS_ACTIVE, CREATE_EMPLOYEE_NUMBER, CREATE_DATETIME, UPDATE_EMPLOYEE_NUMBER, UPDATE_DATETIME)
  VALUES(1878, NULL, NULL, NULL, NULL, 'D6242', 26, 'SBI', '1', 110840, TO_TIMESTAMP('2014-01-14 16:25:21:0','YYYY-MM-DD HH24:MI:SS:FF'), NULL, NULL);
INSERT INTO attachment_requirement(ATTACHMENT_REQUIREMENT_ID, PLAN_TYPE, INS_COMPANY_ID, INSURANCE_PLAN, PROVIDER_TAXONOMY, PROCEDURE_CODE, REQUIREMENT_ID, VENDOR_TYPE, IS_ACTIVE, CREATE_EMPLOYEE_NUMBER, CREATE_DATETIME, UPDATE_EMPLOYEE_NUMBER, UPDATE_DATETIME)
  VALUES(1880, NULL, NULL, NULL, NULL, 'D6245', 18, 'SBI', '1', 110840, TO_TIMESTAMP('2014-01-14 16:25:21:0','YYYY-MM-DD HH24:MI:SS:FF'), NULL, NULL);
INSERT INTO attachment_requirement(ATTACHMENT_REQUIREMENT_ID, PLAN_TYPE, INS_COMPANY_ID, INSURANCE_PLAN, PROVIDER_TAXONOMY, PROCEDURE_CODE, REQUIREMENT_ID, VENDOR_TYPE, IS_ACTIVE, CREATE_EMPLOYEE_NUMBER, CREATE_DATETIME, UPDATE_EMPLOYEE_NUMBER, UPDATE_DATETIME)
  VALUES(1881, NULL, NULL, NULL, NULL, 'D6248', 18, 'SBI', '1', 110840, TO_TIMESTAMP('2014-01-14 16:25:21:0','YYYY-MM-DD HH24:MI:SS:FF'), NULL, NULL);
INSERT INTO attachment_requirement(ATTACHMENT_REQUIREMENT_ID, PLAN_TYPE, INS_COMPANY_ID, INSURANCE_PLAN, PROVIDER_TAXONOMY, PROCEDURE_CODE, REQUIREMENT_ID, VENDOR_TYPE, IS_ACTIVE, CREATE_EMPLOYEE_NUMBER, CREATE_DATETIME, UPDATE_EMPLOYEE_NUMBER, UPDATE_DATETIME)
  VALUES(1882, NULL, NULL, NULL, NULL, 'D6250', 18, 'SBI', '1', 110840, TO_TIMESTAMP('2014-01-14 16:25:21:0','YYYY-MM-DD HH24:MI:SS:FF'), NULL, NULL);
INSERT INTO attachment_requirement(ATTACHMENT_REQUIREMENT_ID, PLAN_TYPE, INS_COMPANY_ID, INSURANCE_PLAN, PROVIDER_TAXONOMY, PROCEDURE_CODE, REQUIREMENT_ID, VENDOR_TYPE, IS_ACTIVE, CREATE_EMPLOYEE_NUMBER, CREATE_DATETIME, UPDATE_EMPLOYEE_NUMBER, UPDATE_DATETIME)
  VALUES(1883, NULL, NULL, NULL, NULL, 'D6251', 18, 'SBI', '1', 110840, TO_TIMESTAMP('2014-01-14 16:25:21:0','YYYY-MM-DD HH24:MI:SS:FF'), NULL, NULL);
INSERT INTO attachment_requirement(ATTACHMENT_REQUIREMENT_ID, PLAN_TYPE, INS_COMPANY_ID, INSURANCE_PLAN, PROVIDER_TAXONOMY, PROCEDURE_CODE, REQUIREMENT_ID, VENDOR_TYPE, IS_ACTIVE, CREATE_EMPLOYEE_NUMBER, CREATE_DATETIME, UPDATE_EMPLOYEE_NUMBER, UPDATE_DATETIME)
  VALUES(1884, NULL, NULL, NULL, NULL, 'D6252', 18, 'SBI', '1', 110840, TO_TIMESTAMP('2014-01-14 16:25:21:0','YYYY-MM-DD HH24:MI:SS:FF'), NULL, NULL);
INSERT INTO attachment_requirement(ATTACHMENT_REQUIREMENT_ID, PLAN_TYPE, INS_COMPANY_ID, INSURANCE_PLAN, PROVIDER_TAXONOMY, PROCEDURE_CODE, REQUIREMENT_ID, VENDOR_TYPE, IS_ACTIVE, CREATE_EMPLOYEE_NUMBER, CREATE_DATETIME, UPDATE_EMPLOYEE_NUMBER, UPDATE_DATETIME)
  VALUES(1885, NULL, NULL, NULL, NULL, 'D6253', 18, 'SBI', '1', 110840, TO_TIMESTAMP('2014-01-14 16:25:21:0','YYYY-MM-DD HH24:MI:SS:FF'), NULL, NULL);
INSERT INTO attachment_requirement(ATTACHMENT_REQUIREMENT_ID, PLAN_TYPE, INS_COMPANY_ID, INSURANCE_PLAN, PROVIDER_TAXONOMY, PROCEDURE_CODE, REQUIREMENT_ID, VENDOR_TYPE, IS_ACTIVE, CREATE_EMPLOYEE_NUMBER, CREATE_DATETIME, UPDATE_EMPLOYEE_NUMBER, UPDATE_DATETIME)
  VALUES(1886, NULL, NULL, NULL, NULL, 'D6600', 2, 'SBI', '1', 110840, TO_TIMESTAMP('2014-01-14 16:25:21:0','YYYY-MM-DD HH24:MI:SS:FF'), NULL, NULL);
INSERT INTO attachment_requirement(ATTACHMENT_REQUIREMENT_ID, PLAN_TYPE, INS_COMPANY_ID, INSURANCE_PLAN, PROVIDER_TAXONOMY, PROCEDURE_CODE, REQUIREMENT_ID, VENDOR_TYPE, IS_ACTIVE, CREATE_EMPLOYEE_NUMBER, CREATE_DATETIME, UPDATE_EMPLOYEE_NUMBER, UPDATE_DATETIME)
  VALUES(1887, NULL, NULL, NULL, NULL, 'D6601', 2, 'SBI', '1', 110840, TO_TIMESTAMP('2014-01-14 16:25:21:0','YYYY-MM-DD HH24:MI:SS:FF'), NULL, NULL);
INSERT INTO attachment_requirement(ATTACHMENT_REQUIREMENT_ID, PLAN_TYPE, INS_COMPANY_ID, INSURANCE_PLAN, PROVIDER_TAXONOMY, PROCEDURE_CODE, REQUIREMENT_ID, VENDOR_TYPE, IS_ACTIVE, CREATE_EMPLOYEE_NUMBER, CREATE_DATETIME, UPDATE_EMPLOYEE_NUMBER, UPDATE_DATETIME)
  VALUES(1888, NULL, NULL, NULL, NULL, 'D6602', 2, 'SBI', '1', 110840, TO_TIMESTAMP('2014-01-14 16:25:21:0','YYYY-MM-DD HH24:MI:SS:FF'), NULL, NULL);
INSERT INTO attachment_requirement(ATTACHMENT_REQUIREMENT_ID, PLAN_TYPE, INS_COMPANY_ID, INSURANCE_PLAN, PROVIDER_TAXONOMY, PROCEDURE_CODE, REQUIREMENT_ID, VENDOR_TYPE, IS_ACTIVE, CREATE_EMPLOYEE_NUMBER, CREATE_DATETIME, UPDATE_EMPLOYEE_NUMBER, UPDATE_DATETIME)
  VALUES(1889, NULL, NULL, NULL, NULL, 'D6603', 2, 'SBI', '1', 110840, TO_TIMESTAMP('2014-01-14 16:25:21:0','YYYY-MM-DD HH24:MI:SS:FF'), NULL, NULL);
INSERT INTO attachment_requirement(ATTACHMENT_REQUIREMENT_ID, PLAN_TYPE, INS_COMPANY_ID, INSURANCE_PLAN, PROVIDER_TAXONOMY, PROCEDURE_CODE, REQUIREMENT_ID, VENDOR_TYPE, IS_ACTIVE, CREATE_EMPLOYEE_NUMBER, CREATE_DATETIME, UPDATE_EMPLOYEE_NUMBER, UPDATE_DATETIME)
  VALUES(1890, NULL, NULL, NULL, NULL, 'D6604', 2, 'SBI', '1', 110840, TO_TIMESTAMP('2014-01-14 16:25:21:0','YYYY-MM-DD HH24:MI:SS:FF'), NULL, NULL);
INSERT INTO attachment_requirement(ATTACHMENT_REQUIREMENT_ID, PLAN_TYPE, INS_COMPANY_ID, INSURANCE_PLAN, PROVIDER_TAXONOMY, PROCEDURE_CODE, REQUIREMENT_ID, VENDOR_TYPE, IS_ACTIVE, CREATE_EMPLOYEE_NUMBER, CREATE_DATETIME, UPDATE_EMPLOYEE_NUMBER, UPDATE_DATETIME)
  VALUES(1891, NULL, NULL, NULL, NULL, 'D6605', 2, 'SBI', '1', 110840, TO_TIMESTAMP('2014-01-14 16:25:21:0','YYYY-MM-DD HH24:MI:SS:FF'), NULL, NULL);
INSERT INTO attachment_requirement(ATTACHMENT_REQUIREMENT_ID, PLAN_TYPE, INS_COMPANY_ID, INSURANCE_PLAN, PROVIDER_TAXONOMY, PROCEDURE_CODE, REQUIREMENT_ID, VENDOR_TYPE, IS_ACTIVE, CREATE_EMPLOYEE_NUMBER, CREATE_DATETIME, UPDATE_EMPLOYEE_NUMBER, UPDATE_DATETIME)
  VALUES(1892, NULL, NULL, NULL, NULL, 'D6245', 26, 'SBI', '1', 110840, TO_TIMESTAMP('2014-01-14 16:25:21:0','YYYY-MM-DD HH24:MI:SS:FF'), NULL, NULL);
INSERT INTO attachment_requirement(ATTACHMENT_REQUIREMENT_ID, PLAN_TYPE, INS_COMPANY_ID, INSURANCE_PLAN, PROVIDER_TAXONOMY, PROCEDURE_CODE, REQUIREMENT_ID, VENDOR_TYPE, IS_ACTIVE, CREATE_EMPLOYEE_NUMBER, CREATE_DATETIME, UPDATE_EMPLOYEE_NUMBER, UPDATE_DATETIME)
  VALUES(1894, NULL, NULL, NULL, NULL, 'D6606', 2, 'SBI', '1', 110840, TO_TIMESTAMP('2014-01-14 16:25:21:0','YYYY-MM-DD HH24:MI:SS:FF'), NULL, NULL);
INSERT INTO attachment_requirement(ATTACHMENT_REQUIREMENT_ID, PLAN_TYPE, INS_COMPANY_ID, INSURANCE_PLAN, PROVIDER_TAXONOMY, PROCEDURE_CODE, REQUIREMENT_ID, VENDOR_TYPE, IS_ACTIVE, CREATE_EMPLOYEE_NUMBER, CREATE_DATETIME, UPDATE_EMPLOYEE_NUMBER, UPDATE_DATETIME)
  VALUES(1895, NULL, NULL, NULL, NULL, 'D6607', 2, 'SBI', '1', 110840, TO_TIMESTAMP('2014-01-14 16:25:21:0','YYYY-MM-DD HH24:MI:SS:FF'), NULL, NULL);
INSERT INTO attachment_requirement(ATTACHMENT_REQUIREMENT_ID, PLAN_TYPE, INS_COMPANY_ID, INSURANCE_PLAN, PROVIDER_TAXONOMY, PROCEDURE_CODE, REQUIREMENT_ID, VENDOR_TYPE, IS_ACTIVE, CREATE_EMPLOYEE_NUMBER, CREATE_DATETIME, UPDATE_EMPLOYEE_NUMBER, UPDATE_DATETIME)
  VALUES(1896, NULL, NULL, NULL, NULL, 'D6608', 2, 'SBI', '1', 110840, TO_TIMESTAMP('2014-01-14 16:25:21:0','YYYY-MM-DD HH24:MI:SS:FF'), NULL, NULL);
INSERT INTO attachment_requirement(ATTACHMENT_REQUIREMENT_ID, PLAN_TYPE, INS_COMPANY_ID, INSURANCE_PLAN, PROVIDER_TAXONOMY, PROCEDURE_CODE, REQUIREMENT_ID, VENDOR_TYPE, IS_ACTIVE, CREATE_EMPLOYEE_NUMBER, CREATE_DATETIME, UPDATE_EMPLOYEE_NUMBER, UPDATE_DATETIME)
  VALUES(1897, NULL, NULL, NULL, NULL, 'D6609', 2, 'SBI', '1', 110840, TO_TIMESTAMP('2014-01-14 16:25:21:0','YYYY-MM-DD HH24:MI:SS:FF'), NULL, NULL);
INSERT INTO attachment_requirement(ATTACHMENT_REQUIREMENT_ID, PLAN_TYPE, INS_COMPANY_ID, INSURANCE_PLAN, PROVIDER_TAXONOMY, PROCEDURE_CODE, REQUIREMENT_ID, VENDOR_TYPE, IS_ACTIVE, CREATE_EMPLOYEE_NUMBER, CREATE_DATETIME, UPDATE_EMPLOYEE_NUMBER, UPDATE_DATETIME)
  VALUES(1898, NULL, NULL, NULL, NULL, 'D6610', 2, 'SBI', '1', 110840, TO_TIMESTAMP('2014-01-14 16:25:21:0','YYYY-MM-DD HH24:MI:SS:FF'), NULL, NULL);
INSERT INTO attachment_requirement(ATTACHMENT_REQUIREMENT_ID, PLAN_TYPE, INS_COMPANY_ID, INSURANCE_PLAN, PROVIDER_TAXONOMY, PROCEDURE_CODE, REQUIREMENT_ID, VENDOR_TYPE, IS_ACTIVE, CREATE_EMPLOYEE_NUMBER, CREATE_DATETIME, UPDATE_EMPLOYEE_NUMBER, UPDATE_DATETIME)
  VALUES(1899, NULL, NULL, NULL, NULL, 'D6611', 2, 'SBI', '1', 110840, TO_TIMESTAMP('2014-01-14 16:25:21:0','YYYY-MM-DD HH24:MI:SS:FF'), NULL, NULL);
INSERT INTO attachment_requirement(ATTACHMENT_REQUIREMENT_ID, PLAN_TYPE, INS_COMPANY_ID, INSURANCE_PLAN, PROVIDER_TAXONOMY, PROCEDURE_CODE, REQUIREMENT_ID, VENDOR_TYPE, IS_ACTIVE, CREATE_EMPLOYEE_NUMBER, CREATE_DATETIME, UPDATE_EMPLOYEE_NUMBER, UPDATE_DATETIME)
  VALUES(1900, NULL, NULL, NULL, NULL, 'D6612', 2, 'SBI', '1', 110840, TO_TIMESTAMP('2014-01-14 16:25:21:0','YYYY-MM-DD HH24:MI:SS:FF'), NULL, NULL);
INSERT INTO attachment_requirement(ATTACHMENT_REQUIREMENT_ID, PLAN_TYPE, INS_COMPANY_ID, INSURANCE_PLAN, PROVIDER_TAXONOMY, PROCEDURE_CODE, REQUIREMENT_ID, VENDOR_TYPE, IS_ACTIVE, CREATE_EMPLOYEE_NUMBER, CREATE_DATETIME, UPDATE_EMPLOYEE_NUMBER, UPDATE_DATETIME)
  VALUES(1901, NULL, NULL, NULL, NULL, 'D6613', 2, 'SBI', '1', 110840, TO_TIMESTAMP('2014-01-14 16:25:21:0','YYYY-MM-DD HH24:MI:SS:FF'), NULL, NULL);
INSERT INTO attachment_requirement(ATTACHMENT_REQUIREMENT_ID, PLAN_TYPE, INS_COMPANY_ID, INSURANCE_PLAN, PROVIDER_TAXONOMY, PROCEDURE_CODE, REQUIREMENT_ID, VENDOR_TYPE, IS_ACTIVE, CREATE_EMPLOYEE_NUMBER, CREATE_DATETIME, UPDATE_EMPLOYEE_NUMBER, UPDATE_DATETIME)
  VALUES(1902, NULL, NULL, NULL, NULL, 'D6614', 2, 'SBI', '1', 110840, TO_TIMESTAMP('2014-01-14 16:25:21:0','YYYY-MM-DD HH24:MI:SS:FF'), NULL, NULL);
INSERT INTO attachment_requirement(ATTACHMENT_REQUIREMENT_ID, PLAN_TYPE, INS_COMPANY_ID, INSURANCE_PLAN, PROVIDER_TAXONOMY, PROCEDURE_CODE, REQUIREMENT_ID, VENDOR_TYPE, IS_ACTIVE, CREATE_EMPLOYEE_NUMBER, CREATE_DATETIME, UPDATE_EMPLOYEE_NUMBER, UPDATE_DATETIME)
  VALUES(1903, NULL, NULL, NULL, NULL, 'D6615', 2, 'SBI', '1', 110840, TO_TIMESTAMP('2014-01-14 16:25:21:0','YYYY-MM-DD HH24:MI:SS:FF'), NULL, NULL);
INSERT INTO attachment_requirement(ATTACHMENT_REQUIREMENT_ID, PLAN_TYPE, INS_COMPANY_ID, INSURANCE_PLAN, PROVIDER_TAXONOMY, PROCEDURE_CODE, REQUIREMENT_ID, VENDOR_TYPE, IS_ACTIVE, CREATE_EMPLOYEE_NUMBER, CREATE_DATETIME, UPDATE_EMPLOYEE_NUMBER, UPDATE_DATETIME)
  VALUES(1904, NULL, NULL, NULL, NULL, 'D6624', 2, 'SBI', '1', 110840, TO_TIMESTAMP('2014-01-14 16:25:21:0','YYYY-MM-DD HH24:MI:SS:FF'), NULL, NULL);
INSERT INTO attachment_requirement(ATTACHMENT_REQUIREMENT_ID, PLAN_TYPE, INS_COMPANY_ID, INSURANCE_PLAN, PROVIDER_TAXONOMY, PROCEDURE_CODE, REQUIREMENT_ID, VENDOR_TYPE, IS_ACTIVE, CREATE_EMPLOYEE_NUMBER, CREATE_DATETIME, UPDATE_EMPLOYEE_NUMBER, UPDATE_DATETIME)
  VALUES(1905, NULL, NULL, NULL, NULL, 'D6634', 2, 'SBI', '1', 110840, TO_TIMESTAMP('2014-01-14 16:25:21:0','YYYY-MM-DD HH24:MI:SS:FF'), NULL, NULL);
INSERT INTO attachment_requirement(ATTACHMENT_REQUIREMENT_ID, PLAN_TYPE, INS_COMPANY_ID, INSURANCE_PLAN, PROVIDER_TAXONOMY, PROCEDURE_CODE, REQUIREMENT_ID, VENDOR_TYPE, IS_ACTIVE, CREATE_EMPLOYEE_NUMBER, CREATE_DATETIME, UPDATE_EMPLOYEE_NUMBER, UPDATE_DATETIME)
  VALUES(1906, NULL, NULL, NULL, NULL, 'D6710', 2, 'SBI', '1', 110840, TO_TIMESTAMP('2014-01-14 16:25:21:0','YYYY-MM-DD HH24:MI:SS:FF'), NULL, NULL);
INSERT INTO attachment_requirement(ATTACHMENT_REQUIREMENT_ID, PLAN_TYPE, INS_COMPANY_ID, INSURANCE_PLAN, PROVIDER_TAXONOMY, PROCEDURE_CODE, REQUIREMENT_ID, VENDOR_TYPE, IS_ACTIVE, CREATE_EMPLOYEE_NUMBER, CREATE_DATETIME, UPDATE_EMPLOYEE_NUMBER, UPDATE_DATETIME)
  VALUES(1907, NULL, NULL, NULL, NULL, 'D6720', 2, 'SBI', '1', 110840, TO_TIMESTAMP('2014-01-14 16:25:21:0','YYYY-MM-DD HH24:MI:SS:FF'), NULL, NULL);
INSERT INTO attachment_requirement(ATTACHMENT_REQUIREMENT_ID, PLAN_TYPE, INS_COMPANY_ID, INSURANCE_PLAN, PROVIDER_TAXONOMY, PROCEDURE_CODE, REQUIREMENT_ID, VENDOR_TYPE, IS_ACTIVE, CREATE_EMPLOYEE_NUMBER, CREATE_DATETIME, UPDATE_EMPLOYEE_NUMBER, UPDATE_DATETIME)
  VALUES(1908, NULL, NULL, NULL, NULL, 'D6721', 2, 'SBI', '1', 110840, TO_TIMESTAMP('2014-01-14 16:25:21:0','YYYY-MM-DD HH24:MI:SS:FF'), NULL, NULL);
INSERT INTO attachment_requirement(ATTACHMENT_REQUIREMENT_ID, PLAN_TYPE, INS_COMPANY_ID, INSURANCE_PLAN, PROVIDER_TAXONOMY, PROCEDURE_CODE, REQUIREMENT_ID, VENDOR_TYPE, IS_ACTIVE, CREATE_EMPLOYEE_NUMBER, CREATE_DATETIME, UPDATE_EMPLOYEE_NUMBER, UPDATE_DATETIME)
  VALUES(1909, NULL, NULL, NULL, NULL, 'D6722', 2, 'SBI', '1', 110840, TO_TIMESTAMP('2014-01-14 16:25:21:0','YYYY-MM-DD HH24:MI:SS:FF'), NULL, NULL);
INSERT INTO attachment_requirement(ATTACHMENT_REQUIREMENT_ID, PLAN_TYPE, INS_COMPANY_ID, INSURANCE_PLAN, PROVIDER_TAXONOMY, PROCEDURE_CODE, REQUIREMENT_ID, VENDOR_TYPE, IS_ACTIVE, CREATE_EMPLOYEE_NUMBER, CREATE_DATETIME, UPDATE_EMPLOYEE_NUMBER, UPDATE_DATETIME)
  VALUES(1910, NULL, NULL, NULL, NULL, 'D6740', 18, 'SBI', '1', 110840, TO_TIMESTAMP('2014-01-14 16:25:21:0','YYYY-MM-DD HH24:MI:SS:FF'), NULL, NULL);
INSERT INTO attachment_requirement(ATTACHMENT_REQUIREMENT_ID, PLAN_TYPE, INS_COMPANY_ID, INSURANCE_PLAN, PROVIDER_TAXONOMY, PROCEDURE_CODE, REQUIREMENT_ID, VENDOR_TYPE, IS_ACTIVE, CREATE_EMPLOYEE_NUMBER, CREATE_DATETIME, UPDATE_EMPLOYEE_NUMBER, UPDATE_DATETIME)
  VALUES(1911, NULL, NULL, NULL, NULL, 'D6740', 26, 'SBI', '1', 110840, TO_TIMESTAMP('2014-01-14 16:25:21:0','YYYY-MM-DD HH24:MI:SS:FF'), NULL, NULL);
INSERT INTO attachment_requirement(ATTACHMENT_REQUIREMENT_ID, PLAN_TYPE, INS_COMPANY_ID, INSURANCE_PLAN, PROVIDER_TAXONOMY, PROCEDURE_CODE, REQUIREMENT_ID, VENDOR_TYPE, IS_ACTIVE, CREATE_EMPLOYEE_NUMBER, CREATE_DATETIME, UPDATE_EMPLOYEE_NUMBER, UPDATE_DATETIME)
  VALUES(1913, NULL, NULL, NULL, NULL, 'D6750', 18, 'SBI', '1', 110840, TO_TIMESTAMP('2014-01-14 16:25:21:0','YYYY-MM-DD HH24:MI:SS:FF'), NULL, NULL);
INSERT INTO attachment_requirement(ATTACHMENT_REQUIREMENT_ID, PLAN_TYPE, INS_COMPANY_ID, INSURANCE_PLAN, PROVIDER_TAXONOMY, PROCEDURE_CODE, REQUIREMENT_ID, VENDOR_TYPE, IS_ACTIVE, CREATE_EMPLOYEE_NUMBER, CREATE_DATETIME, UPDATE_EMPLOYEE_NUMBER, UPDATE_DATETIME)
  VALUES(1914, NULL, NULL, NULL, NULL, 'D6750', 26, 'SBI', '1', 110840, TO_TIMESTAMP('2014-01-14 16:25:21:0','YYYY-MM-DD HH24:MI:SS:FF'), NULL, NULL);
INSERT INTO attachment_requirement(ATTACHMENT_REQUIREMENT_ID, PLAN_TYPE, INS_COMPANY_ID, INSURANCE_PLAN, PROVIDER_TAXONOMY, PROCEDURE_CODE, REQUIREMENT_ID, VENDOR_TYPE, IS_ACTIVE, CREATE_EMPLOYEE_NUMBER, CREATE_DATETIME, UPDATE_EMPLOYEE_NUMBER, UPDATE_DATETIME)
  VALUES(1916, NULL, NULL, NULL, NULL, 'D6751', 18, 'SBI', '1', 110840, TO_TIMESTAMP('2014-01-14 16:25:21:0','YYYY-MM-DD HH24:MI:SS:FF'), NULL, NULL);
INSERT INTO attachment_requirement(ATTACHMENT_REQUIREMENT_ID, PLAN_TYPE, INS_COMPANY_ID, INSURANCE_PLAN, PROVIDER_TAXONOMY, PROCEDURE_CODE, REQUIREMENT_ID, VENDOR_TYPE, IS_ACTIVE, CREATE_EMPLOYEE_NUMBER, CREATE_DATETIME, UPDATE_EMPLOYEE_NUMBER, UPDATE_DATETIME)
  VALUES(1917, NULL, NULL, NULL, NULL, 'D6751', 26, 'SBI', '1', 110840, TO_TIMESTAMP('2014-01-14 16:25:21:0','YYYY-MM-DD HH24:MI:SS:FF'), NULL, NULL);
INSERT INTO attachment_requirement(ATTACHMENT_REQUIREMENT_ID, PLAN_TYPE, INS_COMPANY_ID, INSURANCE_PLAN, PROVIDER_TAXONOMY, PROCEDURE_CODE, REQUIREMENT_ID, VENDOR_TYPE, IS_ACTIVE, CREATE_EMPLOYEE_NUMBER, CREATE_DATETIME, UPDATE_EMPLOYEE_NUMBER, UPDATE_DATETIME)
  VALUES(1919, NULL, NULL, NULL, NULL, 'D6752', 18, 'SBI', '1', 110840, TO_TIMESTAMP('2014-01-14 16:25:21:0','YYYY-MM-DD HH24:MI:SS:FF'), NULL, NULL);
INSERT INTO attachment_requirement(ATTACHMENT_REQUIREMENT_ID, PLAN_TYPE, INS_COMPANY_ID, INSURANCE_PLAN, PROVIDER_TAXONOMY, PROCEDURE_CODE, REQUIREMENT_ID, VENDOR_TYPE, IS_ACTIVE, CREATE_EMPLOYEE_NUMBER, CREATE_DATETIME, UPDATE_EMPLOYEE_NUMBER, UPDATE_DATETIME)
  VALUES(1920, NULL, NULL, NULL, NULL, 'D6752', 26, 'SBI', '1', 110840, TO_TIMESTAMP('2014-01-14 16:25:21:0','YYYY-MM-DD HH24:MI:SS:FF'), NULL, NULL);
INSERT INTO attachment_requirement(ATTACHMENT_REQUIREMENT_ID, PLAN_TYPE, INS_COMPANY_ID, INSURANCE_PLAN, PROVIDER_TAXONOMY, PROCEDURE_CODE, REQUIREMENT_ID, VENDOR_TYPE, IS_ACTIVE, CREATE_EMPLOYEE_NUMBER, CREATE_DATETIME, UPDATE_EMPLOYEE_NUMBER, UPDATE_DATETIME)
  VALUES(1922, NULL, NULL, NULL, NULL, 'D6780', 2, 'SBI', '1', 110840, TO_TIMESTAMP('2014-01-14 16:25:21:0','YYYY-MM-DD HH24:MI:SS:FF'), NULL, NULL);
INSERT INTO attachment_requirement(ATTACHMENT_REQUIREMENT_ID, PLAN_TYPE, INS_COMPANY_ID, INSURANCE_PLAN, PROVIDER_TAXONOMY, PROCEDURE_CODE, REQUIREMENT_ID, VENDOR_TYPE, IS_ACTIVE, CREATE_EMPLOYEE_NUMBER, CREATE_DATETIME, UPDATE_EMPLOYEE_NUMBER, UPDATE_DATETIME)
  VALUES(1923, NULL, NULL, NULL, NULL, 'D6781', 2, 'SBI', '1', 110840, TO_TIMESTAMP('2014-01-14 16:25:21:0','YYYY-MM-DD HH24:MI:SS:FF'), NULL, NULL);
INSERT INTO attachment_requirement(ATTACHMENT_REQUIREMENT_ID, PLAN_TYPE, INS_COMPANY_ID, INSURANCE_PLAN, PROVIDER_TAXONOMY, PROCEDURE_CODE, REQUIREMENT_ID, VENDOR_TYPE, IS_ACTIVE, CREATE_EMPLOYEE_NUMBER, CREATE_DATETIME, UPDATE_EMPLOYEE_NUMBER, UPDATE_DATETIME)
  VALUES(1924, NULL, NULL, NULL, NULL, 'D6782', 2, 'SBI', '1', 110840, TO_TIMESTAMP('2014-01-14 16:25:21:0','YYYY-MM-DD HH24:MI:SS:FF'), NULL, NULL);
INSERT INTO attachment_requirement(ATTACHMENT_REQUIREMENT_ID, PLAN_TYPE, INS_COMPANY_ID, INSURANCE_PLAN, PROVIDER_TAXONOMY, PROCEDURE_CODE, REQUIREMENT_ID, VENDOR_TYPE, IS_ACTIVE, CREATE_EMPLOYEE_NUMBER, CREATE_DATETIME, UPDATE_EMPLOYEE_NUMBER, UPDATE_DATETIME)
  VALUES(1925, NULL, NULL, NULL, NULL, 'D6783', 2, 'SBI', '1', 110840, TO_TIMESTAMP('2014-01-14 16:25:21:0','YYYY-MM-DD HH24:MI:SS:FF'), NULL, NULL);
INSERT INTO attachment_requirement(ATTACHMENT_REQUIREMENT_ID, PLAN_TYPE, INS_COMPANY_ID, INSURANCE_PLAN, PROVIDER_TAXONOMY, PROCEDURE_CODE, REQUIREMENT_ID, VENDOR_TYPE, IS_ACTIVE, CREATE_EMPLOYEE_NUMBER, CREATE_DATETIME, UPDATE_EMPLOYEE_NUMBER, UPDATE_DATETIME)
  VALUES(1926, NULL, NULL, NULL, NULL, 'D6790', 18, 'SBI', '1', 110840, TO_TIMESTAMP('2014-01-14 16:25:21:0','YYYY-MM-DD HH24:MI:SS:FF'), NULL, NULL);
INSERT INTO attachment_requirement(ATTACHMENT_REQUIREMENT_ID, PLAN_TYPE, INS_COMPANY_ID, INSURANCE_PLAN, PROVIDER_TAXONOMY, PROCEDURE_CODE, REQUIREMENT_ID, VENDOR_TYPE, IS_ACTIVE, CREATE_EMPLOYEE_NUMBER, CREATE_DATETIME, UPDATE_EMPLOYEE_NUMBER, UPDATE_DATETIME)
  VALUES(1927, NULL, NULL, NULL, NULL, 'D6790', 26, 'SBI', '1', 110840, TO_TIMESTAMP('2014-01-14 16:25:21:0','YYYY-MM-DD HH24:MI:SS:FF'), NULL, NULL);
INSERT INTO attachment_requirement(ATTACHMENT_REQUIREMENT_ID, PLAN_TYPE, INS_COMPANY_ID, INSURANCE_PLAN, PROVIDER_TAXONOMY, PROCEDURE_CODE, REQUIREMENT_ID, VENDOR_TYPE, IS_ACTIVE, CREATE_EMPLOYEE_NUMBER, CREATE_DATETIME, UPDATE_EMPLOYEE_NUMBER, UPDATE_DATETIME)
  VALUES(1929, NULL, NULL, NULL, NULL, 'D6791', 18, 'SBI', '1', 110840, TO_TIMESTAMP('2014-01-14 16:25:21:0','YYYY-MM-DD HH24:MI:SS:FF'), NULL, NULL);
INSERT INTO attachment_requirement(ATTACHMENT_REQUIREMENT_ID, PLAN_TYPE, INS_COMPANY_ID, INSURANCE_PLAN, PROVIDER_TAXONOMY, PROCEDURE_CODE, REQUIREMENT_ID, VENDOR_TYPE, IS_ACTIVE, CREATE_EMPLOYEE_NUMBER, CREATE_DATETIME, UPDATE_EMPLOYEE_NUMBER, UPDATE_DATETIME)
  VALUES(1930, NULL, NULL, NULL, NULL, 'D6791', 26, 'SBI', '1', 110840, TO_TIMESTAMP('2014-01-14 16:25:21:0','YYYY-MM-DD HH24:MI:SS:FF'), NULL, NULL);
INSERT INTO attachment_requirement(ATTACHMENT_REQUIREMENT_ID, PLAN_TYPE, INS_COMPANY_ID, INSURANCE_PLAN, PROVIDER_TAXONOMY, PROCEDURE_CODE, REQUIREMENT_ID, VENDOR_TYPE, IS_ACTIVE, CREATE_EMPLOYEE_NUMBER, CREATE_DATETIME, UPDATE_EMPLOYEE_NUMBER, UPDATE_DATETIME)
  VALUES(1932, NULL, NULL, NULL, NULL, 'D6792', 18, 'SBI', '1', 110840, TO_TIMESTAMP('2014-01-14 16:25:21:0','YYYY-MM-DD HH24:MI:SS:FF'), NULL, NULL);
INSERT INTO attachment_requirement(ATTACHMENT_REQUIREMENT_ID, PLAN_TYPE, INS_COMPANY_ID, INSURANCE_PLAN, PROVIDER_TAXONOMY, PROCEDURE_CODE, REQUIREMENT_ID, VENDOR_TYPE, IS_ACTIVE, CREATE_EMPLOYEE_NUMBER, CREATE_DATETIME, UPDATE_EMPLOYEE_NUMBER, UPDATE_DATETIME)
  VALUES(1933, NULL, NULL, NULL, NULL, 'D6792', 26, 'SBI', '1', 110840, TO_TIMESTAMP('2014-01-14 16:25:21:0','YYYY-MM-DD HH24:MI:SS:FF'), NULL, NULL);
INSERT INTO attachment_requirement(ATTACHMENT_REQUIREMENT_ID, PLAN_TYPE, INS_COMPANY_ID, INSURANCE_PLAN, PROVIDER_TAXONOMY, PROCEDURE_CODE, REQUIREMENT_ID, VENDOR_TYPE, IS_ACTIVE, CREATE_EMPLOYEE_NUMBER, CREATE_DATETIME, UPDATE_EMPLOYEE_NUMBER, UPDATE_DATETIME)
  VALUES(1935, NULL, NULL, NULL, NULL, 'D6793', 2, 'SBI', '1', 110840, TO_TIMESTAMP('2014-01-14 16:25:21:0','YYYY-MM-DD HH24:MI:SS:FF'), NULL, NULL);
INSERT INTO attachment_requirement(ATTACHMENT_REQUIREMENT_ID, PLAN_TYPE, INS_COMPANY_ID, INSURANCE_PLAN, PROVIDER_TAXONOMY, PROCEDURE_CODE, REQUIREMENT_ID, VENDOR_TYPE, IS_ACTIVE, CREATE_EMPLOYEE_NUMBER, CREATE_DATETIME, UPDATE_EMPLOYEE_NUMBER, UPDATE_DATETIME)
  VALUES(1936, NULL, NULL, NULL, NULL, 'D6794', 2, 'SBI', '1', 110840, TO_TIMESTAMP('2014-01-14 16:25:21:0','YYYY-MM-DD HH24:MI:SS:FF'), NULL, NULL);
INSERT INTO attachment_requirement(ATTACHMENT_REQUIREMENT_ID, PLAN_TYPE, INS_COMPANY_ID, INSURANCE_PLAN, PROVIDER_TAXONOMY, PROCEDURE_CODE, REQUIREMENT_ID, VENDOR_TYPE, IS_ACTIVE, CREATE_EMPLOYEE_NUMBER, CREATE_DATETIME, UPDATE_EMPLOYEE_NUMBER, UPDATE_DATETIME)
  VALUES(1937, NULL, NULL, NULL, NULL, 'D6930', 26, 'SBI', '1', 110840, TO_TIMESTAMP('2014-01-14 16:25:21:0','YYYY-MM-DD HH24:MI:SS:FF'), NULL, NULL);
INSERT INTO attachment_requirement(ATTACHMENT_REQUIREMENT_ID, PLAN_TYPE, INS_COMPANY_ID, INSURANCE_PLAN, PROVIDER_TAXONOMY, PROCEDURE_CODE, REQUIREMENT_ID, VENDOR_TYPE, IS_ACTIVE, CREATE_EMPLOYEE_NUMBER, CREATE_DATETIME, UPDATE_EMPLOYEE_NUMBER, UPDATE_DATETIME)
  VALUES(1938, NULL, NULL, NULL, NULL, 'D6940', 26, 'SBI', '1', 110840, TO_TIMESTAMP('2014-01-14 16:25:21:0','YYYY-MM-DD HH24:MI:SS:FF'), NULL, NULL);
INSERT INTO attachment_requirement(ATTACHMENT_REQUIREMENT_ID, PLAN_TYPE, INS_COMPANY_ID, INSURANCE_PLAN, PROVIDER_TAXONOMY, PROCEDURE_CODE, REQUIREMENT_ID, VENDOR_TYPE, IS_ACTIVE, CREATE_EMPLOYEE_NUMBER, CREATE_DATETIME, UPDATE_EMPLOYEE_NUMBER, UPDATE_DATETIME)
  VALUES(1939, NULL, NULL, NULL, NULL, 'D6950', 26, 'SBI', '1', 110840, TO_TIMESTAMP('2014-01-14 16:25:21:0','YYYY-MM-DD HH24:MI:SS:FF'), NULL, NULL);
INSERT INTO attachment_requirement(ATTACHMENT_REQUIREMENT_ID, PLAN_TYPE, INS_COMPANY_ID, INSURANCE_PLAN, PROVIDER_TAXONOMY, PROCEDURE_CODE, REQUIREMENT_ID, VENDOR_TYPE, IS_ACTIVE, CREATE_EMPLOYEE_NUMBER, CREATE_DATETIME, UPDATE_EMPLOYEE_NUMBER, UPDATE_DATETIME)
  VALUES(1940, NULL, NULL, NULL, NULL, 'D6950', 18, 'SBI', '1', 110840, TO_TIMESTAMP('2014-01-14 16:25:21:0','YYYY-MM-DD HH24:MI:SS:FF'), NULL, NULL);
INSERT INTO attachment_requirement(ATTACHMENT_REQUIREMENT_ID, PLAN_TYPE, INS_COMPANY_ID, INSURANCE_PLAN, PROVIDER_TAXONOMY, PROCEDURE_CODE, REQUIREMENT_ID, VENDOR_TYPE, IS_ACTIVE, CREATE_EMPLOYEE_NUMBER, CREATE_DATETIME, UPDATE_EMPLOYEE_NUMBER, UPDATE_DATETIME)
  VALUES(1941, NULL, NULL, NULL, NULL, 'D6972', 26, 'SBI', '1', 110840, TO_TIMESTAMP('2014-01-14 16:25:21:0','YYYY-MM-DD HH24:MI:SS:FF'), NULL, NULL);
INSERT INTO attachment_requirement(ATTACHMENT_REQUIREMENT_ID, PLAN_TYPE, INS_COMPANY_ID, INSURANCE_PLAN, PROVIDER_TAXONOMY, PROCEDURE_CODE, REQUIREMENT_ID, VENDOR_TYPE, IS_ACTIVE, CREATE_EMPLOYEE_NUMBER, CREATE_DATETIME, UPDATE_EMPLOYEE_NUMBER, UPDATE_DATETIME)
  VALUES(1942, NULL, NULL, NULL, NULL, 'D6980', 18, 'SBI', '1', 110840, TO_TIMESTAMP('2014-01-14 16:25:21:0','YYYY-MM-DD HH24:MI:SS:FF'), NULL, NULL);
INSERT INTO attachment_requirement(ATTACHMENT_REQUIREMENT_ID, PLAN_TYPE, INS_COMPANY_ID, INSURANCE_PLAN, PROVIDER_TAXONOMY, PROCEDURE_CODE, REQUIREMENT_ID, VENDOR_TYPE, IS_ACTIVE, CREATE_EMPLOYEE_NUMBER, CREATE_DATETIME, UPDATE_EMPLOYEE_NUMBER, UPDATE_DATETIME)
  VALUES(1943, NULL, NULL, NULL, NULL, 'D6980', 26, 'SBI', '1', 110840, TO_TIMESTAMP('2014-01-14 16:25:21:0','YYYY-MM-DD HH24:MI:SS:FF'), NULL, NULL);
INSERT INTO attachment_requirement(ATTACHMENT_REQUIREMENT_ID, PLAN_TYPE, INS_COMPANY_ID, INSURANCE_PLAN, PROVIDER_TAXONOMY, PROCEDURE_CODE, REQUIREMENT_ID, VENDOR_TYPE, IS_ACTIVE, CREATE_EMPLOYEE_NUMBER, CREATE_DATETIME, UPDATE_EMPLOYEE_NUMBER, UPDATE_DATETIME)
  VALUES(1944, NULL, NULL, NULL, NULL, 'D6985', 18, 'SBI', '1', 110840, TO_TIMESTAMP('2014-01-14 16:25:21:0','YYYY-MM-DD HH24:MI:SS:FF'), NULL, NULL);
INSERT INTO attachment_requirement(ATTACHMENT_REQUIREMENT_ID, PLAN_TYPE, INS_COMPANY_ID, INSURANCE_PLAN, PROVIDER_TAXONOMY, PROCEDURE_CODE, REQUIREMENT_ID, VENDOR_TYPE, IS_ACTIVE, CREATE_EMPLOYEE_NUMBER, CREATE_DATETIME, UPDATE_EMPLOYEE_NUMBER, UPDATE_DATETIME)
  VALUES(1945, NULL, NULL, NULL, NULL, 'D6985', 26, 'SBI', '1', 110840, TO_TIMESTAMP('2014-01-14 16:25:21:0','YYYY-MM-DD HH24:MI:SS:FF'), NULL, NULL);
INSERT INTO attachment_requirement(ATTACHMENT_REQUIREMENT_ID, PLAN_TYPE, INS_COMPANY_ID, INSURANCE_PLAN, PROVIDER_TAXONOMY, PROCEDURE_CODE, REQUIREMENT_ID, VENDOR_TYPE, IS_ACTIVE, CREATE_EMPLOYEE_NUMBER, CREATE_DATETIME, UPDATE_EMPLOYEE_NUMBER, UPDATE_DATETIME)
  VALUES(1946, NULL, NULL, NULL, NULL, 'D7111', 18, 'SBI', '1', 110840, TO_TIMESTAMP('2014-01-14 16:25:21:0','YYYY-MM-DD HH24:MI:SS:FF'), NULL, NULL);
INSERT INTO attachment_requirement(ATTACHMENT_REQUIREMENT_ID, PLAN_TYPE, INS_COMPANY_ID, INSURANCE_PLAN, PROVIDER_TAXONOMY, PROCEDURE_CODE, REQUIREMENT_ID, VENDOR_TYPE, IS_ACTIVE, CREATE_EMPLOYEE_NUMBER, CREATE_DATETIME, UPDATE_EMPLOYEE_NUMBER, UPDATE_DATETIME)
  VALUES(1947, NULL, NULL, NULL, NULL, 'D7111', 26, 'SBI', '1', 110840, TO_TIMESTAMP('2014-01-14 16:25:21:0','YYYY-MM-DD HH24:MI:SS:FF'), NULL, NULL);
INSERT INTO attachment_requirement(ATTACHMENT_REQUIREMENT_ID, PLAN_TYPE, INS_COMPANY_ID, INSURANCE_PLAN, PROVIDER_TAXONOMY, PROCEDURE_CODE, REQUIREMENT_ID, VENDOR_TYPE, IS_ACTIVE, CREATE_EMPLOYEE_NUMBER, CREATE_DATETIME, UPDATE_EMPLOYEE_NUMBER, UPDATE_DATETIME)
  VALUES(1948, NULL, NULL, NULL, NULL, 'D7111', 60, 'SBI', '1', 110840, TO_TIMESTAMP('2014-01-14 16:25:21:0','YYYY-MM-DD HH24:MI:SS:FF'), NULL, NULL);
INSERT INTO attachment_requirement(ATTACHMENT_REQUIREMENT_ID, PLAN_TYPE, INS_COMPANY_ID, INSURANCE_PLAN, PROVIDER_TAXONOMY, PROCEDURE_CODE, REQUIREMENT_ID, VENDOR_TYPE, IS_ACTIVE, CREATE_EMPLOYEE_NUMBER, CREATE_DATETIME, UPDATE_EMPLOYEE_NUMBER, UPDATE_DATETIME)
  VALUES(1949, NULL, NULL, NULL, NULL, 'D7140', 60, 'SBI', '1', 110840, TO_TIMESTAMP('2014-01-14 16:25:21:0','YYYY-MM-DD HH24:MI:SS:FF'), NULL, NULL);
INSERT INTO attachment_requirement(ATTACHMENT_REQUIREMENT_ID, PLAN_TYPE, INS_COMPANY_ID, INSURANCE_PLAN, PROVIDER_TAXONOMY, PROCEDURE_CODE, REQUIREMENT_ID, VENDOR_TYPE, IS_ACTIVE, CREATE_EMPLOYEE_NUMBER, CREATE_DATETIME, UPDATE_EMPLOYEE_NUMBER, UPDATE_DATETIME)
  VALUES(1950, NULL, NULL, NULL, NULL, 'D7140', 26, 'SBI', '1', 110840, TO_TIMESTAMP('2014-01-14 16:25:21:0','YYYY-MM-DD HH24:MI:SS:FF'), NULL, NULL);
INSERT INTO attachment_requirement(ATTACHMENT_REQUIREMENT_ID, PLAN_TYPE, INS_COMPANY_ID, INSURANCE_PLAN, PROVIDER_TAXONOMY, PROCEDURE_CODE, REQUIREMENT_ID, VENDOR_TYPE, IS_ACTIVE, CREATE_EMPLOYEE_NUMBER, CREATE_DATETIME, UPDATE_EMPLOYEE_NUMBER, UPDATE_DATETIME)
  VALUES(1953, NULL, NULL, NULL, NULL, 'D7210', 26, 'SBI', '1', 110840, TO_TIMESTAMP('2014-01-14 16:25:21:0','YYYY-MM-DD HH24:MI:SS:FF'), NULL, NULL);
INSERT INTO attachment_requirement(ATTACHMENT_REQUIREMENT_ID, PLAN_TYPE, INS_COMPANY_ID, INSURANCE_PLAN, PROVIDER_TAXONOMY, PROCEDURE_CODE, REQUIREMENT_ID, VENDOR_TYPE, IS_ACTIVE, CREATE_EMPLOYEE_NUMBER, CREATE_DATETIME, UPDATE_EMPLOYEE_NUMBER, UPDATE_DATETIME)
  VALUES(1954, NULL, NULL, NULL, NULL, 'D7210', 32, 'SBI', '1', 110840, TO_TIMESTAMP('2014-01-14 16:25:21:0','YYYY-MM-DD HH24:MI:SS:FF'), NULL, NULL);
INSERT INTO attachment_requirement(ATTACHMENT_REQUIREMENT_ID, PLAN_TYPE, INS_COMPANY_ID, INSURANCE_PLAN, PROVIDER_TAXONOMY, PROCEDURE_CODE, REQUIREMENT_ID, VENDOR_TYPE, IS_ACTIVE, CREATE_EMPLOYEE_NUMBER, CREATE_DATETIME, UPDATE_EMPLOYEE_NUMBER, UPDATE_DATETIME)
  VALUES(1955, NULL, NULL, NULL, NULL, 'D7220', 32, 'SBI', '1', 110840, TO_TIMESTAMP('2014-01-14 16:25:21:0','YYYY-MM-DD HH24:MI:SS:FF'), NULL, NULL);
INSERT INTO attachment_requirement(ATTACHMENT_REQUIREMENT_ID, PLAN_TYPE, INS_COMPANY_ID, INSURANCE_PLAN, PROVIDER_TAXONOMY, PROCEDURE_CODE, REQUIREMENT_ID, VENDOR_TYPE, IS_ACTIVE, CREATE_EMPLOYEE_NUMBER, CREATE_DATETIME, UPDATE_EMPLOYEE_NUMBER, UPDATE_DATETIME)
  VALUES(1956, NULL, NULL, NULL, NULL, 'D7220', 26, 'SBI', '1', 110840, TO_TIMESTAMP('2014-01-14 16:25:21:0','YYYY-MM-DD HH24:MI:SS:FF'), NULL, NULL);
INSERT INTO attachment_requirement(ATTACHMENT_REQUIREMENT_ID, PLAN_TYPE, INS_COMPANY_ID, INSURANCE_PLAN, PROVIDER_TAXONOMY, PROCEDURE_CODE, REQUIREMENT_ID, VENDOR_TYPE, IS_ACTIVE, CREATE_EMPLOYEE_NUMBER, CREATE_DATETIME, UPDATE_EMPLOYEE_NUMBER, UPDATE_DATETIME)
  VALUES(1957, NULL, NULL, NULL, NULL, 'D7230', 32, 'SBI', '1', 110840, TO_TIMESTAMP('2014-01-14 16:25:21:0','YYYY-MM-DD HH24:MI:SS:FF'), NULL, NULL);
INSERT INTO attachment_requirement(ATTACHMENT_REQUIREMENT_ID, PLAN_TYPE, INS_COMPANY_ID, INSURANCE_PLAN, PROVIDER_TAXONOMY, PROCEDURE_CODE, REQUIREMENT_ID, VENDOR_TYPE, IS_ACTIVE, CREATE_EMPLOYEE_NUMBER, CREATE_DATETIME, UPDATE_EMPLOYEE_NUMBER, UPDATE_DATETIME)
  VALUES(1958, NULL, NULL, NULL, NULL, 'D7230', 26, 'SBI', '1', 110840, TO_TIMESTAMP('2014-01-14 16:25:21:0','YYYY-MM-DD HH24:MI:SS:FF'), NULL, NULL);
INSERT INTO attachment_requirement(ATTACHMENT_REQUIREMENT_ID, PLAN_TYPE, INS_COMPANY_ID, INSURANCE_PLAN, PROVIDER_TAXONOMY, PROCEDURE_CODE, REQUIREMENT_ID, VENDOR_TYPE, IS_ACTIVE, CREATE_EMPLOYEE_NUMBER, CREATE_DATETIME, UPDATE_EMPLOYEE_NUMBER, UPDATE_DATETIME)
  VALUES(1959, NULL, NULL, NULL, NULL, 'D7240', 32, 'SBI', '1', 110840, TO_TIMESTAMP('2014-01-14 16:25:21:0','YYYY-MM-DD HH24:MI:SS:FF'), NULL, NULL);
INSERT INTO attachment_requirement(ATTACHMENT_REQUIREMENT_ID, PLAN_TYPE, INS_COMPANY_ID, INSURANCE_PLAN, PROVIDER_TAXONOMY, PROCEDURE_CODE, REQUIREMENT_ID, VENDOR_TYPE, IS_ACTIVE, CREATE_EMPLOYEE_NUMBER, CREATE_DATETIME, UPDATE_EMPLOYEE_NUMBER, UPDATE_DATETIME)
  VALUES(1960, NULL, NULL, NULL, NULL, 'D7240', 26, 'SBI', '1', 110840, TO_TIMESTAMP('2014-01-14 16:25:21:0','YYYY-MM-DD HH24:MI:SS:FF'), NULL, NULL);
INSERT INTO attachment_requirement(ATTACHMENT_REQUIREMENT_ID, PLAN_TYPE, INS_COMPANY_ID, INSURANCE_PLAN, PROVIDER_TAXONOMY, PROCEDURE_CODE, REQUIREMENT_ID, VENDOR_TYPE, IS_ACTIVE, CREATE_EMPLOYEE_NUMBER, CREATE_DATETIME, UPDATE_EMPLOYEE_NUMBER, UPDATE_DATETIME)
  VALUES(1961, NULL, NULL, NULL, NULL, 'D7241', 32, 'SBI', '1', 110840, TO_TIMESTAMP('2014-01-14 16:25:21:0','YYYY-MM-DD HH24:MI:SS:FF'), NULL, NULL);
INSERT INTO attachment_requirement(ATTACHMENT_REQUIREMENT_ID, PLAN_TYPE, INS_COMPANY_ID, INSURANCE_PLAN, PROVIDER_TAXONOMY, PROCEDURE_CODE, REQUIREMENT_ID, VENDOR_TYPE, IS_ACTIVE, CREATE_EMPLOYEE_NUMBER, CREATE_DATETIME, UPDATE_EMPLOYEE_NUMBER, UPDATE_DATETIME)
  VALUES(1962, NULL, NULL, NULL, NULL, 'D7241', 26, 'SBI', '1', 110840, TO_TIMESTAMP('2014-01-14 16:25:21:0','YYYY-MM-DD HH24:MI:SS:FF'), NULL, NULL);
INSERT INTO attachment_requirement(ATTACHMENT_REQUIREMENT_ID, PLAN_TYPE, INS_COMPANY_ID, INSURANCE_PLAN, PROVIDER_TAXONOMY, PROCEDURE_CODE, REQUIREMENT_ID, VENDOR_TYPE, IS_ACTIVE, CREATE_EMPLOYEE_NUMBER, CREATE_DATETIME, UPDATE_EMPLOYEE_NUMBER, UPDATE_DATETIME)
  VALUES(1963, NULL, NULL, NULL, NULL, 'D7250', 60, 'SBI', '1', 110840, TO_TIMESTAMP('2014-01-14 16:25:21:0','YYYY-MM-DD HH24:MI:SS:FF'), NULL, NULL);
INSERT INTO attachment_requirement(ATTACHMENT_REQUIREMENT_ID, PLAN_TYPE, INS_COMPANY_ID, INSURANCE_PLAN, PROVIDER_TAXONOMY, PROCEDURE_CODE, REQUIREMENT_ID, VENDOR_TYPE, IS_ACTIVE, CREATE_EMPLOYEE_NUMBER, CREATE_DATETIME, UPDATE_EMPLOYEE_NUMBER, UPDATE_DATETIME)
  VALUES(1964, NULL, NULL, NULL, NULL, 'D7250', 26, 'SBI', '1', 110840, TO_TIMESTAMP('2014-01-14 16:25:21:0','YYYY-MM-DD HH24:MI:SS:FF'), NULL, NULL);
INSERT INTO attachment_requirement(ATTACHMENT_REQUIREMENT_ID, PLAN_TYPE, INS_COMPANY_ID, INSURANCE_PLAN, PROVIDER_TAXONOMY, PROCEDURE_CODE, REQUIREMENT_ID, VENDOR_TYPE, IS_ACTIVE, CREATE_EMPLOYEE_NUMBER, CREATE_DATETIME, UPDATE_EMPLOYEE_NUMBER, UPDATE_DATETIME)
  VALUES(1965, NULL, NULL, NULL, NULL, 'D7250', 32, 'SBI', '1', 110840, TO_TIMESTAMP('2014-01-14 16:25:21:0','YYYY-MM-DD HH24:MI:SS:FF'), NULL, NULL);
INSERT INTO attachment_requirement(ATTACHMENT_REQUIREMENT_ID, PLAN_TYPE, INS_COMPANY_ID, INSURANCE_PLAN, PROVIDER_TAXONOMY, PROCEDURE_CODE, REQUIREMENT_ID, VENDOR_TYPE, IS_ACTIVE, CREATE_EMPLOYEE_NUMBER, CREATE_DATETIME, UPDATE_EMPLOYEE_NUMBER, UPDATE_DATETIME)
  VALUES(1966, NULL, NULL, NULL, NULL, 'D7260', 32, 'SBI', '1', 110840, TO_TIMESTAMP('2014-01-14 16:25:21:0','YYYY-MM-DD HH24:MI:SS:FF'), NULL, NULL);
INSERT INTO attachment_requirement(ATTACHMENT_REQUIREMENT_ID, PLAN_TYPE, INS_COMPANY_ID, INSURANCE_PLAN, PROVIDER_TAXONOMY, PROCEDURE_CODE, REQUIREMENT_ID, VENDOR_TYPE, IS_ACTIVE, CREATE_EMPLOYEE_NUMBER, CREATE_DATETIME, UPDATE_EMPLOYEE_NUMBER, UPDATE_DATETIME)
  VALUES(1967, NULL, NULL, NULL, NULL, 'D7260', 26, 'SBI', '1', 110840, TO_TIMESTAMP('2014-01-14 16:25:21:0','YYYY-MM-DD HH24:MI:SS:FF'), NULL, NULL);
INSERT INTO attachment_requirement(ATTACHMENT_REQUIREMENT_ID, PLAN_TYPE, INS_COMPANY_ID, INSURANCE_PLAN, PROVIDER_TAXONOMY, PROCEDURE_CODE, REQUIREMENT_ID, VENDOR_TYPE, IS_ACTIVE, CREATE_EMPLOYEE_NUMBER, CREATE_DATETIME, UPDATE_EMPLOYEE_NUMBER, UPDATE_DATETIME)
  VALUES(1968, NULL, NULL, NULL, NULL, 'D7261', 32, 'SBI', '1', 110840, TO_TIMESTAMP('2014-01-14 16:25:21:0','YYYY-MM-DD HH24:MI:SS:FF'), NULL, NULL);
INSERT INTO attachment_requirement(ATTACHMENT_REQUIREMENT_ID, PLAN_TYPE, INS_COMPANY_ID, INSURANCE_PLAN, PROVIDER_TAXONOMY, PROCEDURE_CODE, REQUIREMENT_ID, VENDOR_TYPE, IS_ACTIVE, CREATE_EMPLOYEE_NUMBER, CREATE_DATETIME, UPDATE_EMPLOYEE_NUMBER, UPDATE_DATETIME)
  VALUES(1969, NULL, NULL, NULL, NULL, 'D7261', 26, 'SBI', '1', 110840, TO_TIMESTAMP('2014-01-14 16:25:21:0','YYYY-MM-DD HH24:MI:SS:FF'), NULL, NULL);
INSERT INTO attachment_requirement(ATTACHMENT_REQUIREMENT_ID, PLAN_TYPE, INS_COMPANY_ID, INSURANCE_PLAN, PROVIDER_TAXONOMY, PROCEDURE_CODE, REQUIREMENT_ID, VENDOR_TYPE, IS_ACTIVE, CREATE_EMPLOYEE_NUMBER, CREATE_DATETIME, UPDATE_EMPLOYEE_NUMBER, UPDATE_DATETIME)
  VALUES(1970, NULL, NULL, NULL, NULL, 'D7270', 26, 'SBI', '1', 110840, TO_TIMESTAMP('2014-01-14 16:25:21:0','YYYY-MM-DD HH24:MI:SS:FF'), NULL, NULL);
INSERT INTO attachment_requirement(ATTACHMENT_REQUIREMENT_ID, PLAN_TYPE, INS_COMPANY_ID, INSURANCE_PLAN, PROVIDER_TAXONOMY, PROCEDURE_CODE, REQUIREMENT_ID, VENDOR_TYPE, IS_ACTIVE, CREATE_EMPLOYEE_NUMBER, CREATE_DATETIME, UPDATE_EMPLOYEE_NUMBER, UPDATE_DATETIME)
  VALUES(1971, NULL, NULL, NULL, NULL, 'D7270', 60, 'SBI', '1', 110840, TO_TIMESTAMP('2014-01-14 16:25:21:0','YYYY-MM-DD HH24:MI:SS:FF'), NULL, NULL);
INSERT INTO attachment_requirement(ATTACHMENT_REQUIREMENT_ID, PLAN_TYPE, INS_COMPANY_ID, INSURANCE_PLAN, PROVIDER_TAXONOMY, PROCEDURE_CODE, REQUIREMENT_ID, VENDOR_TYPE, IS_ACTIVE, CREATE_EMPLOYEE_NUMBER, CREATE_DATETIME, UPDATE_EMPLOYEE_NUMBER, UPDATE_DATETIME)
  VALUES(1972, NULL, NULL, NULL, NULL, 'D7272', 60, 'SBI', '1', 110840, TO_TIMESTAMP('2014-01-14 16:25:21:0','YYYY-MM-DD HH24:MI:SS:FF'), NULL, NULL);
INSERT INTO attachment_requirement(ATTACHMENT_REQUIREMENT_ID, PLAN_TYPE, INS_COMPANY_ID, INSURANCE_PLAN, PROVIDER_TAXONOMY, PROCEDURE_CODE, REQUIREMENT_ID, VENDOR_TYPE, IS_ACTIVE, CREATE_EMPLOYEE_NUMBER, CREATE_DATETIME, UPDATE_EMPLOYEE_NUMBER, UPDATE_DATETIME)
  VALUES(1973, NULL, NULL, NULL, NULL, 'D7280', 32, 'SBI', '1', 110840, TO_TIMESTAMP('2014-01-14 16:25:21:0','YYYY-MM-DD HH24:MI:SS:FF'), NULL, NULL);
INSERT INTO attachment_requirement(ATTACHMENT_REQUIREMENT_ID, PLAN_TYPE, INS_COMPANY_ID, INSURANCE_PLAN, PROVIDER_TAXONOMY, PROCEDURE_CODE, REQUIREMENT_ID, VENDOR_TYPE, IS_ACTIVE, CREATE_EMPLOYEE_NUMBER, CREATE_DATETIME, UPDATE_EMPLOYEE_NUMBER, UPDATE_DATETIME)
  VALUES(1974, NULL, NULL, NULL, NULL, 'D7283', 26, 'SBI', '1', 110840, TO_TIMESTAMP('2014-01-14 16:25:21:0','YYYY-MM-DD HH24:MI:SS:FF'), NULL, NULL);
INSERT INTO attachment_requirement(ATTACHMENT_REQUIREMENT_ID, PLAN_TYPE, INS_COMPANY_ID, INSURANCE_PLAN, PROVIDER_TAXONOMY, PROCEDURE_CODE, REQUIREMENT_ID, VENDOR_TYPE, IS_ACTIVE, CREATE_EMPLOYEE_NUMBER, CREATE_DATETIME, UPDATE_EMPLOYEE_NUMBER, UPDATE_DATETIME)
  VALUES(1975, NULL, NULL, NULL, NULL, 'D7285', 32, 'SBI', '1', 110840, TO_TIMESTAMP('2014-01-14 16:25:21:0','YYYY-MM-DD HH24:MI:SS:FF'), NULL, NULL);
INSERT INTO attachment_requirement(ATTACHMENT_REQUIREMENT_ID, PLAN_TYPE, INS_COMPANY_ID, INSURANCE_PLAN, PROVIDER_TAXONOMY, PROCEDURE_CODE, REQUIREMENT_ID, VENDOR_TYPE, IS_ACTIVE, CREATE_EMPLOYEE_NUMBER, CREATE_DATETIME, UPDATE_EMPLOYEE_NUMBER, UPDATE_DATETIME)
  VALUES(1976, NULL, NULL, NULL, NULL, 'D7285', 26, 'SBI', '1', 110840, TO_TIMESTAMP('2014-01-14 16:25:21:0','YYYY-MM-DD HH24:MI:SS:FF'), NULL, NULL);
INSERT INTO attachment_requirement(ATTACHMENT_REQUIREMENT_ID, PLAN_TYPE, INS_COMPANY_ID, INSURANCE_PLAN, PROVIDER_TAXONOMY, PROCEDURE_CODE, REQUIREMENT_ID, VENDOR_TYPE, IS_ACTIVE, CREATE_EMPLOYEE_NUMBER, CREATE_DATETIME, UPDATE_EMPLOYEE_NUMBER, UPDATE_DATETIME)
  VALUES(1977, NULL, NULL, NULL, NULL, 'D7285', 43, 'SBI', '1', 110840, TO_TIMESTAMP('2014-01-14 16:25:21:0','YYYY-MM-DD HH24:MI:SS:FF'), NULL, NULL);
INSERT INTO attachment_requirement(ATTACHMENT_REQUIREMENT_ID, PLAN_TYPE, INS_COMPANY_ID, INSURANCE_PLAN, PROVIDER_TAXONOMY, PROCEDURE_CODE, REQUIREMENT_ID, VENDOR_TYPE, IS_ACTIVE, CREATE_EMPLOYEE_NUMBER, CREATE_DATETIME, UPDATE_EMPLOYEE_NUMBER, UPDATE_DATETIME)
  VALUES(1978, NULL, NULL, NULL, NULL, 'D7286', 43, 'SBI', '1', 110840, TO_TIMESTAMP('2014-01-14 16:25:21:0','YYYY-MM-DD HH24:MI:SS:FF'), NULL, NULL);
INSERT INTO attachment_requirement(ATTACHMENT_REQUIREMENT_ID, PLAN_TYPE, INS_COMPANY_ID, INSURANCE_PLAN, PROVIDER_TAXONOMY, PROCEDURE_CODE, REQUIREMENT_ID, VENDOR_TYPE, IS_ACTIVE, CREATE_EMPLOYEE_NUMBER, CREATE_DATETIME, UPDATE_EMPLOYEE_NUMBER, UPDATE_DATETIME)
  VALUES(1979, NULL, NULL, NULL, NULL, 'D7286', 32, 'SBI', '1', 110840, TO_TIMESTAMP('2014-01-14 16:25:21:0','YYYY-MM-DD HH24:MI:SS:FF'), NULL, NULL);
INSERT INTO attachment_requirement(ATTACHMENT_REQUIREMENT_ID, PLAN_TYPE, INS_COMPANY_ID, INSURANCE_PLAN, PROVIDER_TAXONOMY, PROCEDURE_CODE, REQUIREMENT_ID, VENDOR_TYPE, IS_ACTIVE, CREATE_EMPLOYEE_NUMBER, CREATE_DATETIME, UPDATE_EMPLOYEE_NUMBER, UPDATE_DATETIME)
  VALUES(1980, NULL, NULL, NULL, NULL, 'D7291', 32, 'SBI', '1', 110840, TO_TIMESTAMP('2014-01-14 16:25:21:0','YYYY-MM-DD HH24:MI:SS:FF'), NULL, NULL);
INSERT INTO attachment_requirement(ATTACHMENT_REQUIREMENT_ID, PLAN_TYPE, INS_COMPANY_ID, INSURANCE_PLAN, PROVIDER_TAXONOMY, PROCEDURE_CODE, REQUIREMENT_ID, VENDOR_TYPE, IS_ACTIVE, CREATE_EMPLOYEE_NUMBER, CREATE_DATETIME, UPDATE_EMPLOYEE_NUMBER, UPDATE_DATETIME)
  VALUES(1981, NULL, NULL, NULL, NULL, 'D7291', 26, 'SBI', '1', 110840, TO_TIMESTAMP('2014-01-14 16:25:21:0','YYYY-MM-DD HH24:MI:SS:FF'), NULL, NULL);
INSERT INTO attachment_requirement(ATTACHMENT_REQUIREMENT_ID, PLAN_TYPE, INS_COMPANY_ID, INSURANCE_PLAN, PROVIDER_TAXONOMY, PROCEDURE_CODE, REQUIREMENT_ID, VENDOR_TYPE, IS_ACTIVE, CREATE_EMPLOYEE_NUMBER, CREATE_DATETIME, UPDATE_EMPLOYEE_NUMBER, UPDATE_DATETIME)
  VALUES(1982, NULL, NULL, NULL, NULL, 'D7310', 32, 'SBI', '1', 110840, TO_TIMESTAMP('2014-01-14 16:25:21:0','YYYY-MM-DD HH24:MI:SS:FF'), NULL, NULL);
INSERT INTO attachment_requirement(ATTACHMENT_REQUIREMENT_ID, PLAN_TYPE, INS_COMPANY_ID, INSURANCE_PLAN, PROVIDER_TAXONOMY, PROCEDURE_CODE, REQUIREMENT_ID, VENDOR_TYPE, IS_ACTIVE, CREATE_EMPLOYEE_NUMBER, CREATE_DATETIME, UPDATE_EMPLOYEE_NUMBER, UPDATE_DATETIME)
  VALUES(1983, NULL, NULL, NULL, NULL, 'D7310', 26, 'SBI', '1', 110840, TO_TIMESTAMP('2014-01-14 16:25:21:0','YYYY-MM-DD HH24:MI:SS:FF'), NULL, NULL);
INSERT INTO attachment_requirement(ATTACHMENT_REQUIREMENT_ID, PLAN_TYPE, INS_COMPANY_ID, INSURANCE_PLAN, PROVIDER_TAXONOMY, PROCEDURE_CODE, REQUIREMENT_ID, VENDOR_TYPE, IS_ACTIVE, CREATE_EMPLOYEE_NUMBER, CREATE_DATETIME, UPDATE_EMPLOYEE_NUMBER, UPDATE_DATETIME)
  VALUES(1984, NULL, NULL, NULL, NULL, 'D7320', 32, 'SBI', '1', 110840, TO_TIMESTAMP('2014-01-14 16:25:21:0','YYYY-MM-DD HH24:MI:SS:FF'), NULL, NULL);
INSERT INTO attachment_requirement(ATTACHMENT_REQUIREMENT_ID, PLAN_TYPE, INS_COMPANY_ID, INSURANCE_PLAN, PROVIDER_TAXONOMY, PROCEDURE_CODE, REQUIREMENT_ID, VENDOR_TYPE, IS_ACTIVE, CREATE_EMPLOYEE_NUMBER, CREATE_DATETIME, UPDATE_EMPLOYEE_NUMBER, UPDATE_DATETIME)
  VALUES(1986, NULL, NULL, NULL, NULL, 'D7320', 26, 'SBI', '1', 110840, TO_TIMESTAMP('2014-01-14 16:25:21:0','YYYY-MM-DD HH24:MI:SS:FF'), NULL, NULL);
INSERT INTO attachment_requirement(ATTACHMENT_REQUIREMENT_ID, PLAN_TYPE, INS_COMPANY_ID, INSURANCE_PLAN, PROVIDER_TAXONOMY, PROCEDURE_CODE, REQUIREMENT_ID, VENDOR_TYPE, IS_ACTIVE, CREATE_EMPLOYEE_NUMBER, CREATE_DATETIME, UPDATE_EMPLOYEE_NUMBER, UPDATE_DATETIME)
  VALUES(1987, NULL, NULL, NULL, NULL, 'D7321', 32, 'SBI', '1', 110840, TO_TIMESTAMP('2014-01-14 16:25:21:0','YYYY-MM-DD HH24:MI:SS:FF'), NULL, NULL);
INSERT INTO attachment_requirement(ATTACHMENT_REQUIREMENT_ID, PLAN_TYPE, INS_COMPANY_ID, INSURANCE_PLAN, PROVIDER_TAXONOMY, PROCEDURE_CODE, REQUIREMENT_ID, VENDOR_TYPE, IS_ACTIVE, CREATE_EMPLOYEE_NUMBER, CREATE_DATETIME, UPDATE_EMPLOYEE_NUMBER, UPDATE_DATETIME)
  VALUES(1988, NULL, NULL, NULL, NULL, 'D7340', 26, 'SBI', '1', 110840, TO_TIMESTAMP('2014-01-14 16:25:21:0','YYYY-MM-DD HH24:MI:SS:FF'), NULL, NULL);
INSERT INTO attachment_requirement(ATTACHMENT_REQUIREMENT_ID, PLAN_TYPE, INS_COMPANY_ID, INSURANCE_PLAN, PROVIDER_TAXONOMY, PROCEDURE_CODE, REQUIREMENT_ID, VENDOR_TYPE, IS_ACTIVE, CREATE_EMPLOYEE_NUMBER, CREATE_DATETIME, UPDATE_EMPLOYEE_NUMBER, UPDATE_DATETIME)
  VALUES(1989, NULL, NULL, NULL, NULL, 'D7350', 26, 'SBI', '1', 110840, TO_TIMESTAMP('2014-01-14 16:25:21:0','YYYY-MM-DD HH24:MI:SS:FF'), NULL, NULL);
INSERT INTO attachment_requirement(ATTACHMENT_REQUIREMENT_ID, PLAN_TYPE, INS_COMPANY_ID, INSURANCE_PLAN, PROVIDER_TAXONOMY, PROCEDURE_CODE, REQUIREMENT_ID, VENDOR_TYPE, IS_ACTIVE, CREATE_EMPLOYEE_NUMBER, CREATE_DATETIME, UPDATE_EMPLOYEE_NUMBER, UPDATE_DATETIME)
  VALUES(1990, NULL, NULL, NULL, NULL, 'D7410', 32, 'SBI', '1', 110840, TO_TIMESTAMP('2014-01-14 16:25:21:0','YYYY-MM-DD HH24:MI:SS:FF'), NULL, NULL);
INSERT INTO attachment_requirement(ATTACHMENT_REQUIREMENT_ID, PLAN_TYPE, INS_COMPANY_ID, INSURANCE_PLAN, PROVIDER_TAXONOMY, PROCEDURE_CODE, REQUIREMENT_ID, VENDOR_TYPE, IS_ACTIVE, CREATE_EMPLOYEE_NUMBER, CREATE_DATETIME, UPDATE_EMPLOYEE_NUMBER, UPDATE_DATETIME)
  VALUES(1992, NULL, NULL, NULL, NULL, 'D7410', 26, 'SBI', '1', 110840, TO_TIMESTAMP('2014-01-14 16:25:21:0','YYYY-MM-DD HH24:MI:SS:FF'), NULL, NULL);
INSERT INTO attachment_requirement(ATTACHMENT_REQUIREMENT_ID, PLAN_TYPE, INS_COMPANY_ID, INSURANCE_PLAN, PROVIDER_TAXONOMY, PROCEDURE_CODE, REQUIREMENT_ID, VENDOR_TYPE, IS_ACTIVE, CREATE_EMPLOYEE_NUMBER, CREATE_DATETIME, UPDATE_EMPLOYEE_NUMBER, UPDATE_DATETIME)
  VALUES(1993, NULL, NULL, NULL, NULL, 'D7411', 32, 'SBI', '1', 110840, TO_TIMESTAMP('2014-01-14 16:25:21:0','YYYY-MM-DD HH24:MI:SS:FF'), NULL, NULL);
INSERT INTO attachment_requirement(ATTACHMENT_REQUIREMENT_ID, PLAN_TYPE, INS_COMPANY_ID, INSURANCE_PLAN, PROVIDER_TAXONOMY, PROCEDURE_CODE, REQUIREMENT_ID, VENDOR_TYPE, IS_ACTIVE, CREATE_EMPLOYEE_NUMBER, CREATE_DATETIME, UPDATE_EMPLOYEE_NUMBER, UPDATE_DATETIME)
  VALUES(1994, NULL, NULL, NULL, NULL, 'D7411', 26, 'SBI', '1', 110840, TO_TIMESTAMP('2014-01-14 16:25:21:0','YYYY-MM-DD HH24:MI:SS:FF'), NULL, NULL);
INSERT INTO attachment_requirement(ATTACHMENT_REQUIREMENT_ID, PLAN_TYPE, INS_COMPANY_ID, INSURANCE_PLAN, PROVIDER_TAXONOMY, PROCEDURE_CODE, REQUIREMENT_ID, VENDOR_TYPE, IS_ACTIVE, CREATE_EMPLOYEE_NUMBER, CREATE_DATETIME, UPDATE_EMPLOYEE_NUMBER, UPDATE_DATETIME)
  VALUES(1995, NULL, NULL, NULL, NULL, 'D7412', 32, 'SBI', '1', 110840, TO_TIMESTAMP('2014-01-14 16:25:21:0','YYYY-MM-DD HH24:MI:SS:FF'), NULL, NULL);
INSERT INTO attachment_requirement(ATTACHMENT_REQUIREMENT_ID, PLAN_TYPE, INS_COMPANY_ID, INSURANCE_PLAN, PROVIDER_TAXONOMY, PROCEDURE_CODE, REQUIREMENT_ID, VENDOR_TYPE, IS_ACTIVE, CREATE_EMPLOYEE_NUMBER, CREATE_DATETIME, UPDATE_EMPLOYEE_NUMBER, UPDATE_DATETIME)
  VALUES(1996, NULL, NULL, NULL, NULL, 'D7412', 26, 'SBI', '1', 110840, TO_TIMESTAMP('2014-01-14 16:25:21:0','YYYY-MM-DD HH24:MI:SS:FF'), NULL, NULL);
INSERT INTO attachment_requirement(ATTACHMENT_REQUIREMENT_ID, PLAN_TYPE, INS_COMPANY_ID, INSURANCE_PLAN, PROVIDER_TAXONOMY, PROCEDURE_CODE, REQUIREMENT_ID, VENDOR_TYPE, IS_ACTIVE, CREATE_EMPLOYEE_NUMBER, CREATE_DATETIME, UPDATE_EMPLOYEE_NUMBER, UPDATE_DATETIME)
  VALUES(1997, NULL, NULL, NULL, NULL, 'D7413', 32, 'SBI', '1', 110840, TO_TIMESTAMP('2014-01-14 16:25:21:0','YYYY-MM-DD HH24:MI:SS:FF'), NULL, NULL);
INSERT INTO attachment_requirement(ATTACHMENT_REQUIREMENT_ID, PLAN_TYPE, INS_COMPANY_ID, INSURANCE_PLAN, PROVIDER_TAXONOMY, PROCEDURE_CODE, REQUIREMENT_ID, VENDOR_TYPE, IS_ACTIVE, CREATE_EMPLOYEE_NUMBER, CREATE_DATETIME, UPDATE_EMPLOYEE_NUMBER, UPDATE_DATETIME)
  VALUES(1998, NULL, NULL, NULL, NULL, 'D7413', 26, 'SBI', '1', 110840, TO_TIMESTAMP('2014-01-14 16:25:21:0','YYYY-MM-DD HH24:MI:SS:FF'), NULL, NULL);
INSERT INTO attachment_requirement(ATTACHMENT_REQUIREMENT_ID, PLAN_TYPE, INS_COMPANY_ID, INSURANCE_PLAN, PROVIDER_TAXONOMY, PROCEDURE_CODE, REQUIREMENT_ID, VENDOR_TYPE, IS_ACTIVE, CREATE_EMPLOYEE_NUMBER, CREATE_DATETIME, UPDATE_EMPLOYEE_NUMBER, UPDATE_DATETIME)
  VALUES(1999, NULL, NULL, NULL, NULL, 'D7414', 32, 'SBI', '1', 110840, TO_TIMESTAMP('2014-01-14 16:25:21:0','YYYY-MM-DD HH24:MI:SS:FF'), NULL, NULL);
INSERT INTO attachment_requirement(ATTACHMENT_REQUIREMENT_ID, PLAN_TYPE, INS_COMPANY_ID, INSURANCE_PLAN, PROVIDER_TAXONOMY, PROCEDURE_CODE, REQUIREMENT_ID, VENDOR_TYPE, IS_ACTIVE, CREATE_EMPLOYEE_NUMBER, CREATE_DATETIME, UPDATE_EMPLOYEE_NUMBER, UPDATE_DATETIME)
  VALUES(2000, NULL, NULL, NULL, NULL, 'D7414', 26, 'SBI', '1', 110840, TO_TIMESTAMP('2014-01-14 16:25:21:0','YYYY-MM-DD HH24:MI:SS:FF'), NULL, NULL);
INSERT INTO attachment_requirement(ATTACHMENT_REQUIREMENT_ID, PLAN_TYPE, INS_COMPANY_ID, INSURANCE_PLAN, PROVIDER_TAXONOMY, PROCEDURE_CODE, REQUIREMENT_ID, VENDOR_TYPE, IS_ACTIVE, CREATE_EMPLOYEE_NUMBER, CREATE_DATETIME, UPDATE_EMPLOYEE_NUMBER, UPDATE_DATETIME)
  VALUES(2001, NULL, NULL, NULL, NULL, 'D7415', 32, 'SBI', '1', 110840, TO_TIMESTAMP('2014-01-14 16:25:21:0','YYYY-MM-DD HH24:MI:SS:FF'), NULL, NULL);
INSERT INTO attachment_requirement(ATTACHMENT_REQUIREMENT_ID, PLAN_TYPE, INS_COMPANY_ID, INSURANCE_PLAN, PROVIDER_TAXONOMY, PROCEDURE_CODE, REQUIREMENT_ID, VENDOR_TYPE, IS_ACTIVE, CREATE_EMPLOYEE_NUMBER, CREATE_DATETIME, UPDATE_EMPLOYEE_NUMBER, UPDATE_DATETIME)
  VALUES(2002, NULL, NULL, NULL, NULL, 'D7415', 26, 'SBI', '1', 110840, TO_TIMESTAMP('2014-01-14 16:25:21:0','YYYY-MM-DD HH24:MI:SS:FF'), NULL, NULL);
INSERT INTO attachment_requirement(ATTACHMENT_REQUIREMENT_ID, PLAN_TYPE, INS_COMPANY_ID, INSURANCE_PLAN, PROVIDER_TAXONOMY, PROCEDURE_CODE, REQUIREMENT_ID, VENDOR_TYPE, IS_ACTIVE, CREATE_EMPLOYEE_NUMBER, CREATE_DATETIME, UPDATE_EMPLOYEE_NUMBER, UPDATE_DATETIME)
  VALUES(2003, NULL, NULL, NULL, NULL, 'D7440', 32, 'SBI', '1', 110840, TO_TIMESTAMP('2014-01-14 16:25:21:0','YYYY-MM-DD HH24:MI:SS:FF'), NULL, NULL);
INSERT INTO attachment_requirement(ATTACHMENT_REQUIREMENT_ID, PLAN_TYPE, INS_COMPANY_ID, INSURANCE_PLAN, PROVIDER_TAXONOMY, PROCEDURE_CODE, REQUIREMENT_ID, VENDOR_TYPE, IS_ACTIVE, CREATE_EMPLOYEE_NUMBER, CREATE_DATETIME, UPDATE_EMPLOYEE_NUMBER, UPDATE_DATETIME)
  VALUES(2004, NULL, NULL, NULL, NULL, 'D7440', 26, 'SBI', '1', 110840, TO_TIMESTAMP('2014-01-14 16:25:21:0','YYYY-MM-DD HH24:MI:SS:FF'), NULL, NULL);
INSERT INTO attachment_requirement(ATTACHMENT_REQUIREMENT_ID, PLAN_TYPE, INS_COMPANY_ID, INSURANCE_PLAN, PROVIDER_TAXONOMY, PROCEDURE_CODE, REQUIREMENT_ID, VENDOR_TYPE, IS_ACTIVE, CREATE_EMPLOYEE_NUMBER, CREATE_DATETIME, UPDATE_EMPLOYEE_NUMBER, UPDATE_DATETIME)
  VALUES(2005, NULL, NULL, NULL, NULL, 'D7441', 32, 'SBI', '1', 110840, TO_TIMESTAMP('2014-01-14 16:25:21:0','YYYY-MM-DD HH24:MI:SS:FF'), NULL, NULL);
INSERT INTO attachment_requirement(ATTACHMENT_REQUIREMENT_ID, PLAN_TYPE, INS_COMPANY_ID, INSURANCE_PLAN, PROVIDER_TAXONOMY, PROCEDURE_CODE, REQUIREMENT_ID, VENDOR_TYPE, IS_ACTIVE, CREATE_EMPLOYEE_NUMBER, CREATE_DATETIME, UPDATE_EMPLOYEE_NUMBER, UPDATE_DATETIME)
  VALUES(2006, NULL, NULL, NULL, NULL, 'D7441', 26, 'SBI', '1', 110840, TO_TIMESTAMP('2014-01-14 16:25:21:0','YYYY-MM-DD HH24:MI:SS:FF'), NULL, NULL);
INSERT INTO attachment_requirement(ATTACHMENT_REQUIREMENT_ID, PLAN_TYPE, INS_COMPANY_ID, INSURANCE_PLAN, PROVIDER_TAXONOMY, PROCEDURE_CODE, REQUIREMENT_ID, VENDOR_TYPE, IS_ACTIVE, CREATE_EMPLOYEE_NUMBER, CREATE_DATETIME, UPDATE_EMPLOYEE_NUMBER, UPDATE_DATETIME)
  VALUES(2007, NULL, NULL, NULL, NULL, 'D7460', 32, 'SBI', '1', 110840, TO_TIMESTAMP('2014-01-14 16:25:21:0','YYYY-MM-DD HH24:MI:SS:FF'), NULL, NULL);
INSERT INTO attachment_requirement(ATTACHMENT_REQUIREMENT_ID, PLAN_TYPE, INS_COMPANY_ID, INSURANCE_PLAN, PROVIDER_TAXONOMY, PROCEDURE_CODE, REQUIREMENT_ID, VENDOR_TYPE, IS_ACTIVE, CREATE_EMPLOYEE_NUMBER, CREATE_DATETIME, UPDATE_EMPLOYEE_NUMBER, UPDATE_DATETIME)
  VALUES(2008, NULL, NULL, NULL, NULL, 'D7460', 26, 'SBI', '1', 110840, TO_TIMESTAMP('2014-01-14 16:25:21:0','YYYY-MM-DD HH24:MI:SS:FF'), NULL, NULL);
INSERT INTO attachment_requirement(ATTACHMENT_REQUIREMENT_ID, PLAN_TYPE, INS_COMPANY_ID, INSURANCE_PLAN, PROVIDER_TAXONOMY, PROCEDURE_CODE, REQUIREMENT_ID, VENDOR_TYPE, IS_ACTIVE, CREATE_EMPLOYEE_NUMBER, CREATE_DATETIME, UPDATE_EMPLOYEE_NUMBER, UPDATE_DATETIME)
  VALUES(2009, NULL, NULL, NULL, NULL, 'D7471', 32, 'SBI', '1', 110840, TO_TIMESTAMP('2014-01-14 16:25:21:0','YYYY-MM-DD HH24:MI:SS:FF'), NULL, NULL);
INSERT INTO attachment_requirement(ATTACHMENT_REQUIREMENT_ID, PLAN_TYPE, INS_COMPANY_ID, INSURANCE_PLAN, PROVIDER_TAXONOMY, PROCEDURE_CODE, REQUIREMENT_ID, VENDOR_TYPE, IS_ACTIVE, CREATE_EMPLOYEE_NUMBER, CREATE_DATETIME, UPDATE_EMPLOYEE_NUMBER, UPDATE_DATETIME)
  VALUES(2010, NULL, NULL, NULL, NULL, 'D7471', 26, 'SBI', '1', 110840, TO_TIMESTAMP('2014-01-14 16:25:21:0','YYYY-MM-DD HH24:MI:SS:FF'), NULL, NULL);
INSERT INTO attachment_requirement(ATTACHMENT_REQUIREMENT_ID, PLAN_TYPE, INS_COMPANY_ID, INSURANCE_PLAN, PROVIDER_TAXONOMY, PROCEDURE_CODE, REQUIREMENT_ID, VENDOR_TYPE, IS_ACTIVE, CREATE_EMPLOYEE_NUMBER, CREATE_DATETIME, UPDATE_EMPLOYEE_NUMBER, UPDATE_DATETIME)
  VALUES(2011, NULL, NULL, NULL, NULL, 'D7472', 32, 'SBI', '1', 110840, TO_TIMESTAMP('2014-01-14 16:25:21:0','YYYY-MM-DD HH24:MI:SS:FF'), NULL, NULL);
INSERT INTO attachment_requirement(ATTACHMENT_REQUIREMENT_ID, PLAN_TYPE, INS_COMPANY_ID, INSURANCE_PLAN, PROVIDER_TAXONOMY, PROCEDURE_CODE, REQUIREMENT_ID, VENDOR_TYPE, IS_ACTIVE, CREATE_EMPLOYEE_NUMBER, CREATE_DATETIME, UPDATE_EMPLOYEE_NUMBER, UPDATE_DATETIME)
  VALUES(2012, NULL, NULL, NULL, NULL, 'D7472', 26, 'SBI', '1', 110840, TO_TIMESTAMP('2014-01-14 16:25:21:0','YYYY-MM-DD HH24:MI:SS:FF'), NULL, NULL);
INSERT INTO attachment_requirement(ATTACHMENT_REQUIREMENT_ID, PLAN_TYPE, INS_COMPANY_ID, INSURANCE_PLAN, PROVIDER_TAXONOMY, PROCEDURE_CODE, REQUIREMENT_ID, VENDOR_TYPE, IS_ACTIVE, CREATE_EMPLOYEE_NUMBER, CREATE_DATETIME, UPDATE_EMPLOYEE_NUMBER, UPDATE_DATETIME)
  VALUES(2013, NULL, NULL, NULL, NULL, 'D7473', 32, 'SBI', '1', 110840, TO_TIMESTAMP('2014-01-14 16:25:21:0','YYYY-MM-DD HH24:MI:SS:FF'), NULL, NULL);
INSERT INTO attachment_requirement(ATTACHMENT_REQUIREMENT_ID, PLAN_TYPE, INS_COMPANY_ID, INSURANCE_PLAN, PROVIDER_TAXONOMY, PROCEDURE_CODE, REQUIREMENT_ID, VENDOR_TYPE, IS_ACTIVE, CREATE_EMPLOYEE_NUMBER, CREATE_DATETIME, UPDATE_EMPLOYEE_NUMBER, UPDATE_DATETIME)
  VALUES(2014, NULL, NULL, NULL, NULL, 'D7473', 26, 'SBI', '1', 110840, TO_TIMESTAMP('2014-01-14 16:25:21:0','YYYY-MM-DD HH24:MI:SS:FF'), NULL, NULL);
INSERT INTO attachment_requirement(ATTACHMENT_REQUIREMENT_ID, PLAN_TYPE, INS_COMPANY_ID, INSURANCE_PLAN, PROVIDER_TAXONOMY, PROCEDURE_CODE, REQUIREMENT_ID, VENDOR_TYPE, IS_ACTIVE, CREATE_EMPLOYEE_NUMBER, CREATE_DATETIME, UPDATE_EMPLOYEE_NUMBER, UPDATE_DATETIME)
  VALUES(2015, NULL, NULL, NULL, NULL, 'D7485', 32, 'SBI', '1', 110840, TO_TIMESTAMP('2014-01-14 16:25:21:0','YYYY-MM-DD HH24:MI:SS:FF'), NULL, NULL);
INSERT INTO attachment_requirement(ATTACHMENT_REQUIREMENT_ID, PLAN_TYPE, INS_COMPANY_ID, INSURANCE_PLAN, PROVIDER_TAXONOMY, PROCEDURE_CODE, REQUIREMENT_ID, VENDOR_TYPE, IS_ACTIVE, CREATE_EMPLOYEE_NUMBER, CREATE_DATETIME, UPDATE_EMPLOYEE_NUMBER, UPDATE_DATETIME)
  VALUES(2016, NULL, NULL, NULL, NULL, 'D7510', 26, 'SBI', '1', 110840, TO_TIMESTAMP('2014-01-14 16:25:21:0','YYYY-MM-DD HH24:MI:SS:FF'), NULL, NULL);
INSERT INTO attachment_requirement(ATTACHMENT_REQUIREMENT_ID, PLAN_TYPE, INS_COMPANY_ID, INSURANCE_PLAN, PROVIDER_TAXONOMY, PROCEDURE_CODE, REQUIREMENT_ID, VENDOR_TYPE, IS_ACTIVE, CREATE_EMPLOYEE_NUMBER, CREATE_DATETIME, UPDATE_EMPLOYEE_NUMBER, UPDATE_DATETIME)
  VALUES(2017, NULL, NULL, NULL, NULL, 'D7520', 26, 'SBI', '1', 110840, TO_TIMESTAMP('2014-01-14 16:25:21:0','YYYY-MM-DD HH24:MI:SS:FF'), NULL, NULL);
INSERT INTO attachment_requirement(ATTACHMENT_REQUIREMENT_ID, PLAN_TYPE, INS_COMPANY_ID, INSURANCE_PLAN, PROVIDER_TAXONOMY, PROCEDURE_CODE, REQUIREMENT_ID, VENDOR_TYPE, IS_ACTIVE, CREATE_EMPLOYEE_NUMBER, CREATE_DATETIME, UPDATE_EMPLOYEE_NUMBER, UPDATE_DATETIME)
  VALUES(2018, NULL, NULL, NULL, NULL, 'D7530', 32, 'SBI', '1', 110840, TO_TIMESTAMP('2014-01-14 16:25:21:0','YYYY-MM-DD HH24:MI:SS:FF'), NULL, NULL);
INSERT INTO attachment_requirement(ATTACHMENT_REQUIREMENT_ID, PLAN_TYPE, INS_COMPANY_ID, INSURANCE_PLAN, PROVIDER_TAXONOMY, PROCEDURE_CODE, REQUIREMENT_ID, VENDOR_TYPE, IS_ACTIVE, CREATE_EMPLOYEE_NUMBER, CREATE_DATETIME, UPDATE_EMPLOYEE_NUMBER, UPDATE_DATETIME)
  VALUES(2019, NULL, NULL, NULL, NULL, 'D7530', 26, 'SBI', '1', 110840, TO_TIMESTAMP('2014-01-14 16:25:21:0','YYYY-MM-DD HH24:MI:SS:FF'), NULL, NULL);
INSERT INTO attachment_requirement(ATTACHMENT_REQUIREMENT_ID, PLAN_TYPE, INS_COMPANY_ID, INSURANCE_PLAN, PROVIDER_TAXONOMY, PROCEDURE_CODE, REQUIREMENT_ID, VENDOR_TYPE, IS_ACTIVE, CREATE_EMPLOYEE_NUMBER, CREATE_DATETIME, UPDATE_EMPLOYEE_NUMBER, UPDATE_DATETIME)
  VALUES(2020, NULL, NULL, NULL, NULL, 'D7540', 32, 'SBI', '1', 110840, TO_TIMESTAMP('2014-01-14 16:25:21:0','YYYY-MM-DD HH24:MI:SS:FF'), NULL, NULL);
INSERT INTO attachment_requirement(ATTACHMENT_REQUIREMENT_ID, PLAN_TYPE, INS_COMPANY_ID, INSURANCE_PLAN, PROVIDER_TAXONOMY, PROCEDURE_CODE, REQUIREMENT_ID, VENDOR_TYPE, IS_ACTIVE, CREATE_EMPLOYEE_NUMBER, CREATE_DATETIME, UPDATE_EMPLOYEE_NUMBER, UPDATE_DATETIME)
  VALUES(2021, NULL, NULL, NULL, NULL, 'D7540', 26, 'SBI', '1', 110840, TO_TIMESTAMP('2014-01-14 16:25:21:0','YYYY-MM-DD HH24:MI:SS:FF'), NULL, NULL);
INSERT INTO attachment_requirement(ATTACHMENT_REQUIREMENT_ID, PLAN_TYPE, INS_COMPANY_ID, INSURANCE_PLAN, PROVIDER_TAXONOMY, PROCEDURE_CODE, REQUIREMENT_ID, VENDOR_TYPE, IS_ACTIVE, CREATE_EMPLOYEE_NUMBER, CREATE_DATETIME, UPDATE_EMPLOYEE_NUMBER, UPDATE_DATETIME)
  VALUES(2022, NULL, NULL, NULL, NULL, 'D7560', 32, 'SBI', '1', 110840, TO_TIMESTAMP('2014-01-14 16:25:21:0','YYYY-MM-DD HH24:MI:SS:FF'), NULL, NULL);
INSERT INTO attachment_requirement(ATTACHMENT_REQUIREMENT_ID, PLAN_TYPE, INS_COMPANY_ID, INSURANCE_PLAN, PROVIDER_TAXONOMY, PROCEDURE_CODE, REQUIREMENT_ID, VENDOR_TYPE, IS_ACTIVE, CREATE_EMPLOYEE_NUMBER, CREATE_DATETIME, UPDATE_EMPLOYEE_NUMBER, UPDATE_DATETIME)
  VALUES(2023, NULL, NULL, NULL, NULL, 'D7560', 26, 'SBI', '1', 110840, TO_TIMESTAMP('2014-01-14 16:25:21:0','YYYY-MM-DD HH24:MI:SS:FF'), NULL, NULL);
INSERT INTO attachment_requirement(ATTACHMENT_REQUIREMENT_ID, PLAN_TYPE, INS_COMPANY_ID, INSURANCE_PLAN, PROVIDER_TAXONOMY, PROCEDURE_CODE, REQUIREMENT_ID, VENDOR_TYPE, IS_ACTIVE, CREATE_EMPLOYEE_NUMBER, CREATE_DATETIME, UPDATE_EMPLOYEE_NUMBER, UPDATE_DATETIME)
  VALUES(2024, NULL, NULL, NULL, NULL, 'D7880', 32, 'SBI', '1', 110840, TO_TIMESTAMP('2014-01-14 16:25:21:0','YYYY-MM-DD HH24:MI:SS:FF'), NULL, NULL);
INSERT INTO attachment_requirement(ATTACHMENT_REQUIREMENT_ID, PLAN_TYPE, INS_COMPANY_ID, INSURANCE_PLAN, PROVIDER_TAXONOMY, PROCEDURE_CODE, REQUIREMENT_ID, VENDOR_TYPE, IS_ACTIVE, CREATE_EMPLOYEE_NUMBER, CREATE_DATETIME, UPDATE_EMPLOYEE_NUMBER, UPDATE_DATETIME)
  VALUES(2025, NULL, NULL, NULL, NULL, 'D7940', 2, 'SBI', '1', 110840, TO_TIMESTAMP('2014-01-14 16:25:21:0','YYYY-MM-DD HH24:MI:SS:FF'), NULL, NULL);
INSERT INTO attachment_requirement(ATTACHMENT_REQUIREMENT_ID, PLAN_TYPE, INS_COMPANY_ID, INSURANCE_PLAN, PROVIDER_TAXONOMY, PROCEDURE_CODE, REQUIREMENT_ID, VENDOR_TYPE, IS_ACTIVE, CREATE_EMPLOYEE_NUMBER, CREATE_DATETIME, UPDATE_EMPLOYEE_NUMBER, UPDATE_DATETIME)
  VALUES(2026, NULL, NULL, NULL, NULL, 'D7941', 2, 'SBI', '1', 110840, TO_TIMESTAMP('2014-01-14 16:25:21:0','YYYY-MM-DD HH24:MI:SS:FF'), NULL, NULL);
INSERT INTO attachment_requirement(ATTACHMENT_REQUIREMENT_ID, PLAN_TYPE, INS_COMPANY_ID, INSURANCE_PLAN, PROVIDER_TAXONOMY, PROCEDURE_CODE, REQUIREMENT_ID, VENDOR_TYPE, IS_ACTIVE, CREATE_EMPLOYEE_NUMBER, CREATE_DATETIME, UPDATE_EMPLOYEE_NUMBER, UPDATE_DATETIME)
  VALUES(2027, NULL, NULL, NULL, NULL, 'D7943', 2, 'SBI', '1', 110840, TO_TIMESTAMP('2014-01-14 16:25:21:0','YYYY-MM-DD HH24:MI:SS:FF'), NULL, NULL);
INSERT INTO attachment_requirement(ATTACHMENT_REQUIREMENT_ID, PLAN_TYPE, INS_COMPANY_ID, INSURANCE_PLAN, PROVIDER_TAXONOMY, PROCEDURE_CODE, REQUIREMENT_ID, VENDOR_TYPE, IS_ACTIVE, CREATE_EMPLOYEE_NUMBER, CREATE_DATETIME, UPDATE_EMPLOYEE_NUMBER, UPDATE_DATETIME)
  VALUES(2028, NULL, NULL, NULL, NULL, 'D7944', 2, 'SBI', '1', 110840, TO_TIMESTAMP('2014-01-14 16:25:21:0','YYYY-MM-DD HH24:MI:SS:FF'), NULL, NULL);
INSERT INTO attachment_requirement(ATTACHMENT_REQUIREMENT_ID, PLAN_TYPE, INS_COMPANY_ID, INSURANCE_PLAN, PROVIDER_TAXONOMY, PROCEDURE_CODE, REQUIREMENT_ID, VENDOR_TYPE, IS_ACTIVE, CREATE_EMPLOYEE_NUMBER, CREATE_DATETIME, UPDATE_EMPLOYEE_NUMBER, UPDATE_DATETIME)
  VALUES(2029, NULL, NULL, NULL, NULL, 'D7945', 2, 'SBI', '1', 110840, TO_TIMESTAMP('2014-01-14 16:25:21:0','YYYY-MM-DD HH24:MI:SS:FF'), NULL, NULL);
INSERT INTO attachment_requirement(ATTACHMENT_REQUIREMENT_ID, PLAN_TYPE, INS_COMPANY_ID, INSURANCE_PLAN, PROVIDER_TAXONOMY, PROCEDURE_CODE, REQUIREMENT_ID, VENDOR_TYPE, IS_ACTIVE, CREATE_EMPLOYEE_NUMBER, CREATE_DATETIME, UPDATE_EMPLOYEE_NUMBER, UPDATE_DATETIME)
  VALUES(2030, NULL, NULL, NULL, NULL, 'D7946', 2, 'SBI', '1', 110840, TO_TIMESTAMP('2014-01-14 16:25:21:0','YYYY-MM-DD HH24:MI:SS:FF'), NULL, NULL);
INSERT INTO attachment_requirement(ATTACHMENT_REQUIREMENT_ID, PLAN_TYPE, INS_COMPANY_ID, INSURANCE_PLAN, PROVIDER_TAXONOMY, PROCEDURE_CODE, REQUIREMENT_ID, VENDOR_TYPE, IS_ACTIVE, CREATE_EMPLOYEE_NUMBER, CREATE_DATETIME, UPDATE_EMPLOYEE_NUMBER, UPDATE_DATETIME)
  VALUES(2031, NULL, NULL, NULL, NULL, 'D7947', 2, 'SBI', '1', 110840, TO_TIMESTAMP('2014-01-14 16:25:21:0','YYYY-MM-DD HH24:MI:SS:FF'), NULL, NULL);
INSERT INTO attachment_requirement(ATTACHMENT_REQUIREMENT_ID, PLAN_TYPE, INS_COMPANY_ID, INSURANCE_PLAN, PROVIDER_TAXONOMY, PROCEDURE_CODE, REQUIREMENT_ID, VENDOR_TYPE, IS_ACTIVE, CREATE_EMPLOYEE_NUMBER, CREATE_DATETIME, UPDATE_EMPLOYEE_NUMBER, UPDATE_DATETIME)
  VALUES(2032, NULL, NULL, NULL, NULL, 'D7948', 2, 'SBI', '1', 110840, TO_TIMESTAMP('2014-01-14 16:25:21:0','YYYY-MM-DD HH24:MI:SS:FF'), NULL, NULL);
INSERT INTO attachment_requirement(ATTACHMENT_REQUIREMENT_ID, PLAN_TYPE, INS_COMPANY_ID, INSURANCE_PLAN, PROVIDER_TAXONOMY, PROCEDURE_CODE, REQUIREMENT_ID, VENDOR_TYPE, IS_ACTIVE, CREATE_EMPLOYEE_NUMBER, CREATE_DATETIME, UPDATE_EMPLOYEE_NUMBER, UPDATE_DATETIME)
  VALUES(2033, NULL, NULL, NULL, NULL, 'D7949', 2, 'SBI', '1', 110840, TO_TIMESTAMP('2014-01-14 16:25:21:0','YYYY-MM-DD HH24:MI:SS:FF'), NULL, NULL);
INSERT INTO attachment_requirement(ATTACHMENT_REQUIREMENT_ID, PLAN_TYPE, INS_COMPANY_ID, INSURANCE_PLAN, PROVIDER_TAXONOMY, PROCEDURE_CODE, REQUIREMENT_ID, VENDOR_TYPE, IS_ACTIVE, CREATE_EMPLOYEE_NUMBER, CREATE_DATETIME, UPDATE_EMPLOYEE_NUMBER, UPDATE_DATETIME)
  VALUES(2034, NULL, NULL, NULL, NULL, 'D7950', 1, 'SBI', '1', 110840, TO_TIMESTAMP('2014-01-14 16:25:21:0','YYYY-MM-DD HH24:MI:SS:FF'), NULL, NULL);
INSERT INTO attachment_requirement(ATTACHMENT_REQUIREMENT_ID, PLAN_TYPE, INS_COMPANY_ID, INSURANCE_PLAN, PROVIDER_TAXONOMY, PROCEDURE_CODE, REQUIREMENT_ID, VENDOR_TYPE, IS_ACTIVE, CREATE_EMPLOYEE_NUMBER, CREATE_DATETIME, UPDATE_EMPLOYEE_NUMBER, UPDATE_DATETIME)
  VALUES(2035, NULL, NULL, NULL, NULL, 'D7950', 45, 'SBI', '1', 110840, TO_TIMESTAMP('2014-01-14 16:25:21:0','YYYY-MM-DD HH24:MI:SS:FF'), NULL, NULL);
INSERT INTO attachment_requirement(ATTACHMENT_REQUIREMENT_ID, PLAN_TYPE, INS_COMPANY_ID, INSURANCE_PLAN, PROVIDER_TAXONOMY, PROCEDURE_CODE, REQUIREMENT_ID, VENDOR_TYPE, IS_ACTIVE, CREATE_EMPLOYEE_NUMBER, CREATE_DATETIME, UPDATE_EMPLOYEE_NUMBER, UPDATE_DATETIME)
  VALUES(2036, NULL, NULL, NULL, NULL, 'D7950', 18, 'SBI', '1', 110840, TO_TIMESTAMP('2014-01-14 16:25:21:0','YYYY-MM-DD HH24:MI:SS:FF'), NULL, NULL);
INSERT INTO attachment_requirement(ATTACHMENT_REQUIREMENT_ID, PLAN_TYPE, INS_COMPANY_ID, INSURANCE_PLAN, PROVIDER_TAXONOMY, PROCEDURE_CODE, REQUIREMENT_ID, VENDOR_TYPE, IS_ACTIVE, CREATE_EMPLOYEE_NUMBER, CREATE_DATETIME, UPDATE_EMPLOYEE_NUMBER, UPDATE_DATETIME)
  VALUES(2037, NULL, NULL, NULL, NULL, 'D7951', 26, 'SBI', '1', 110840, TO_TIMESTAMP('2014-01-14 16:25:21:0','YYYY-MM-DD HH24:MI:SS:FF'), NULL, NULL);
INSERT INTO attachment_requirement(ATTACHMENT_REQUIREMENT_ID, PLAN_TYPE, INS_COMPANY_ID, INSURANCE_PLAN, PROVIDER_TAXONOMY, PROCEDURE_CODE, REQUIREMENT_ID, VENDOR_TYPE, IS_ACTIVE, CREATE_EMPLOYEE_NUMBER, CREATE_DATETIME, UPDATE_EMPLOYEE_NUMBER, UPDATE_DATETIME)
  VALUES(2039, NULL, NULL, NULL, NULL, 'D7953', 26, 'SBI', '1', 110840, TO_TIMESTAMP('2014-01-14 16:25:21:0','YYYY-MM-DD HH24:MI:SS:FF'), NULL, NULL);
INSERT INTO attachment_requirement(ATTACHMENT_REQUIREMENT_ID, PLAN_TYPE, INS_COMPANY_ID, INSURANCE_PLAN, PROVIDER_TAXONOMY, PROCEDURE_CODE, REQUIREMENT_ID, VENDOR_TYPE, IS_ACTIVE, CREATE_EMPLOYEE_NUMBER, CREATE_DATETIME, UPDATE_EMPLOYEE_NUMBER, UPDATE_DATETIME)
  VALUES(2040, NULL, NULL, NULL, NULL, 'D7953', 1, 'SBI', '1', 110840, TO_TIMESTAMP('2014-01-14 16:25:21:0','YYYY-MM-DD HH24:MI:SS:FF'), NULL, NULL);
INSERT INTO attachment_requirement(ATTACHMENT_REQUIREMENT_ID, PLAN_TYPE, INS_COMPANY_ID, INSURANCE_PLAN, PROVIDER_TAXONOMY, PROCEDURE_CODE, REQUIREMENT_ID, VENDOR_TYPE, IS_ACTIVE, CREATE_EMPLOYEE_NUMBER, CREATE_DATETIME, UPDATE_EMPLOYEE_NUMBER, UPDATE_DATETIME)
  VALUES(2042, NULL, NULL, NULL, NULL, 'D7960', 26, 'SBI', '1', 110840, TO_TIMESTAMP('2014-01-14 16:25:21:0','YYYY-MM-DD HH24:MI:SS:FF'), NULL, NULL);
INSERT INTO attachment_requirement(ATTACHMENT_REQUIREMENT_ID, PLAN_TYPE, INS_COMPANY_ID, INSURANCE_PLAN, PROVIDER_TAXONOMY, PROCEDURE_CODE, REQUIREMENT_ID, VENDOR_TYPE, IS_ACTIVE, CREATE_EMPLOYEE_NUMBER, CREATE_DATETIME, UPDATE_EMPLOYEE_NUMBER, UPDATE_DATETIME)
  VALUES(2043, NULL, NULL, NULL, NULL, 'D7970', 26, 'SBI', '1', 110840, TO_TIMESTAMP('2014-01-14 16:25:21:0','YYYY-MM-DD HH24:MI:SS:FF'), NULL, NULL);
INSERT INTO attachment_requirement(ATTACHMENT_REQUIREMENT_ID, PLAN_TYPE, INS_COMPANY_ID, INSURANCE_PLAN, PROVIDER_TAXONOMY, PROCEDURE_CODE, REQUIREMENT_ID, VENDOR_TYPE, IS_ACTIVE, CREATE_EMPLOYEE_NUMBER, CREATE_DATETIME, UPDATE_EMPLOYEE_NUMBER, UPDATE_DATETIME)
  VALUES(2044, NULL, NULL, NULL, NULL, 'D7970', 32, 'SBI', '1', 110840, TO_TIMESTAMP('2014-01-14 16:25:21:0','YYYY-MM-DD HH24:MI:SS:FF'), NULL, NULL);
INSERT INTO attachment_requirement(ATTACHMENT_REQUIREMENT_ID, PLAN_TYPE, INS_COMPANY_ID, INSURANCE_PLAN, PROVIDER_TAXONOMY, PROCEDURE_CODE, REQUIREMENT_ID, VENDOR_TYPE, IS_ACTIVE, CREATE_EMPLOYEE_NUMBER, CREATE_DATETIME, UPDATE_EMPLOYEE_NUMBER, UPDATE_DATETIME)
  VALUES(2045, NULL, NULL, NULL, NULL, 'D7971', 1, 'SBI', '1', 110840, TO_TIMESTAMP('2014-01-14 16:25:21:0','YYYY-MM-DD HH24:MI:SS:FF'), NULL, NULL);
INSERT INTO attachment_requirement(ATTACHMENT_REQUIREMENT_ID, PLAN_TYPE, INS_COMPANY_ID, INSURANCE_PLAN, PROVIDER_TAXONOMY, PROCEDURE_CODE, REQUIREMENT_ID, VENDOR_TYPE, IS_ACTIVE, CREATE_EMPLOYEE_NUMBER, CREATE_DATETIME, UPDATE_EMPLOYEE_NUMBER, UPDATE_DATETIME)
  VALUES(2046, NULL, NULL, NULL, NULL, 'D7971', 26, 'SBI', '1', 110840, TO_TIMESTAMP('2014-01-14 16:25:21:0','YYYY-MM-DD HH24:MI:SS:FF'), NULL, NULL);
INSERT INTO attachment_requirement(ATTACHMENT_REQUIREMENT_ID, PLAN_TYPE, INS_COMPANY_ID, INSURANCE_PLAN, PROVIDER_TAXONOMY, PROCEDURE_CODE, REQUIREMENT_ID, VENDOR_TYPE, IS_ACTIVE, CREATE_EMPLOYEE_NUMBER, CREATE_DATETIME, UPDATE_EMPLOYEE_NUMBER, UPDATE_DATETIME)
  VALUES(2047, NULL, NULL, NULL, NULL, 'D7972', 26, 'SBI', '1', 110840, TO_TIMESTAMP('2014-01-14 16:25:21:0','YYYY-MM-DD HH24:MI:SS:FF'), NULL, NULL);
INSERT INTO attachment_requirement(ATTACHMENT_REQUIREMENT_ID, PLAN_TYPE, INS_COMPANY_ID, INSURANCE_PLAN, PROVIDER_TAXONOMY, PROCEDURE_CODE, REQUIREMENT_ID, VENDOR_TYPE, IS_ACTIVE, CREATE_EMPLOYEE_NUMBER, CREATE_DATETIME, UPDATE_EMPLOYEE_NUMBER, UPDATE_DATETIME)
  VALUES(2048, NULL, NULL, NULL, NULL, 'D7980', 32, 'SBI', '1', 110840, TO_TIMESTAMP('2014-01-14 16:25:21:0','YYYY-MM-DD HH24:MI:SS:FF'), NULL, NULL);
INSERT INTO attachment_requirement(ATTACHMENT_REQUIREMENT_ID, PLAN_TYPE, INS_COMPANY_ID, INSURANCE_PLAN, PROVIDER_TAXONOMY, PROCEDURE_CODE, REQUIREMENT_ID, VENDOR_TYPE, IS_ACTIVE, CREATE_EMPLOYEE_NUMBER, CREATE_DATETIME, UPDATE_EMPLOYEE_NUMBER, UPDATE_DATETIME)
  VALUES(2049, NULL, NULL, NULL, NULL, 'D7980', 26, 'SBI', '1', 110840, TO_TIMESTAMP('2014-01-14 16:25:21:0','YYYY-MM-DD HH24:MI:SS:FF'), NULL, NULL);
INSERT INTO attachment_requirement(ATTACHMENT_REQUIREMENT_ID, PLAN_TYPE, INS_COMPANY_ID, INSURANCE_PLAN, PROVIDER_TAXONOMY, PROCEDURE_CODE, REQUIREMENT_ID, VENDOR_TYPE, IS_ACTIVE, CREATE_EMPLOYEE_NUMBER, CREATE_DATETIME, UPDATE_EMPLOYEE_NUMBER, UPDATE_DATETIME)
  VALUES(2050, NULL, NULL, NULL, NULL, 'D7981', 32, 'SBI', '1', 110840, TO_TIMESTAMP('2014-01-14 16:25:21:0','YYYY-MM-DD HH24:MI:SS:FF'), NULL, NULL);
INSERT INTO attachment_requirement(ATTACHMENT_REQUIREMENT_ID, PLAN_TYPE, INS_COMPANY_ID, INSURANCE_PLAN, PROVIDER_TAXONOMY, PROCEDURE_CODE, REQUIREMENT_ID, VENDOR_TYPE, IS_ACTIVE, CREATE_EMPLOYEE_NUMBER, CREATE_DATETIME, UPDATE_EMPLOYEE_NUMBER, UPDATE_DATETIME)
  VALUES(2051, NULL, NULL, NULL, NULL, 'D7982', 32, 'SBI', '1', 110840, TO_TIMESTAMP('2014-01-14 16:25:21:0','YYYY-MM-DD HH24:MI:SS:FF'), NULL, NULL);
INSERT INTO attachment_requirement(ATTACHMENT_REQUIREMENT_ID, PLAN_TYPE, INS_COMPANY_ID, INSURANCE_PLAN, PROVIDER_TAXONOMY, PROCEDURE_CODE, REQUIREMENT_ID, VENDOR_TYPE, IS_ACTIVE, CREATE_EMPLOYEE_NUMBER, CREATE_DATETIME, UPDATE_EMPLOYEE_NUMBER, UPDATE_DATETIME)
  VALUES(2052, NULL, NULL, NULL, NULL, 'D7982', 26, 'SBI', '1', 110840, TO_TIMESTAMP('2014-01-14 16:25:21:0','YYYY-MM-DD HH24:MI:SS:FF'), NULL, NULL);
INSERT INTO attachment_requirement(ATTACHMENT_REQUIREMENT_ID, PLAN_TYPE, INS_COMPANY_ID, INSURANCE_PLAN, PROVIDER_TAXONOMY, PROCEDURE_CODE, REQUIREMENT_ID, VENDOR_TYPE, IS_ACTIVE, CREATE_EMPLOYEE_NUMBER, CREATE_DATETIME, UPDATE_EMPLOYEE_NUMBER, UPDATE_DATETIME)
  VALUES(2053, NULL, NULL, NULL, NULL, 'D7983', 32, 'SBI', '1', 110840, TO_TIMESTAMP('2014-01-14 16:25:21:0','YYYY-MM-DD HH24:MI:SS:FF'), NULL, NULL);
INSERT INTO attachment_requirement(ATTACHMENT_REQUIREMENT_ID, PLAN_TYPE, INS_COMPANY_ID, INSURANCE_PLAN, PROVIDER_TAXONOMY, PROCEDURE_CODE, REQUIREMENT_ID, VENDOR_TYPE, IS_ACTIVE, CREATE_EMPLOYEE_NUMBER, CREATE_DATETIME, UPDATE_EMPLOYEE_NUMBER, UPDATE_DATETIME)
  VALUES(2054, NULL, NULL, NULL, NULL, 'D7983', 26, 'SBI', '1', 110840, TO_TIMESTAMP('2014-01-14 16:25:21:0','YYYY-MM-DD HH24:MI:SS:FF'), NULL, NULL);
INSERT INTO attachment_requirement(ATTACHMENT_REQUIREMENT_ID, PLAN_TYPE, INS_COMPANY_ID, INSURANCE_PLAN, PROVIDER_TAXONOMY, PROCEDURE_CODE, REQUIREMENT_ID, VENDOR_TYPE, IS_ACTIVE, CREATE_EMPLOYEE_NUMBER, CREATE_DATETIME, UPDATE_EMPLOYEE_NUMBER, UPDATE_DATETIME)
  VALUES(2055, NULL, NULL, NULL, NULL, 'D9110', 26, 'SBI', '1', 110840, TO_TIMESTAMP('2014-01-14 16:25:21:0','YYYY-MM-DD HH24:MI:SS:FF'), NULL, NULL);
INSERT INTO attachment_requirement(ATTACHMENT_REQUIREMENT_ID, PLAN_TYPE, INS_COMPANY_ID, INSURANCE_PLAN, PROVIDER_TAXONOMY, PROCEDURE_CODE, REQUIREMENT_ID, VENDOR_TYPE, IS_ACTIVE, CREATE_EMPLOYEE_NUMBER, CREATE_DATETIME, UPDATE_EMPLOYEE_NUMBER, UPDATE_DATETIME)
  VALUES(2056, NULL, NULL, NULL, NULL, 'D9220', 21, 'SBI', '1', 110840, TO_TIMESTAMP('2014-01-14 16:25:21:0','YYYY-MM-DD HH24:MI:SS:FF'), NULL, NULL);
INSERT INTO attachment_requirement(ATTACHMENT_REQUIREMENT_ID, PLAN_TYPE, INS_COMPANY_ID, INSURANCE_PLAN, PROVIDER_TAXONOMY, PROCEDURE_CODE, REQUIREMENT_ID, VENDOR_TYPE, IS_ACTIVE, CREATE_EMPLOYEE_NUMBER, CREATE_DATETIME, UPDATE_EMPLOYEE_NUMBER, UPDATE_DATETIME)
  VALUES(2057, NULL, NULL, NULL, NULL, 'D9221', 21, 'SBI', '1', 110840, TO_TIMESTAMP('2014-01-14 16:25:21:0','YYYY-MM-DD HH24:MI:SS:FF'), NULL, NULL);
INSERT INTO attachment_requirement(ATTACHMENT_REQUIREMENT_ID, PLAN_TYPE, INS_COMPANY_ID, INSURANCE_PLAN, PROVIDER_TAXONOMY, PROCEDURE_CODE, REQUIREMENT_ID, VENDOR_TYPE, IS_ACTIVE, CREATE_EMPLOYEE_NUMBER, CREATE_DATETIME, UPDATE_EMPLOYEE_NUMBER, UPDATE_DATETIME)
  VALUES(2058, NULL, NULL, NULL, NULL, 'D9230', 26, 'SBI', '1', 110840, TO_TIMESTAMP('2014-01-14 16:25:21:0','YYYY-MM-DD HH24:MI:SS:FF'), NULL, NULL);
INSERT INTO attachment_requirement(ATTACHMENT_REQUIREMENT_ID, PLAN_TYPE, INS_COMPANY_ID, INSURANCE_PLAN, PROVIDER_TAXONOMY, PROCEDURE_CODE, REQUIREMENT_ID, VENDOR_TYPE, IS_ACTIVE, CREATE_EMPLOYEE_NUMBER, CREATE_DATETIME, UPDATE_EMPLOYEE_NUMBER, UPDATE_DATETIME)
  VALUES(2059, NULL, NULL, NULL, NULL, 'D9241', 21, 'SBI', '1', 110840, TO_TIMESTAMP('2014-01-14 16:25:21:0','YYYY-MM-DD HH24:MI:SS:FF'), NULL, NULL);
INSERT INTO attachment_requirement(ATTACHMENT_REQUIREMENT_ID, PLAN_TYPE, INS_COMPANY_ID, INSURANCE_PLAN, PROVIDER_TAXONOMY, PROCEDURE_CODE, REQUIREMENT_ID, VENDOR_TYPE, IS_ACTIVE, CREATE_EMPLOYEE_NUMBER, CREATE_DATETIME, UPDATE_EMPLOYEE_NUMBER, UPDATE_DATETIME)
  VALUES(2060, NULL, NULL, NULL, NULL, 'D9242', 21, 'SBI', '1', 110840, TO_TIMESTAMP('2014-01-14 16:25:21:0','YYYY-MM-DD HH24:MI:SS:FF'), NULL, NULL);
INSERT INTO attachment_requirement(ATTACHMENT_REQUIREMENT_ID, PLAN_TYPE, INS_COMPANY_ID, INSURANCE_PLAN, PROVIDER_TAXONOMY, PROCEDURE_CODE, REQUIREMENT_ID, VENDOR_TYPE, IS_ACTIVE, CREATE_EMPLOYEE_NUMBER, CREATE_DATETIME, UPDATE_EMPLOYEE_NUMBER, UPDATE_DATETIME)
  VALUES(2061, NULL, NULL, NULL, NULL, 'D9310', 26, 'SBI', '1', 110840, TO_TIMESTAMP('2014-01-14 16:25:21:0','YYYY-MM-DD HH24:MI:SS:FF'), NULL, NULL);
INSERT INTO attachment_requirement(ATTACHMENT_REQUIREMENT_ID, PLAN_TYPE, INS_COMPANY_ID, INSURANCE_PLAN, PROVIDER_TAXONOMY, PROCEDURE_CODE, REQUIREMENT_ID, VENDOR_TYPE, IS_ACTIVE, CREATE_EMPLOYEE_NUMBER, CREATE_DATETIME, UPDATE_EMPLOYEE_NUMBER, UPDATE_DATETIME)
  VALUES(2062, NULL, NULL, NULL, NULL, 'D9430', 26, 'SBI', '1', 110840, TO_TIMESTAMP('2014-01-14 16:25:21:0','YYYY-MM-DD HH24:MI:SS:FF'), NULL, NULL);
INSERT INTO attachment_requirement(ATTACHMENT_REQUIREMENT_ID, PLAN_TYPE, INS_COMPANY_ID, INSURANCE_PLAN, PROVIDER_TAXONOMY, PROCEDURE_CODE, REQUIREMENT_ID, VENDOR_TYPE, IS_ACTIVE, CREATE_EMPLOYEE_NUMBER, CREATE_DATETIME, UPDATE_EMPLOYEE_NUMBER, UPDATE_DATETIME)
  VALUES(2065, NULL, NULL, 630, NULL, 'D9910', 26, 'SBI', '1', 110840, TO_TIMESTAMP('2014-01-14 16:25:21:0','YYYY-MM-DD HH24:MI:SS:FF'), NULL, NULL);
INSERT INTO attachment_requirement(ATTACHMENT_REQUIREMENT_ID, PLAN_TYPE, INS_COMPANY_ID, INSURANCE_PLAN, PROVIDER_TAXONOMY, PROCEDURE_CODE, REQUIREMENT_ID, VENDOR_TYPE, IS_ACTIVE, CREATE_EMPLOYEE_NUMBER, CREATE_DATETIME, UPDATE_EMPLOYEE_NUMBER, UPDATE_DATETIME)
  VALUES(2066, NULL, NULL, NULL, NULL, 'D9930', 26, 'SBI', '1', 110840, TO_TIMESTAMP('2014-01-14 16:25:21:0','YYYY-MM-DD HH24:MI:SS:FF'), NULL, NULL);
INSERT INTO attachment_requirement(ATTACHMENT_REQUIREMENT_ID, PLAN_TYPE, INS_COMPANY_ID, INSURANCE_PLAN, PROVIDER_TAXONOMY, PROCEDURE_CODE, REQUIREMENT_ID, VENDOR_TYPE, IS_ACTIVE, CREATE_EMPLOYEE_NUMBER, CREATE_DATETIME, UPDATE_EMPLOYEE_NUMBER, UPDATE_DATETIME)
  VALUES(2067, NULL, NULL, NULL, NULL, 'D9940', 26, 'SBI', '1', 110840, TO_TIMESTAMP('2014-01-14 16:25:21:0','YYYY-MM-DD HH24:MI:SS:FF'), NULL, NULL);
INSERT INTO attachment_requirement(ATTACHMENT_REQUIREMENT_ID, PLAN_TYPE, INS_COMPANY_ID, INSURANCE_PLAN, PROVIDER_TAXONOMY, PROCEDURE_CODE, REQUIREMENT_ID, VENDOR_TYPE, IS_ACTIVE, CREATE_EMPLOYEE_NUMBER, CREATE_DATETIME, UPDATE_EMPLOYEE_NUMBER, UPDATE_DATETIME)
  VALUES(2068, NULL, NULL, NULL, NULL, 'D9951', 26, 'SBI', '1', 110840, TO_TIMESTAMP('2014-01-14 16:25:21:0','YYYY-MM-DD HH24:MI:SS:FF'), NULL, NULL);
INSERT INTO attachment_requirement(ATTACHMENT_REQUIREMENT_ID, PLAN_TYPE, INS_COMPANY_ID, INSURANCE_PLAN, PROVIDER_TAXONOMY, PROCEDURE_CODE, REQUIREMENT_ID, VENDOR_TYPE, IS_ACTIVE, CREATE_EMPLOYEE_NUMBER, CREATE_DATETIME, UPDATE_EMPLOYEE_NUMBER, UPDATE_DATETIME)
  VALUES(2069, NULL, NULL, NULL, NULL, 'D9952', 26, 'SBI', '1', 110840, TO_TIMESTAMP('2014-01-14 16:25:21:0','YYYY-MM-DD HH24:MI:SS:FF'), NULL, NULL);
INSERT INTO attachment_requirement(ATTACHMENT_REQUIREMENT_ID, PLAN_TYPE, INS_COMPANY_ID, INSURANCE_PLAN, PROVIDER_TAXONOMY, PROCEDURE_CODE, REQUIREMENT_ID, VENDOR_TYPE, IS_ACTIVE, CREATE_EMPLOYEE_NUMBER, CREATE_DATETIME, UPDATE_EMPLOYEE_NUMBER, UPDATE_DATETIME)
  VALUES(2070, NULL, NULL, NULL, NULL, 'D9970', 26, 'SBI', '1', 110840, TO_TIMESTAMP('2014-01-14 16:25:21:0','YYYY-MM-DD HH24:MI:SS:FF'), NULL, NULL);
INSERT INTO attachment_requirement(ATTACHMENT_REQUIREMENT_ID, PLAN_TYPE, INS_COMPANY_ID, INSURANCE_PLAN, PROVIDER_TAXONOMY, PROCEDURE_CODE, REQUIREMENT_ID, VENDOR_TYPE, IS_ACTIVE, CREATE_EMPLOYEE_NUMBER, CREATE_DATETIME, UPDATE_EMPLOYEE_NUMBER, UPDATE_DATETIME)
  VALUES(2071, NULL, NULL, NULL, NULL, 'D9971', 26, 'SBI', '1', 110840, TO_TIMESTAMP('2014-01-14 16:25:21:0','YYYY-MM-DD HH24:MI:SS:FF'), NULL, NULL);
INSERT INTO attachment_requirement(ATTACHMENT_REQUIREMENT_ID, PLAN_TYPE, INS_COMPANY_ID, INSURANCE_PLAN, PROVIDER_TAXONOMY, PROCEDURE_CODE, REQUIREMENT_ID, VENDOR_TYPE, IS_ACTIVE, CREATE_EMPLOYEE_NUMBER, CREATE_DATETIME, UPDATE_EMPLOYEE_NUMBER, UPDATE_DATETIME)
  VALUES(2072, NULL, NULL, NULL, NULL, 'D9974', 26, 'SBI', '1', 110840, TO_TIMESTAMP('2014-01-14 16:25:21:0','YYYY-MM-DD HH24:MI:SS:FF'), NULL, NULL);
INSERT INTO attachment_requirement(ATTACHMENT_REQUIREMENT_ID, PLAN_TYPE, INS_COMPANY_ID, INSURANCE_PLAN, PROVIDER_TAXONOMY, PROCEDURE_CODE, REQUIREMENT_ID, VENDOR_TYPE, IS_ACTIVE, CREATE_EMPLOYEE_NUMBER, CREATE_DATETIME, UPDATE_EMPLOYEE_NUMBER, UPDATE_DATETIME)
  VALUES(2120, NULL, 114, NULL, NULL, NULL, 62, 'SBI', '1', 110840, TO_TIMESTAMP('2014-01-15 08:58:44:0','YYYY-MM-DD HH24:MI:SS:FF'), NULL, NULL);
INSERT INTO attachment_requirement(ATTACHMENT_REQUIREMENT_ID, PLAN_TYPE, INS_COMPANY_ID, INSURANCE_PLAN, PROVIDER_TAXONOMY, PROCEDURE_CODE, REQUIREMENT_ID, VENDOR_TYPE, IS_ACTIVE, CREATE_EMPLOYEE_NUMBER, CREATE_DATETIME, UPDATE_EMPLOYEE_NUMBER, UPDATE_DATETIME)
  VALUES(2144, NULL, 1, 10, NULL, 'D0120', 61, 'SBI', '1', 103326, TO_TIMESTAMP('2014-01-20 09:38:41:0','YYYY-MM-DD HH24:MI:SS:FF'), NULL, NULL);
INSERT INTO attachment_requirement(ATTACHMENT_REQUIREMENT_ID, PLAN_TYPE, INS_COMPANY_ID, INSURANCE_PLAN, PROVIDER_TAXONOMY, PROCEDURE_CODE, REQUIREMENT_ID, VENDOR_TYPE, IS_ACTIVE, CREATE_EMPLOYEE_NUMBER, CREATE_DATETIME, UPDATE_EMPLOYEE_NUMBER, UPDATE_DATETIME)
  VALUES(2145, 'P1', 1, 10, NULL, 'D0140', 64, 'SBI', '1', 103326, TO_TIMESTAMP('2014-01-20 16:13:54:0','YYYY-MM-DD HH24:MI:SS:FF'), NULL, NULL);
INSERT INTO attachment_requirement(ATTACHMENT_REQUIREMENT_ID, PLAN_TYPE, INS_COMPANY_ID, INSURANCE_PLAN, PROVIDER_TAXONOMY, PROCEDURE_CODE, REQUIREMENT_ID, VENDOR_TYPE, IS_ACTIVE, CREATE_EMPLOYEE_NUMBER, CREATE_DATETIME, UPDATE_EMPLOYEE_NUMBER, UPDATE_DATETIME)
  VALUES(2146, 'G4', 5, 1642, NULL, 'D0170', 71, 'SBI', '1', 103326, TO_TIMESTAMP('2014-01-21 10:09:23:0','YYYY-MM-DD HH24:MI:SS:FF'), NULL, NULL);
INSERT INTO attachment_requirement(ATTACHMENT_REQUIREMENT_ID, PLAN_TYPE, INS_COMPANY_ID, INSURANCE_PLAN, PROVIDER_TAXONOMY, PROCEDURE_CODE, REQUIREMENT_ID, VENDOR_TYPE, IS_ACTIVE, CREATE_EMPLOYEE_NUMBER, CREATE_DATETIME, UPDATE_EMPLOYEE_NUMBER, UPDATE_DATETIME)
  VALUES(2147, 'G4', 5, 1642, NULL, 'D0170', 67, 'SBI', '1', 103326, TO_TIMESTAMP('2014-01-21 10:09:23:0','YYYY-MM-DD HH24:MI:SS:FF'), NULL, NULL);
INSERT INTO attachment_requirement(ATTACHMENT_REQUIREMENT_ID, PLAN_TYPE, INS_COMPANY_ID, INSURANCE_PLAN, PROVIDER_TAXONOMY, PROCEDURE_CODE, REQUIREMENT_ID, VENDOR_TYPE, IS_ACTIVE, CREATE_EMPLOYEE_NUMBER, CREATE_DATETIME, UPDATE_EMPLOYEE_NUMBER, UPDATE_DATETIME)
  VALUES(2148, 'G4', 5, 1642, NULL, 'D0170', 63, 'SBI', '1', 103326, TO_TIMESTAMP('2014-01-21 10:09:23:0','YYYY-MM-DD HH24:MI:SS:FF'), NULL, NULL);
INSERT INTO attachment_requirement(ATTACHMENT_REQUIREMENT_ID, PLAN_TYPE, INS_COMPANY_ID, INSURANCE_PLAN, PROVIDER_TAXONOMY, PROCEDURE_CODE, REQUIREMENT_ID, VENDOR_TYPE, IS_ACTIVE, CREATE_EMPLOYEE_NUMBER, CREATE_DATETIME, UPDATE_EMPLOYEE_NUMBER, UPDATE_DATETIME)
  VALUES(2149, 'C2', 127, 127, '122300000X', NULL, 26, 'SBI', '1', 103208, TO_TIMESTAMP('2014-01-24 08:57:13:0','YYYY-MM-DD HH24:MI:SS:FF'), NULL, NULL);
INSERT INTO attachment_requirement(ATTACHMENT_REQUIREMENT_ID, PLAN_TYPE, INS_COMPANY_ID, INSURANCE_PLAN, PROVIDER_TAXONOMY, PROCEDURE_CODE, REQUIREMENT_ID, VENDOR_TYPE, IS_ACTIVE, CREATE_EMPLOYEE_NUMBER, CREATE_DATETIME, UPDATE_EMPLOYEE_NUMBER, UPDATE_DATETIME)
  VALUES(2150, 'C2', NULL, NULL, NULL, NULL, 26, 'SBI', '1', 103208, TO_TIMESTAMP('2014-01-24 10:31:51:0','YYYY-MM-DD HH24:MI:SS:FF'), NULL, NULL);
INSERT INTO attachment_requirement(ATTACHMENT_REQUIREMENT_ID, PLAN_TYPE, INS_COMPANY_ID, INSURANCE_PLAN, PROVIDER_TAXONOMY, PROCEDURE_CODE, REQUIREMENT_ID, VENDOR_TYPE, IS_ACTIVE, CREATE_EMPLOYEE_NUMBER, CREATE_DATETIME, UPDATE_EMPLOYEE_NUMBER, UPDATE_DATETIME)
  VALUES(2151, NULL, 127, NULL, NULL, NULL, 26, 'SBI', '1', 103208, TO_TIMESTAMP('2014-01-24 10:32:06:0','YYYY-MM-DD HH24:MI:SS:FF'), NULL, NULL);
INSERT INTO attachment_requirement(ATTACHMENT_REQUIREMENT_ID, PLAN_TYPE, INS_COMPANY_ID, INSURANCE_PLAN, PROVIDER_TAXONOMY, PROCEDURE_CODE, REQUIREMENT_ID, VENDOR_TYPE, IS_ACTIVE, CREATE_EMPLOYEE_NUMBER, CREATE_DATETIME, UPDATE_EMPLOYEE_NUMBER, UPDATE_DATETIME)
  VALUES(2152, NULL, NULL, 127, NULL, NULL, 26, 'SBI', '1', 103208, TO_TIMESTAMP('2014-01-24 10:32:24:0','YYYY-MM-DD HH24:MI:SS:FF'), NULL, NULL);
INSERT INTO attachment_requirement(ATTACHMENT_REQUIREMENT_ID, PLAN_TYPE, INS_COMPANY_ID, INSURANCE_PLAN, PROVIDER_TAXONOMY, PROCEDURE_CODE, REQUIREMENT_ID, VENDOR_TYPE, IS_ACTIVE, CREATE_EMPLOYEE_NUMBER, CREATE_DATETIME, UPDATE_EMPLOYEE_NUMBER, UPDATE_DATETIME)
  VALUES(2153, NULL, NULL, NULL, '122300000X', NULL, 26, 'SBI', '1', 103208, TO_TIMESTAMP('2014-01-24 10:32:41:0','YYYY-MM-DD HH24:MI:SS:FF'), NULL, NULL);
INSERT INTO attachment_requirement(ATTACHMENT_REQUIREMENT_ID, PLAN_TYPE, INS_COMPANY_ID, INSURANCE_PLAN, PROVIDER_TAXONOMY, PROCEDURE_CODE, REQUIREMENT_ID, VENDOR_TYPE, IS_ACTIVE, CREATE_EMPLOYEE_NUMBER, CREATE_DATETIME, UPDATE_EMPLOYEE_NUMBER, UPDATE_DATETIME)
  VALUES(2154, 'C2', 127, 127, '122300000X', NULL, 18, 'SBI', '1', 103208, TO_TIMESTAMP('2014-01-24 10:32:58:0','YYYY-MM-DD HH24:MI:SS:FF'), NULL, NULL);
INSERT INTO attachment_requirement(ATTACHMENT_REQUIREMENT_ID, PLAN_TYPE, INS_COMPANY_ID, INSURANCE_PLAN, PROVIDER_TAXONOMY, PROCEDURE_CODE, REQUIREMENT_ID, VENDOR_TYPE, IS_ACTIVE, CREATE_EMPLOYEE_NUMBER, CREATE_DATETIME, UPDATE_EMPLOYEE_NUMBER, UPDATE_DATETIME)
  VALUES(2155, 'C2', 127, 127, '122300000X', NULL, 18, 'SBI', '1', 103208, TO_TIMESTAMP('2014-01-24 10:33:12:0','YYYY-MM-DD HH24:MI:SS:FF'), NULL, NULL);
INSERT INTO attachment_requirement(ATTACHMENT_REQUIREMENT_ID, PLAN_TYPE, INS_COMPANY_ID, INSURANCE_PLAN, PROVIDER_TAXONOMY, PROCEDURE_CODE, REQUIREMENT_ID, VENDOR_TYPE, IS_ACTIVE, CREATE_EMPLOYEE_NUMBER, CREATE_DATETIME, UPDATE_EMPLOYEE_NUMBER, UPDATE_DATETIME)
  VALUES(2156, 'C2', 127, 127, '122300000X', NULL, 2, 'SBI', '1', 103208, TO_TIMESTAMP('2014-01-24 10:33:27:0','YYYY-MM-DD HH24:MI:SS:FF'), NULL, NULL);
INSERT INTO attachment_requirement(ATTACHMENT_REQUIREMENT_ID, PLAN_TYPE, INS_COMPANY_ID, INSURANCE_PLAN, PROVIDER_TAXONOMY, PROCEDURE_CODE, REQUIREMENT_ID, VENDOR_TYPE, IS_ACTIVE, CREATE_EMPLOYEE_NUMBER, CREATE_DATETIME, UPDATE_EMPLOYEE_NUMBER, UPDATE_DATETIME)
  VALUES(2157, 'C2', 127, 127, '122300000X', NULL, 32, 'SBI', '1', 103208, TO_TIMESTAMP('2014-01-24 10:33:43:0','YYYY-MM-DD HH24:MI:SS:FF'), NULL, NULL);
INSERT INTO attachment_requirement(ATTACHMENT_REQUIREMENT_ID, PLAN_TYPE, INS_COMPANY_ID, INSURANCE_PLAN, PROVIDER_TAXONOMY, PROCEDURE_CODE, REQUIREMENT_ID, VENDOR_TYPE, IS_ACTIVE, CREATE_EMPLOYEE_NUMBER, CREATE_DATETIME, UPDATE_EMPLOYEE_NUMBER, UPDATE_DATETIME)
  VALUES(2158, 'C2', 127, 127, '122300000X', 'D7210', 26, 'SBI', '1', 103208, TO_TIMESTAMP('2014-01-24 11:53:11:0','YYYY-MM-DD HH24:MI:SS:FF'), NULL, NULL);
INSERT INTO attachment_requirement(ATTACHMENT_REQUIREMENT_ID, PLAN_TYPE, INS_COMPANY_ID, INSURANCE_PLAN, PROVIDER_TAXONOMY, PROCEDURE_CODE, REQUIREMENT_ID, VENDOR_TYPE, IS_ACTIVE, CREATE_EMPLOYEE_NUMBER, CREATE_DATETIME, UPDATE_EMPLOYEE_NUMBER, UPDATE_DATETIME)
  VALUES(2159, 'C2', NULL, NULL, NULL, 'D7210', 26, 'SBI', '1', 103208, TO_TIMESTAMP('2014-01-24 11:53:23:0','YYYY-MM-DD HH24:MI:SS:FF'), NULL, NULL);
INSERT INTO attachment_requirement(ATTACHMENT_REQUIREMENT_ID, PLAN_TYPE, INS_COMPANY_ID, INSURANCE_PLAN, PROVIDER_TAXONOMY, PROCEDURE_CODE, REQUIREMENT_ID, VENDOR_TYPE, IS_ACTIVE, CREATE_EMPLOYEE_NUMBER, CREATE_DATETIME, UPDATE_EMPLOYEE_NUMBER, UPDATE_DATETIME)
  VALUES(2160, NULL, 127, NULL, NULL, 'D4261', 26, 'SBI', '1', 103208, TO_TIMESTAMP('2014-01-24 11:53:37:0','YYYY-MM-DD HH24:MI:SS:FF'), NULL, NULL);
INSERT INTO attachment_requirement(ATTACHMENT_REQUIREMENT_ID, PLAN_TYPE, INS_COMPANY_ID, INSURANCE_PLAN, PROVIDER_TAXONOMY, PROCEDURE_CODE, REQUIREMENT_ID, VENDOR_TYPE, IS_ACTIVE, CREATE_EMPLOYEE_NUMBER, CREATE_DATETIME, UPDATE_EMPLOYEE_NUMBER, UPDATE_DATETIME)
  VALUES(2161, NULL, NULL, 127, NULL, 'D4260', 26, 'SBI', '1', 103208, TO_TIMESTAMP('2014-01-24 11:53:52:0','YYYY-MM-DD HH24:MI:SS:FF'), NULL, NULL);
INSERT INTO attachment_requirement(ATTACHMENT_REQUIREMENT_ID, PLAN_TYPE, INS_COMPANY_ID, INSURANCE_PLAN, PROVIDER_TAXONOMY, PROCEDURE_CODE, REQUIREMENT_ID, VENDOR_TYPE, IS_ACTIVE, CREATE_EMPLOYEE_NUMBER, CREATE_DATETIME, UPDATE_EMPLOYEE_NUMBER, UPDATE_DATETIME)
  VALUES(2162, NULL, NULL, NULL, '122300000X', 'D4260', 26, 'SBI', '1', 103208, TO_TIMESTAMP('2014-01-24 11:54:05:0','YYYY-MM-DD HH24:MI:SS:FF'), NULL, NULL);
INSERT INTO attachment_requirement(ATTACHMENT_REQUIREMENT_ID, PLAN_TYPE, INS_COMPANY_ID, INSURANCE_PLAN, PROVIDER_TAXONOMY, PROCEDURE_CODE, REQUIREMENT_ID, VENDOR_TYPE, IS_ACTIVE, CREATE_EMPLOYEE_NUMBER, CREATE_DATETIME, UPDATE_EMPLOYEE_NUMBER, UPDATE_DATETIME)
  VALUES(2163, 'C2', 127, 127, '122300000X', 'D4260', 18, 'SBI', '1', 103208, TO_TIMESTAMP('2014-01-24 11:54:18:0','YYYY-MM-DD HH24:MI:SS:FF'), NULL, NULL);
INSERT INTO attachment_requirement(ATTACHMENT_REQUIREMENT_ID, PLAN_TYPE, INS_COMPANY_ID, INSURANCE_PLAN, PROVIDER_TAXONOMY, PROCEDURE_CODE, REQUIREMENT_ID, VENDOR_TYPE, IS_ACTIVE, CREATE_EMPLOYEE_NUMBER, CREATE_DATETIME, UPDATE_EMPLOYEE_NUMBER, UPDATE_DATETIME)
  VALUES(2164, 'C2', 127, 127, '122300000X', 'D4260', 18, 'SBI', '1', 103208, TO_TIMESTAMP('2014-01-24 11:54:33:0','YYYY-MM-DD HH24:MI:SS:FF'), NULL, NULL);
INSERT INTO attachment_requirement(ATTACHMENT_REQUIREMENT_ID, PLAN_TYPE, INS_COMPANY_ID, INSURANCE_PLAN, PROVIDER_TAXONOMY, PROCEDURE_CODE, REQUIREMENT_ID, VENDOR_TYPE, IS_ACTIVE, CREATE_EMPLOYEE_NUMBER, CREATE_DATETIME, UPDATE_EMPLOYEE_NUMBER, UPDATE_DATETIME)
  VALUES(2165, 'C2', 127, 127, '122300000X', 'D7210', 2, 'SBI', '1', 103208, TO_TIMESTAMP('2014-01-24 11:54:46:0','YYYY-MM-DD HH24:MI:SS:FF'), NULL, NULL);
INSERT INTO attachment_requirement(ATTACHMENT_REQUIREMENT_ID, PLAN_TYPE, INS_COMPANY_ID, INSURANCE_PLAN, PROVIDER_TAXONOMY, PROCEDURE_CODE, REQUIREMENT_ID, VENDOR_TYPE, IS_ACTIVE, CREATE_EMPLOYEE_NUMBER, CREATE_DATETIME, UPDATE_EMPLOYEE_NUMBER, UPDATE_DATETIME)
  VALUES(2166, 'C2', 127, 127, '122300000X', 'D7210', 32, 'SBI', '1', 103208, TO_TIMESTAMP('2014-01-24 11:54:59:0','YYYY-MM-DD HH24:MI:SS:FF'), NULL, NULL);
INSERT INTO attachment_requirement(ATTACHMENT_REQUIREMENT_ID, PLAN_TYPE, INS_COMPANY_ID, INSURANCE_PLAN, PROVIDER_TAXONOMY, PROCEDURE_CODE, REQUIREMENT_ID, VENDOR_TYPE, IS_ACTIVE, CREATE_EMPLOYEE_NUMBER, CREATE_DATETIME, UPDATE_EMPLOYEE_NUMBER, UPDATE_DATETIME)
  VALUES(2181, 'C5', NULL, 0, '124Q00000X', 'D0120', 61, 'SBI', '1', 103326, TO_TIMESTAMP('2014-01-29 10:39:01:0','YYYY-MM-DD HH24:MI:SS:FF'), NULL, NULL);



------------------------------------------------------------------------------------------
-- Apply insurance plan id from claim insurance to patient pay/adj ledger               --
-- Kevin Tran (2/3/2013)                                                                --
------------------------------------------------------------------------------------------

-- Primary Claims
UPDATE PATIENT_PAY_ADJ_LEDGER PPAL
SET PPAL.PRI_INSURANCE_PLAN = (
    SELECT DISTINCT CI.INS_PLAN_ID
    FROM CLAIM C

        LEFT JOIN CLAIM_INSURANCE CI
            ON (
                (C.CLAIM_ROLE = 'PRIMARY' AND C.PRIMARY_CLAIM_INS_ID = CI.CLAIM_INS_ID) OR
                (C.CLAIM_ROLE = 'SECONDARY' AND C.SECONDARY_CLAIM_INS_ID = CI.CLAIM_INS_ID)
            )

    WHERE C.CLAIM_ID = PPAL.CLAIM_ID AND
        C.CLAIM_ROLE = 'PRIMARY' AND
        CI.INS_PLAN_ID != 0
);

-- Secondary Claims
UPDATE PATIENT_PAY_ADJ_LEDGER PPAL
SET PPAL.SEC_INSURANCE_PLAN = (
    SELECT DISTINCT CI.INS_PLAN_ID
    FROM CLAIM C

        LEFT JOIN CLAIM_INSURANCE CI
            ON (
                (C.CLAIM_ROLE = 'PRIMARY' AND C.PRIMARY_CLAIM_INS_ID = CI.CLAIM_INS_ID) OR
                (C.CLAIM_ROLE = 'SECONDARY' AND C.SECONDARY_CLAIM_INS_ID = CI.CLAIM_INS_ID)
            )

    WHERE C.CLAIM_ID = PPAL.CLAIM_ID AND
        C.CLAIM_ROLE = 'SECONDARY' AND
        CI.INS_PLAN_ID != 0
);