--------------------------------------------------------
--  DDL for Table INSURANCE_PLAN_OVERRIDES
--------------------------------------------------------

  CREATE TABLE "LIBERTY_DATA"."INSURANCE_PLAN_OVERRIDES"
   (	"INS_PLAN_OVERRIDE_ID" NUMBER(10,0), 
	"INS_PLAN_ID" NUMBER(10,0), 
	"CDT_CODE" VARCHAR2(5 BYTE), 
	"BENEFIT_CATEGORY_ID" NUMBER(5,0), 
	"IS_NON_COVERED_ITEM" CHAR(1 BYTE), 
	"NON_COVERED_CHG_METHOD" CHAR(1 BYTE), 
	"NEXT_VISIT_RESTRICTION" CHAR(1 BYTE), 
	"NEXT_VISIT" NUMBER(5,0), 
	"AGE_RESTRICTION" CHAR(10 BYTE), 
	"FROM_AGE" NUMBER(3,0), 
	"TO_AGE" NUMBER(3,0), 
	"FREQUENCY_RESTRICTION" CHAR(1 BYTE), 
	"FREQUENCY" NUMBER(5,0), 
	"QTY_RESTRICTION" CHAR(10 BYTE), 
	"MAX_QTY" NUMBER(3,0), 
	"PRE_AUTH_REQUIRED" CHAR(1 BYTE), 
	"METAL_LAB_UPGRADE" NUMBER(7,2), 
	"MOLAR_UPGRADE" NUMBER(7,2), 
	"FLEXITE_UPGRADE" NUMBER(7,2), 
	"UP_DOWN_OPTION" CHAR(2 BYTE), 
	"UPGRADE_DOWNGRADE_CODE" VARCHAR2(5 BYTE), 
	"CREATE_EMPLOYEE" NUMBER(10,0), 
	"CREATE_DATETIME" TIMESTAMP (6), 
	"UPDATE_EMPLOYEE" NUMBER(10,0), 
	"UPDATE_DATETIME" TIMESTAMP (6)
   ) PCTFREE 10 PCTUSED 40 INITRANS 1 MAXTRANS 255 NOCOMPRESS LOGGING
  STORAGE(INITIAL 65536 NEXT 1048576 MINEXTENTS 1 MAXEXTENTS 2147483645
  PCTINCREASE 0 FREELISTS 1 FREELIST GROUPS 1 BUFFER_POOL DEFAULT)
  TABLESPACE "BNDNEW_DATA" ;
