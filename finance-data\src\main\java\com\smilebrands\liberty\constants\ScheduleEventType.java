package com.smilebrands.liberty.constants;

/**
 * Java5 style enum class to represent the various types of schedule events.
 * 
 * <AUTHOR> Smile Brands Inc, 2011
 */
public enum ScheduleEventType{

    UNAVAILABLE("Unavailable"){
        @Override public String getDescription() { return "Describes availability that represents a provider's off time"; }
        @Override public boolean isAcrossColumns() { return true; }
        @Override public boolean isAvailability() { return true; }
    },
    PROVIDER_REQUEST ("Provider requested Block") {
        @Override public String getDescription() { return "Used for blocking an \"open reservation\" when a provider requests a block without any reasonable explanation."; }
    },
    PATIENT_ISSUE ("Patient Issue Block") {
        @Override public String getDescription() { return "Used for blocking an \"open reservation\" when a provider has a difficult patient that requires them to restrict their schedule during that time."; }
    },
    MAJOR_TX ("Major Treatment block") {
        @Override public String getDescription() { return "Used for blocking an \"open reservation\" when a provider has a major case that requires them to restrict their schedule during that time."; }
    },
    SPECIALTY_SERVICES ("Specialty Service block") {
        @Override public String getDescription() { return "Used for blocking an \"open reservation\" due to specialty providers appointments and available room limitations."; }
    },
    HOLIDAY ("Holiday Event") {
        @Override public String getDescription() { return "Used for blocking days when the provider is out on Holiday"; }
        @Override public boolean isSpecialCaseType() { return true; }
    },
    STAFF_MEETING ("Staff Meeting block") {
        @Override public String getDescription() { return "Used for blocking a unit of time when the provider is in a staff meeting"; }
        @Override public boolean isSpecialCaseType() { return true; }
    },
    PROVIDER_MEETING ("Provider Meeting block") {
        @Override public String getDescription() { return "Used for blocking a unit of time when the provider is out for business related meetings (example: CE course)"; }
        @Override public boolean isSpecialCaseType() { return true; }
    },
    NOT_IN ("Provider Is Not In the Office") {
        @Override public String getDescription() { return "Used for blocking hours and/or days when the provider is out of the office for any reason other than those listed above"; }
        @Override public boolean isSpecialCaseType() { return true; }
    },
    OUT_SICK ("Provider Is Not In the Office due to illness") {
        @Override public String getDescription() { return "Provider special case to flag a provider being out sick."; }
        @Override public boolean isSpecialCaseType() { return true; }
    },
    VACATION("Vacation"){
        @Override public String getDescription() { return "Events that are used to describe provider's vacations"; }
        @Override public boolean isAcrossColumns() { return true; }
        @Override public boolean isAvailability() { return true; }
        @Override public boolean isSpecialCaseType() { return true; }
    },
    RESERVATION("Reservation"){
        @Override public String getDescription() { return "Schedule events that are pending completion"; }
        @Override public boolean isScheduleEvent() { return true; }
    },
   	SCHEDULE_EVENT("ScheduleEvent"){
		@Override public String getDescription() { return "Events that are considered patient events"; }
		@Override public boolean isScheduleEvent() { return true; }
   	},
    WITHHOLD("Withhold"){
        @Override public String getDescription() { return "Describes availability that is blocked out for all scheduling"; }
        @Override public boolean isAvailability() { return true; }
    },
   	AVAILABILITY("Availability"){

		@Override public String getDescription() { return "Events that are used to configure provider's availability"; }
		@Override public boolean isConfigurable() { return true; }
	    @Override public boolean isAvailability() { return true; }
        @Override public boolean isSpecialCaseType() { return true; }
   	},
   	LUNCH("Lunch"){
		@Override public String getDescription() { return "Events that are used to describe lunch and break times for providers"; }
		@Override public boolean isConfigurable() { return true; }
		@Override public boolean isAcrossColumns() { return true; }
        @Override public boolean isSpecialCaseType() { return true; }
   	},
	EARMARK("Earmark"){
		@Override public String getDescription() { return "Describes availability that is blocked out for specific purposes"; }
		@Override public boolean isAvailability() { return true; }
   	};

    private final String scheduleEventType;

    private ScheduleEventType(String scheduleEventTypeIn) { this.scheduleEventType = scheduleEventTypeIn; }

    public String getDescription() { return ""; }

    public boolean isScheduleEvent() { return false; }

	public boolean isConfigurable() { return false; }

	public boolean isAcrossColumns(){ return false; }

	public boolean isAvailability(){ return false; }

    public boolean isSpecialCaseType(){ return false; }

	@Override
    public String toString() { return this.scheduleEventType; }

	public String scheduleEventType() { return this.scheduleEventType; }

}
