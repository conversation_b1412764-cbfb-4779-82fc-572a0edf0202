--------------------------------------------------------
--  DDL for Table TOOTH_NUMBERING
--------------------------------------------------------

  CREATE TABLE "LIBERTY_DATA"."TOOTH_NUMBERING"
   (	"TOOTH_NUMBER" VARCHAR2(2 BYTE), 
	"ANSI_TOOTH_NUMBER" VARCHAR2(2 BYTE), 
	"SUPER_NUMERATOR" VARCHAR2(2 BYTE), 
	"IS_PERMANENT_TOOTH" NUMBER(1,0), 
	"TYPE_DESCRIPTION" VARCHAR2(25 BYTE), 
	"DESCRIPTION" VARCHAR2(25 BYTE), 
	"ADDITIONAL_DESCRIPTION" VARCHAR2(25 BYTE), 
	"QUADRANT" VARCHAR2(2 BYTE), 
	"ANSI_QUADRANT" VARCHAR2(25 BYTE), 
	"QUADRANT_DESCRIPTION" VARCHAR2(25 BYTE), 
	"ARCH" CHAR(2 BYTE), 
	"ARCH_DESCRIPTION" VARCHAR2(25 BYTE), 
	"ROOTS" NUMBER(1,0), 
	"ERRUPT_FROM_MONTHS" NUMBER(2,0), 
	"EERRUPT_TO_MONTHS" NUMBER(2,0), 
	"SHED_YEARS" NUMBER(2,0)
   ) PCTFREE 10 PCTUSED 40 INITRANS 1 MAXTRANS 255 NOCOMPRESS LOGGING
  STORAGE(INITIAL 65536 NEXT 1048576 MINEXTENTS 1 MAXEXTENTS 2147483645
  PCTINCREASE 0 FREELISTS 1 FREELIST GROUPS 1 BUFFER_POOL DEFAULT)
  TABLESPACE "BNDNEW_DATA" ;
