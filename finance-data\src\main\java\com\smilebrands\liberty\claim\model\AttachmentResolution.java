package com.smilebrands.liberty.claim.model;

import com.smilebrands.liberty.constants.ImageSourceType;
import com.smilebrands.liberty.model.BaseObject;
import com.smilebrands.liberty.utility.JsonDateDeSerializer;
import com.smilebrands.liberty.utility.JsonDateSerializer;
import org.codehaus.jackson.map.annotate.JsonDeserialize;
import org.codehaus.jackson.map.annotate.JsonSerialize;

import javax.persistence.*;
import java.util.Date;

/*
* <PERSON><PERSON> - Smile Brands Inc. 12/28/12
*
*/
@Entity
@NamedQueries(value = {
        @NamedQuery(name = "AttachmentResolution.findByRequiredAttachmentId",
                    query = "SELECT p FROM AttachmentResolution p " +
                            "WHERE p.claimRequiredAttachmentId = ?1 " +
                            "AND p.active = true")
})
@Table(name = "ATTACHMENT_RESOLUTION")
public class AttachmentResolution extends BaseObject{

    @Id
    @Column(name = "ATTACHMENT_RESOLUTION_ID")
    @GeneratedValue(generator = "AttachmentResSeq")
    @SequenceGenerator(name = "AttachmentResSeq", sequenceName = "ATTACHMENT_RES_ID_SEQ", allocationSize = 1)
    protected Long attachmentResolutionId;

    @Column(name = "REQ_ATTACHMENT_ID")
    protected Long claimRequiredAttachmentId;

    @Column(name = "ATTACHMENT_SOURCE_TYPE")
    @Enumerated(EnumType.STRING)
    protected ImageSourceType imageSourceType;

    @Column(name = "ATTACHMENT_SOURCE")
    protected String imageSource;

    @Column(name = "ATTACHMENT_REF_ID")
    protected Long imageSourceReferenceId;

    @Temporal(TemporalType.TIMESTAMP)
    @Column(name = "CREATE_DATETIME")
    private Date creationDatetime;

    @Column(name = "CREATE_EMPLOYEE_NUMBER")
    private Integer createEmployeeNumber;

    @Column(name = "IS_ACTIVE")
    private Boolean active;

    @Override
    public boolean equals(Object o) {
        if (this == o) return true;
        if (o == null || getClass() != o.getClass()) return false;

        AttachmentResolution that = (AttachmentResolution) o;

        if (attachmentResolutionId != null ? !attachmentResolutionId.equals(that.attachmentResolutionId) : that.attachmentResolutionId != null)
            return false;

        return true;
    }

    @Override
    public int hashCode() {
        return attachmentResolutionId != null ? attachmentResolutionId.hashCode() : 0;
    }

    public Long getAttachmentResolutionId() {
        return attachmentResolutionId;
    }

    public void setAttachmentResolutionId(Long attachmentResolutionId) {
        this.attachmentResolutionId = attachmentResolutionId;
    }

    public Long getClaimRequiredAttachmentId() {
        return claimRequiredAttachmentId;
    }

    public void setClaimRequiredAttachmentId(Long claimRequiredAttachmentId) {
        this.claimRequiredAttachmentId = claimRequiredAttachmentId;
    }

    public ImageSourceType getImageSourceType() {
        return imageSourceType;
    }

    public void setImageSourceType(ImageSourceType imageSourceType) {
        this.imageSourceType = imageSourceType;
    }

    @JsonSerialize(using = JsonDateSerializer.class)
    public Date getCreationDatetime() {
        return creationDatetime;
    }

    @JsonDeserialize(using = JsonDateDeSerializer.class)
    public void setCreationDatetime(Date creationDatetime) {
        this.creationDatetime = creationDatetime;
    }

    public Boolean getActive() {
        return active;
    }

    public void setActive(Boolean active) {
        this.active = active;
    }

    public Integer getCreateEmployeeNumber() {
        return createEmployeeNumber;
    }

    public void setCreateEmployeeNumber(Integer createEmployeeNumber) {
        this.createEmployeeNumber = createEmployeeNumber;
    }

    public String getImageSource() {
        return imageSource;
    }

    public void setImageSource(String imageSource) {
        this.imageSource = imageSource;
    }

    public Long getImageSourceReferenceId() {
        return imageSourceReferenceId;
    }

    public void setImageSourceReferenceId(Long imageSourceReferenceId) {
        this.imageSourceReferenceId = imageSourceReferenceId;
    }

    @Transient
    private Integer requirementId;

    public Integer getRequirementId() {
        return requirementId;
    }

    public void setRequirementId(Integer requirementId) {
        this.requirementId = requirementId;
    }
}