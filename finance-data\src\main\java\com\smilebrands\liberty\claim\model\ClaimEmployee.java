package com.smilebrands.liberty.claim.model;

import com.smilebrands.liberty.model.BaseObject;

import javax.persistence.*;

/**
 * User: <PERSON><PERSON>
 * Date: 3/12/13
 */
@Entity
@Table(name = "EMPLOYEE")
@SuppressWarnings("serial")
public class ClaimEmployee extends BaseObject {

    private static final long serialVersionUID = 1L;

    @Id
    @Basic(optional = false)
    @Column(name="EMPLOYEE_NUMBER")
    private Integer employeeNumber;

    @Column(name="FIRST_NAME")
    private String firstName;

    @Column(name="LAST_NAME")
    private String lastName;

    @Column(name="EMPLOYMENT_STATUS")
    private String employmentStatus;

    @Column(name="TITLE")
    private String title;

    @Column(name="EMAIL_ADDRESS")
    private String emailAddress;

    @Override
    public boolean equals(Object o) {
        if (this == o) return true;
        if (o == null || getClass() != o.getClass()) return false;

        ClaimEmployee claimEmployee = (ClaimEmployee) o;

        if (!employeeNumber.equals(claimEmployee.employeeNumber)) return false;

        return true;
    }

    @Override
    public int hashCode() {
        return employeeNumber.hashCode();
    }

    public Integer getEmployeeNumber() {
        return employeeNumber;
    }

    public void setEmployeeNumber(Integer employeeNumber) {
        this.employeeNumber = employeeNumber;
    }

    public String getFirstName() {
        return firstName;
    }

    public void setFirstName(String firstName) {
        this.firstName = firstName;
    }

    public String getLastName() {
        return lastName;
    }

    public void setLastName(String lastName) {
        this.lastName = lastName;
    }

    public String getEmploymentStatus() {
        return employmentStatus;
    }

    public void setEmploymentStatus(String employmentStatus) {
        this.employmentStatus = employmentStatus;
    }

    public String getTitle() {
        return title;
    }

    public void setTitle(String title) {
        this.title = title;
    }

    public String getEmailAddress() {
        return emailAddress;
    }

    public void setEmailAddress(String emailAddress) {
        this.emailAddress = emailAddress;
    }
}
