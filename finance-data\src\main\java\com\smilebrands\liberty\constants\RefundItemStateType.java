package com.smilebrands.liberty.constants;

/**
 * Java5 style enum class to represent the various states
 * a refund request works through.
 *
 * Added some additional sugar of a description for each type.
 *
 * <AUTHOR> Bright Now! Dental, 2009
 */
public enum RefundItemStateType {

    OPEN("Open") {

        @Override
        public String getDescription() {
            return "Open is the initial state for each Refund Request.";
        }

        @Override
        public boolean display() {
            return false;
        }
    },
    RE_OPEN("Re-open") {

        @Override
        public String getDescription() {
            return "Re-open is used if an item has been closed or canceled.";
        }

        @Override
        public boolean display() {
            return false;
        }
    },
    CBO_REVIEW("CBO Review") {

        @Override
        public String getDescription() {
            return "Select the 'CBO Review' state if you are actively working on this request.";
        }
        @Override
        public boolean display() {
            return true;
        }
    },
    CBO_APPROVED("CBO Approved") {

        @Override
        public String getDescription() {
            return "Select the 'CBO APPROVED' state if you are actively working on this request.";
        }
        @Override
        public boolean display() {
            return true;
        }
    },
    CBO_DENIED("CBO Denied") {

        @Override
        public String getDescription() {
            return "Select the 'CBO Denied' state if you do not approve of the payment of the refund.";
        }
        @Override
        public boolean display() {
            return true;
        }
    },
    DMO_REVIEW("DMO Review") {

        @Override
        public String getDescription() {
            return "Select the 'DMO Review' state if you are actively working on this request.";
        }
        @Override
        public boolean display() {
            return true;
        }
    },
    DMO_APPROVED("DMO Approved") {

        @Override
        public String getDescription() {
            return "Select the 'DMO APPROVED' state if you are actively working on this request.";
        }
        @Override
        public boolean display() {
            return true;
        }
    },
    DMO_DENIED("DMO Denied") {

        @Override
        public String getDescription() {
            return "Select the 'DMO Denied' state if you do not approve of the payment of the refund.";
        }
        @Override
        public boolean display() {
            return true;
        }
    },
    AVP_REVIEW("AVP Review") {

        @Override
        public String getDescription() {
            return "Select the 'DMO Review' state if you are actively working on this request.";
        }
        @Override
        public boolean display() {
            return true;
        }
    },
    AVP_APPROVED("AVP Approved") {

        @Override
        public String getDescription() {
            return "Select the 'DMO APPROVED' state if you are actively working on this request.";
        }
        @Override
        public boolean display() {
            return true;
        }
    },
    AVP_DENIED("AVP Denied") {

        @Override
        public String getDescription() {
            return "Select the 'DMO Denied' state if you do not approve of the payment of the refund.";
        }
        @Override
        public boolean display() {
            return true;
        }
    },
    CONT_REVIEW("Controller Review") {

        @Override
        public String getDescription() {
            return "Select the 'DMO Review' state if you are actively working on this request.";
        }
        @Override
        public boolean display() {
            return true;
        }
    },
    CONT_APPROVED("Controller Approved") {

        @Override
        public String getDescription() {
            return "Select the 'DMO APPROVED' state if you are actively working on this request.";
        }
        @Override
        public boolean display() {
            return true;
        }
    },
    CONT_DENIED("Controller Denied") {

        @Override
        public String getDescription() {
            return "Select the 'DMO Denied' state if you do not approve of the payment of the refund.";
        }
        @Override
        public boolean display() {
            return true;
        }
    },
    //    IN_REVIEW("In Review") {
//
//        @Override
//        public String getDescription() {
//            return "Select the 'In Review' state if you are actively working on this request.";
//        }
//        @Override
//        public boolean display() {
//            return false;
//        }
//    },
//    APPROVED("Approved") {
//
//        @Override
//        public String getDescription() {
//            return "Select the 'Approved' state if you approve of the payment of the refund.";
//        }
//        @Override
//        public boolean display() {
//            return false;
//        }
//    },
    PAID("Paid") {

        @Override
        public String getDescription() {
            return "Select the 'Paid' state if the refund has been disbursed.";
        }
        @Override
        public boolean display() {
            return true;
        }
    },
    QUEUE_FOR_PAYMENT ("Queue For Payment") {

        @Override
        public String getDescription() {
            return "Queue this refund item for payment";
        }
        @Override
        public boolean display() {
            return true;
        }
    },
    SUBMITTED("Submitted") {

        @Override
        public String getDescription() {
            return "Submitted to Qsi Transaction Gateway.";
        }
        @Override
        public boolean display() {
            return false;
        }

    },
    SUBMIT_FAILED("Submit Failed") {

        @Override
        public String getDescription() {
            return "Failed to submit to Qsi Transaction Gateway.";
        }
        @Override
        public boolean display() {
            return false;
        }

    },
    CANCELED("Canceled") {

        @Override
        public String getDescription() {
            return "Refund request has been canceled.";
        }
        @Override
        public boolean display() {
            return true;
        }

    },
    CLOSED("Closed") {

        @Override
        public String getDescription() {
            return "Select the 'Closed' state if all work has been completed.";
        }
        @Override
        public boolean display() {
            return true;
        }
    };
    private final String strValue;

    private RefundItemStateType(final String strValue) {
        this.strValue = strValue;
    }

    public String getDescription() {
        return "";
    }

    public boolean display() {
        return false;
    }

    @Override
    public String toString() {
        return this.strValue;
    }
}