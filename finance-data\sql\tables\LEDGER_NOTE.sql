--------------------------------------------------------
--  DDL for Table LEDGER_NOTE
--------------------------------------------------------

  CREATE TABLE "LIBERTY_DATA"."LEDGER_NOTE"
   (	"LEDGER_NOTE_ID" NUMBER(11,0), 
	"NOTE_REFERENCE_ID" NUMBER(10,0), 
	"NOTE_SOURCE" CHAR(2 BYTE), 
	"NOTE" VARCHAR2(200 BYTE), 
	"ALLOW_UPDATE" CHAR(1 BYTE), 
	"PARENT_NOTE_ID" NUMBER(11,0), 
	"CREATE_EMPLOYEE" NUMBER(10,0), 
	"CREATE_DATETIME" TIMESTAMP (6), 
	"UPDATE_EMPLOYEE" NUMBER(10,0), 
	"UPDATE_DATETIME" TIMESTAMP (6)
   ) PCTFREE 10 PCTUSED 40 INITRANS 1 MAXTRANS 255 NOCOMPRESS LOGGING
  STORAGE(INITIAL 65536 NEXT 1048576 MINEXTENTS 1 MAXEXTENTS 2147483645
  PCTINCREASE 0 FREELISTS 1 FREELIST GROUPS 1 BUFFER_POOL DEFAULT)
  TABLESPACE "BNDNEW_DATA" ;
