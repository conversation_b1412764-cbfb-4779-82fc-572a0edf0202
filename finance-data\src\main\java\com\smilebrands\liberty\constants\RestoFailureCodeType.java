package com.smilebrands.liberty.constants;

/*
* <PERSON><PERSON> - Smile Brands Inc. 7/30/12
*
*/
public enum RestoFailureCodeType {

    CF("Cracked Fill") {
        @Override
        public String getDescription() {
            return "Cracked Filling";
        }
    },
    M("Open Margin") {
        @Override
        public String getDescription() {
            return "Open Margin";
        }
    },
    OHM("Overhung Margin") {
        @Override
        public String getDescription() {
            return "Overhung Margin";
        }
    },
    LM("Leaky Margin") {
        @Override
        public String getDescription() {
            return "Leaky Margin";
        }
    },
    DK("Decay") {
        @Override
        public String getDescription() {
            return "Decay";
        }
    },
    LF("Leaky Fill") {
        @Override
        public String getDescription() {
            return "Leaky Filling";
        }
    },
    BF("Broken Fill") {
        @Override
        public String getDescription() {
            return "Broken Filling";
        }
    },
    REDK("Recurrent Decay") {
        @Override
        public String getDescription() {
            return "Recurrent Decay";
        }
    };

    private final String strValue;

    private RestoFailureCodeType(final String strValue) {
        this.strValue = strValue;
    }

    public String getDescription() {
        return "";
    }

    @Override
    public String toString() {
        return this.strValue;
    }
}