--------------------------------------------------------
--  DDL for Table LABORATORY_CASE
--------------------------------------------------------

  CREATE TABLE "LIBERTY_DATA"."LABORATORY_CASE"
   (	"CASE_ID" NUMBER(15,0), 
	"CURRENT_CASE_STATUS_ID" NUMBER(15,0), 
	"FACILITY_ID" NUMBER(10,0), 
	"SERVICE_PROVIDER_ID" NUMBER(6,0), 
	"PATIENT_ID" NUMBER(10,0), 
	"LAB_ID" NUMBER(10,0), 
	"LAB_OTHER_NAME" VARCHAR2(30 BYTE), 
	"CASE_TYPE" VARCHAR2(30 BYTE), 
	"REMAKE_REASON" VARCHAR2(50 BYTE), 
	"DUE_DATE" TIMESTAMP (6), 
	"DELIVERY_SCH_EVENT_ID" NUMBER(15,0), 
	"SENT_SHIPPER" VARCHAR2(30 BYTE), 
	"SENT_TRACKING_NUMBER" VARCHAR2(50 BYTE), 
	"RECEIPT_SHIPPER" VARCHAR2(30 BYTE), 
	"RECEIPT_TRACKING_NUMBER" VARCHAR2(50 BYTE), 
	"TEETH_ASSOCIATION" VARCHAR2(500 BYTE), 
	"PATIENT_CHART_NUMBER" VARCHAR2(25 BYTE), 
	"PRESCRIPTION_DATE" DATE, 
	"SCRIPT_VERIFICATION" VARCHAR2(1 BYTE), 
	"ADDITIONAL_INSTRUCTIONS" VARCHAR2(500 BYTE), 
	"FORM_DATA" VARCHAR2(2000 BYTE), 
	"INVOICE_NUMBER" VARCHAR2(25 BYTE), 
	"INVOICE_SUBTOTAL" NUMBER(15,2), 
	"INVOICE_TAX" NUMBER(15,2), 
	"INVOICE_SHIPPING" NUMBER(15,2), 
	"INVOICE_TOTAL" NUMBER(15,2), 
	"ORIGIN_SCH_EVENT_ID" NUMBER(15,0)
   ) PCTFREE 10 PCTUSED 40 INITRANS 1 MAXTRANS 255 NOCOMPRESS LOGGING
  STORAGE(INITIAL 65536 NEXT 1048576 MINEXTENTS 1 MAXEXTENTS **********
  PCTINCREASE 0 FREELISTS 1 FREELIST GROUPS 1 BUFFER_POOL DEFAULT)
  TABLESPACE "BNDNEW_DATA" ;
