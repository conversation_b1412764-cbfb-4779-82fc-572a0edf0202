--------------------------------------------------------
--  DDL for Table PATIENT_PAY_ADJ_LEDGER
--------------------------------------------------------

  CREATE TABLE "LIBERTY_DATA"."PATIENT_PAY_ADJ_LEDGER"
   (	"TRANSACTION_ID" NUMBER(11,0), 
	"BATCH_DETAIL_ID" NUMBER(11,0), 
	"PATIENT_ID" NUMBER(10,0), 
	"FACILITY_ID" NUMBER(10,0), 
	"PAY_ADJ_CODE" NUMBER(7,0), 
	"SERVICE_PROVIDER_ID" NUMBER(10,0), 
	"RELATED_TRANSACTION" NUMBER(11,0), 
	"CAP_REVENUE_ID" NUMBER(11,0), 
	"ARRANGEMENT_ID" NUMBER(10,0), 
	"ARRANGEMENT_CORELATION_ID" CHAR(10 BYTE), 
	"FUNDING_SOURCE_TYPE" VARCHAR2(20 BYTE), 
	"FUNDING_FREQUENCY_TYPE" VARCHAR2(20 BYTE), 
	"UNAPPLIED_CASH_STATUS" CHAR(1 BYTE), 
	"DATE_POSTED" DATE, 
	"REF_DATE_OF_SERVICE" DATE, 
	"PATIENT_AMOUNT" NUMBER(9,2), 
	"INSURANCE_PRIMARY" NUMBER(9,2), 
	"INSURANCE_SECONDARY" NUMBER(9,2), 
	"STATISTIC_AMOUNT" NUMBER(9,2), 
	"TRANSACTION_STATUS" CHAR(1 BYTE), 
	"QSI_CLINIC" NUMBER(10,0), 
	"TR_ID" NUMBER(10,0), 
	"IS_CONVERSION" CHAR(1 BYTE), 
	"GL_AUDIT_CODE" NUMBER(11,0), 
	"CREATE_EMPLOYEE" NUMBER(10,0), 
	"CREATE_DATETIME" TIMESTAMP (6), 
	"UPDATE_EMPLOYEE" NUMBER(10,0), 
	"UPDATE_DATETIME" TIMESTAMP (6), 
	"CLAIM_ID" NUMBER(15,0), 
	"CHECKOUT_INVOICE_ID" NUMBER(15,0)
   ) PCTFREE 10 PCTUSED 40 INITRANS 1 MAXTRANS 255 NOCOMPRESS LOGGING
  STORAGE(INITIAL 65536 NEXT 1048576 MINEXTENTS 1 MAXEXTENTS **********
  PCTINCREASE 0 FREELISTS 1 FREELIST GROUPS 1 BUFFER_POOL DEFAULT)
  TABLESPACE "BNDNEW_DATA" ;
