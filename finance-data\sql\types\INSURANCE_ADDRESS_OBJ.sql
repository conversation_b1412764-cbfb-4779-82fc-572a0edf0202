--------------------------------------------------------
--  DDL for Type INSURANCE_ADDRESS_OBJ
--------------------------------------------------------

  CREATE OR REPLACE TYPE "LIBERTY_DATA"."INSURANCE_ADDRESS_OBJ" IS OBJECT (
        RETURN_CODE                NUMBER(1,0),
        INS_CO_ADDRESS_ID          NUMBER(10),
        ADDRESS_TYPE               VARCHAR2(10),
        PLAN_OR_COMPANY            VARCHAR2(25), 
        LINE_ONE                   VARCHAR2(50),
        LINE_TWO                   VARCHAR2(50),
        IN_CARE_OF                 VARCHAR2(50),
        CITY                       VARCHAR2(35),
        ST<PERSON><PERSON>                      CHAR(2),
        ZIPCODE                    VARCHAR2(5),
        ZIPCODE_4                  VARCHAR2(4),
        ADDRESS_DESCRIPTION        VARCHAR2(75),
        EFFECTIVE_DATE             DATE,
        INACTIVE_DATE              DATE, 
        CREATE_EMPLOYEE            NUMBER(10),
        CREATE_DATETIME            TIMESTAMP(6),
        UPDATE_EMPLOYEE            NUMBER(10),
        UPDATE_DATETIME            TIMESTAMP,
        ERR_NUM                    NUMBER,
        ERR_MSG                    VARCHAR2(512))

/
