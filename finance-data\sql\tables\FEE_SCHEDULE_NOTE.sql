--------------------------------------------------------
--  DDL for Table FEE_SCHEDULE_NOTE
--------------------------------------------------------

  CREATE TABLE "LIBERTY_DATA"."FEE_SCHEDULE_NOTE"
   (	"NOTE_ID" NUMBER(15,0), 
	"CHILD_NOTE_ID" NUMBER(15,0), 
	"PARENT_NOTE_ID" NUMBER(15,0), 
	"NOTE_SOURCE" CHAR(2 BYTE), 
	"NOTE_REFERENCE_ID" NUMBER(10,0), 
	"SHOW_ON_PLAN_SCHED" CHAR(1 BYTE), 
	"NOTE" VARCHAR2(2000 BYTE), 
	"CREATE_EMPLOYEE" NUMBER(10,0), 
	"CREATE_DATETIME" TIMESTAMP (6), 
	"UPDATE_EMPLOYEE" NUMBER(10,0), 
	"UPDATE_DATETIME" TIMESTAMP (6)
   ) PCTFREE 10 PCTUSED 40 INITRANS 1 MAXTRANS 255 NOCOMPRESS LOGGING
  STORAGE(INITIAL 65536 NEXT 1048576 MINEXTENTS 1 MAXEXTENTS 2147483645
  PCTINCREASE 0 FREELISTS 1 FREELIST GROUPS 1 BUFFER_POOL DEFAULT)
  TABLESPACE "BNDNEW_DATA" ;
