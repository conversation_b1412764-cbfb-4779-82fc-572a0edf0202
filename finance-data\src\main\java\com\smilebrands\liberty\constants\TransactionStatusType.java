package com.smilebrands.liberty.constants;

/**
 * Created with IntelliJ IDEA.
 * User: danta
 * Date: 12/28/12
 * Time: 5:17 PM
 * To change this template use File | Settings | File Templates.
 */
public enum TransactionStatusType {

    A("Active"),
    I("In Process"),
    D("Deleted"),
    P("Posted");


    private final String strValue;

    private TransactionStatusType(final String strValue) {
        this.strValue = strValue;
    }

    public String getDescription() {
        return "";
    }

    @Override
    public String toString() {
        return this.strValue;
    }
}
