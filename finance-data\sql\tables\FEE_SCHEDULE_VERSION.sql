--------------------------------------------------------
--  DDL for Table FEE_SCHEDULE_VERSION
--------------------------------------------------------

  CREATE TABLE "LIBERTY_DATA"."FEE_SCHEDULE_VERSION"
   (	"FEE_SCHEDULE_ID" NUMBER(10,0), 
	"FEE_SCHEDULE" NUMBER(10,0), 
	"PARENT_FEE_SCHED_ID" NUMBER(10,0), 
	"CHILD_FEE_SCHED_ID" NUMBER(10,0), 
	"VERSION_STATUS" CHAR(1 BYTE), 
	"EFFECTIVE_DATE" DATE, 
	"INACTIVE_DATE" DATE, 
	"COVERED_ZIPCODES" NUMBER(5,0), 
	"CREATE_EMPLOYEE" NUMBER(10,0), 
	"CREATE_DATETIME" TIMESTAMP (6), 
	"UPDATE_EMPLOYEE" NUMBER(10,0), 
	"UPDATE_DATETIME" TIMESTAMP (6)
   ) PCTFREE 10 PCTUSED 40 INITRANS 1 MAXTRANS 255 NOCOMPRESS LOGGING
  STORAGE(INITIAL 65536 NEXT 1048576 MINEXTENTS 1 MAXEXTENTS 2147483645
  PCTINCREASE 0 FREELISTS 1 FREELIST GROUPS 1 BUFFER_POOL DEFAULT)
  TABLESPACE "BNDNEW_DATA" ;
