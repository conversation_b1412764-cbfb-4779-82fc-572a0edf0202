package com.smilebrands.liberty.document.model;

import com.smilebrands.liberty.model.BaseObject;
import org.springframework.data.annotation.Id;
import org.springframework.data.mongodb.core.mapping.Document;

import java.util.Date;
import java.util.HashSet;
import java.util.Set;

/**
 * User: <PERSON><PERSON>
 * Date: 9/12/13
 *
 * Simple Value Object to collect details about a Controller method request.
 */
@Document
public class ControllerRequest extends BaseObject {

    @Id
    private Long controllerRequestId;

    private String applicationName;
    private String  methodName;
    private Date    startDateTime;
    private Date    endDateTime;
    private Double  duration;
    private String  hostIP;
    private String  requestIP;
    private Integer requestEmployeeNumber;
    private Integer responseObjectSize;
    private Set<ControllerRequestArgument> arguments = new HashSet<ControllerRequestArgument>();

    public Long getControllerRequestId() {
        return controllerRequestId;
    }

    public void setControllerRequestId(Long controllerRequestId) {
        this.controllerRequestId = controllerRequestId;
    }

    public Integer getResponseObjectSize() {
        return responseObjectSize;
    }

    public void setResponseObjectSize(Integer responseObjectSize) {
        this.responseObjectSize = responseObjectSize;
    }

    public Set<ControllerRequestArgument> getArguments() {
        return arguments;
    }

    public void setArguments(Set<ControllerRequestArgument> arguments) {
        this.arguments = arguments;
    }

    public String getApplicationName() {
        return applicationName;
    }

    public void setApplicationName(String applicationName) {
        this.applicationName = applicationName;
    }

    public String getMethodName() {
        return methodName;
    }

    public void setMethodName(String methodName) {
        this.methodName = methodName;
    }

    public Date getStartDateTime() {
        return startDateTime;
    }

    public void setStartDateTime(Date startDateTime) {
        this.startDateTime = startDateTime;
    }

    public Date getEndDateTime() {
        return endDateTime;
    }

    public void setEndDateTime(Date endDateTime) {
        this.endDateTime = endDateTime;
    }

    public Double getDuration() {
        return duration;
    }

    public void setDuration(Double duration) {
        this.duration = duration;
    }

    public String getHostIP() {
        return hostIP;
    }

    public void setHostIP(String hostIP) {
        this.hostIP = hostIP;
    }

    public String getRequestIP() {
        return requestIP;
    }

    public void setRequestIP(String requestIP) {
        this.requestIP = requestIP;
    }

    public Integer getRequestEmployeeNumber() {
        return requestEmployeeNumber;
    }

    public void setRequestEmployeeNumber(Integer requestEmployeeNumber) {
        this.requestEmployeeNumber = requestEmployeeNumber;
    }

    public String toString() {
        return "Request method [" + this.methodName + "] with and argument count [" + arguments.size()
                + "] by [" + this.requestEmployeeNumber + "] from [" + this.requestIP + "] took [" + this.duration
                + "] seconds to process and resulted in a response of [" + this.responseObjectSize + "] object size.";
    }

    public Long generateId(){
        int hashValue = 0;
        hashValue += this.getMethodName() != null ? this.getMethodName().hashCode() : 0;
        hashValue += this.getRequestEmployeeNumber() != null ? this.getRequestEmployeeNumber().hashCode() : 0;
        hashValue += this.getRequestIP() != null ? this.getRequestIP().hashCode() : 0;
        hashValue += this.getHostIP() != null ? this.getHostIP().hashCode() : 0;
        hashValue += this.getStartDateTime() != null ? this.getStartDateTime().hashCode() : 0;
        hashValue += this.getEndDateTime() != null ? this.getEndDateTime().hashCode() : 0;
        hashValue += this.getResponseObjectSize() != null ? this.getResponseObjectSize().hashCode() : 0;
        return new Long(hashValue);
    }
}
