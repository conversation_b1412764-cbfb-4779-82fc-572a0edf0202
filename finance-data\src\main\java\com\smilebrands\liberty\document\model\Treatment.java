package com.smilebrands.liberty.document.model;

import com.smilebrands.liberty.model.BaseObject;
import org.springframework.data.annotation.Id;
import org.springframework.data.mongodb.core.mapping.Document;

import java.math.BigDecimal;

/**
 * User: <PERSON><PERSON>
 * Date: 8/21/13
 */
@Document
public class Treatment extends BaseObject {

    @Id
    private Long id;
    private String name;
    private Long productServiceCode;
    private String status;
    private BigDecimal ucrValue;
    private BigDecimal primaryInsuranceValue;
    private BigDecimal secondaryInsuranceValue;
    private BigDecimal patientValue;

    public Long getId() {
        return id;
    }

    public void setId(Long id) {
        this.id = id;
    }

    public String getName() {
        return name;
    }

    public void setName(String name) {
        this.name = name;
    }

    public Long getProductServiceCode() {
        return productServiceCode;
    }

    public void setProductServiceCode(Long productServiceCode) {
        this.productServiceCode = productServiceCode;
    }

    public String getStatus() {
        return status;
    }

    public void setStatus(String status) {
        this.status = status;
    }

    public BigDecimal getUcrValue() {
        return ucrValue;
    }

    public void setUcrValue(BigDecimal ucrValue) {
        this.ucrValue = ucrValue;
    }

    public BigDecimal getPrimaryInsuranceValue() {
        return primaryInsuranceValue;
    }

    public void setPrimaryInsuranceValue(BigDecimal primaryInsuranceValue) {
        this.primaryInsuranceValue = primaryInsuranceValue;
    }

    public BigDecimal getSecondaryInsuranceValue() {
        return secondaryInsuranceValue;
    }

    public void setSecondaryInsuranceValue(BigDecimal secondaryInsuranceValue) {
        this.secondaryInsuranceValue = secondaryInsuranceValue;
    }

    public BigDecimal getPatientValue() {
        return patientValue;
    }

    public void setPatientValue(BigDecimal patientValue) {
        this.patientValue = patientValue;
    }

    @Override
    public boolean equals (Object anotherObject) {
        boolean isEqual = false;

        if (anotherObject instanceof com.smilebrands.liberty.document.model.Treatment) {
            isEqual = (this.getId().equals(((Treatment) anotherObject).getId()));
        }

        return isEqual;
    }
}
