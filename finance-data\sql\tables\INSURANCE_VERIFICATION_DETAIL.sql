--------------------------------------------------------
--  DDL for Table INSURANCE_VERIFICATION_DETAIL
--------------------------------------------------------

  CREATE TABLE "LIBERTY_DATA"."INSURANCE_VERIFICATION_DETAIL"
   (	"VERIFICATION_DETAIL_ID" NUMBER(10,0), 
	"VERIFICATION_ID" NUMBER(10,0), 
	"MEMBER_LAST_NAME" VARCHAR2(30 BYTE), 
	"MEMBER_FIRST_NAME" VARCHAR2(30 BYTE), 
	"MEMBER_GENDER" CHAR(1 BYTE), 
	"MEMBER_DATE_OF_BIRTH" TIMESTAMP (6), 
	"MEMBER_RELATIONSHIP" VARCHAR2(30 BYTE), 
	"ELIGIBILITY_EFFECTIVE" TIMESTAMP (6), 
	"ELIGIBILITY_EXPIRATION" TIMESTAMP (6), 
	"DEDUCTIBLE_AMOUNT" NUMBER(10,2) DEFAULT 0, 
	"REMAINING_DEDUCTIBLE" NUMBER(10,2) DEFAULT 0
   ) PCTFREE 10 PCTUSED 40 INITRANS 1 MAXTRANS 255 NOCOMPRESS LOGGING
  STORAGE(INITIAL 65536 NEXT 1048576 MINEXTENTS 1 MAXEXTENTS 2147483645
  PCTINCREASE 0 FREELISTS 1 FREELIST GROUPS 1 BUFFER_POOL DEFAULT)
  TABLESPACE "BNDNEW_DATA" ;
