--------------------------------------------------------
--  DDL for Table VISIT_EVENT
--------------------------------------------------------

  CREATE TABLE "LIBERTY_DATA"."VISIT_EVENT"
   (	"VISIT_EVENT_ID" NUMBER(10,0), 
	"PATIENT_ID" NUMBER(10,0), 
	"FACILITY_ID" NUMBER(10,0), 
	"SCH_EVENT_ID" NUMBER(10,0), 
	"COMPUTER_LOCATION_ID" NUMBER(10,0), 
	"STATUS" VARCHAR2(30 BYTE), 
	"CREATE_DATETIME" TIMESTAMP (6) DEFAULT SYSDATE, 
	"CREATE_EMPLOYEE_NUMBER" NUMBER(10,0), 
	"ELAPSED_TIME" NUMBER(12,0)
   ) PCTFREE 10 PCTUSED 40 INITRANS 1 MAXTRANS 255 NOCOMPRESS LOGGING
  STORAGE(INITIAL 65536 NEXT 1048576 MINEXTENTS 1 MAXEXTENTS **********
  PCTINCREASE 0 FREELISTS 1 FREELIST GROUPS 1 BUFFER_POOL DEFAULT)
  TABLESPACE "BNDNEW_DATA" ;
