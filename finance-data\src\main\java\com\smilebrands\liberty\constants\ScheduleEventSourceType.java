package com.smilebrands.liberty.constants;

/**
 * Created with IntelliJ IDEA.
 * User: danta
 * Date: 12/7/12
 * Time: 11:23 AM
 * To change this template use File | Settings | File Templates.
 */
public enum ScheduleEventSourceType {

    FIRST_VISIT_REPORT("1st Visit Report") {
        @Override
        public String getDescription() {
            return "1st Visit Report";
        }
    },
    CO_UNION("CO Union") {
        @Override
        public String getDescription() {
            return "CO Union";
        }
    },
    CPN_BUND("Cpn Bund") {
        @Override
        public String getDescription() {
            return "Coupon Bundle";
        }
    },
    FAMILY_FRIEND("Family Friend") {
        @Override
        public String getDescription() {
            return "Family Friend recommendation";
        }
    },
    FLYER("Flyer") {
        @Override
        public String getDescription() {
            return "Flyer";
        }
    },
    FREE_BUNDLE("Free Bundle") {
        @Override
        public String getDescription() {
            return "Free bundle";
        }
    },
    INSURANCE_PLAN("Insurance Plan") {
        @Override
        public String getDescription() {
            return "Insurance plan recommendation";
        }
    },
    MISSED_APPT("Missed Appt") {
        @Override
        public String getDescription() {
            return "Missed appointment (Callback)";
        }
    },
    NEWSPAPER("Newspaper") {
        @Override
        public String getDescription() {
            return "Newspaper print ad";
        }
    },
    NO_SHOW("No Show") {
        @Override
        public String getDescription() {
            return "No show callback";
        }
    },
    NP_TRANSFER("NP Transfer") {
        @Override
        public String getDescription() {
            return "New patient transfer";
        }
    },
    OFFICE_SIGN("Office Sign") {
        @Override
        public String getDescription() {
            return "Office signage";
        }
    },
    ORTHO_EMAIL("Ortho Email") {
        @Override
        public String getDescription() {
            return "Ortho email";
        }
    },
    OTHER("Other") {
        @Override
        public String getDescription() {
            return "Other sources";
        }
    },
    POST_CARD("Post Card") {
        @Override
        public String getDescription() {
            return "Post card mailer";
        }
    },
    PROSPECT_CALL("Prospect Call (Callback)") {
        @Override
        public String getDescription() {
            return "Prospect call (callback)";
        }
    },
    RADIO("Radio") {
        @Override
        public String getDescription() {
            return "Radio advertisement";
        }
    },
    REFER_A_FRIEND("Refer A Friend") {
        @Override
        public String getDescription() {
            return "Friend referral";
        }
    },
    SEIU("SEIU") {
        @Override
        public String getDescription() {
            return "SEIU?";
        }
    },
    TV("TV") {
        @Override
        public String getDescription() {
            return "TV";
        }
    },
    WEB("WEB") {
        @Override
        public String getDescription() {
            return "World wide web";
        }
    },
    YELLOW_PAGES("Yellow Pages") {
        @Override
        public String getDescription() {
            return "Yellow pages print media";
        }
    },
    BLANK("Blank") {
        @Override
        public String getDescription() {
            return "Nothing?";
        }
    };

    private final String strValue;

    private ScheduleEventSourceType(final String strValue) {
        this.strValue = strValue;
    }

    public String getDescription() {
        return "";
    }

    @Override
    public String toString() {
        return this.strValue;
    }
}
