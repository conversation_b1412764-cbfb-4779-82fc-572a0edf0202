package com.smilebrands.liberty.constants;

/**
 *
 * <AUTHOR> Bright Now! Dental, 2009
 */
public enum RefundReasonType {

    UNSATISFIED_TREATMENT("Unsatisfied Treatment") {

        @Override
        public boolean showDates() {
            return true;
        }
    },
    PATIENT_CREDIT("Patient Credit") {

        @Override
        public boolean showDates() {
            return false;
        }
    },
    OVER_PAYMENT_PRIMARY("Over Payment Primary") {

        @Override
        public boolean showDates() {
            return true;
        }
    },
    OVER_PAYMENT_SECONDARY("Over Payment Secondary") {

        @Override
        public boolean showDates() {
            return true;
        }
    },
    INCOMPLETE_FAILED("Incomplete / Failed Treatment") {

        @Override
        public boolean showDates() {
            return true;
        }
    },
    OTHER("Other") {

        @Override
        public boolean showDates() {
            return true;
        }
    };
    private String strValue;

    private RefundReasonType(final String strValue) {
        this.strValue = strValue;
    }

    public boolean showDates() {
        return false;
    }

    @Override
    public String toString() {
        return this.strValue;
    }
}