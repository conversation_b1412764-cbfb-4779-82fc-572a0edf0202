package com.smilebrands.liberty.constants;

/**
 * Created with IntelliJ IDEA.
 * User: danta
 * Date: 12/28/12
 * Time: 4:46 PM
 * To change this template use File | Settings | File Templates.
 */
public enum PostingType {
    CHECK("Check"),
    BULK_CHECK("Bulk Check"),
    DIRECT("Direct Deposit (Check)"),
    DIRECT_CAP("Direct Deposit (Cap. Check)"),
    EFT("EFT"),
    ADJ("Adjustment"),
    CAP("Cap Check"),
    CREDIT("Credit Card"),
    CONV("Conversion"),
    OTHER("Other"),
    FIN("Finance Funding"),
    CASH("Cash"),
    BULK_POST("Bulk Post");

    private final String strValue;

    private PostingType(final String strValue) {
        this.strValue = strValue;
    }

    public String getDescription() {
        return "";
    }

    @Override
    public String toString() {
        return this.strValue;
    }
}
