package com.smilebrands.liberty.claim.model;

import javax.persistence.DiscriminatorValue;
import javax.persistence.Entity;

/*
* <PERSON><PERSON> - Smile Brands Inc. 10/23/12
*
*/
@SuppressWarnings("serial")
@Entity
@DiscriminatorValue("BILLING")
public class ClaimBillingProvider extends ClaimProvider {
  private transient String type = "BILLING";
  
  /**
   * @return the type
   */
  public String getType() {
    return type;
  }

  /**
   * @param type the type to set
   */
  public void setType(String type) {
    this.type = type;
  }
}
