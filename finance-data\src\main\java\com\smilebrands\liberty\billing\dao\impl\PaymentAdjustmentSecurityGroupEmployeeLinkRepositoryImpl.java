package com.smilebrands.liberty.billing.dao.impl;

import com.mysema.query.jpa.JPQLQuery;
import com.mysema.query.jpa.impl.JPAQuery;
import com.smilebrands.liberty.billing.dao.PaymentAdjustmentSecurityEmployeeCustom;
import com.smilebrands.liberty.billing.model.*;
import com.smilebrands.liberty.dao.SimpleAbstractDao;

import java.util.ArrayList;
import java.util.List;

/**
 * User: <PERSON><PERSON>
 * Date: 12/27/13
 */
public class PaymentAdjustmentSecurityGroupEmployeeLinkRepositoryImpl extends SimpleAbstractDao implements PaymentAdjustmentSecurityEmployeeCustom {

    @Override
    public List<PaymentAdjustmentCode> getPaymentAdjustmentCodesByEmployeeNumber(Integer employeeNumber, Boolean activeGroupsOnly) {
        JPQLQuery queryDsl = new JPAQuery(em);
        QPaymentAdjustmentCode code = QPaymentAdjustmentCode.paymentAdjustmentCode;
        QPaymentAdjustmentSecurityMember member = QPaymentAdjustmentSecurityMember.paymentAdjustmentSecurityMember1;
        QPaymentAdjustmentSecurityGroup group = QPaymentAdjustmentSecurityGroup.paymentAdjustmentSecurityGroup;
        QPaymentAdjustmentSecurityGroupEmployeeLink link = QPaymentAdjustmentSecurityGroupEmployeeLink.paymentAdjustmentSecurityGroupEmployeeLink;

        // -- follow group links to codes by employee number
        List<Integer> groups = queryDsl.from(link)
                               .where(link.employeeNumber.eq(employeeNumber))
                               .where(link.inactivateDateTime.isNull())
                               .list(link.securityGroupId);

        logger.debug("Found " + groups.size() + " security groups for employee number [" + employeeNumber + "]");
        if (groups.size() > 0) {
            List<PaymentAdjustmentCode> codes;
            if (activeGroupsOnly) {
                 codes = queryDsl.from(member)
                        .leftJoin(member.payAdjCode.paymentAdjustmentGLDistributionCodes).fetch()
                        .where(member.securityGroupId.in(groups))
                        .where(member.inactivateDateTime.isNull())
                        .where(member.paymentAdjustmentSecurityGroup.active.isTrue())
                        .listDistinct(member.payAdjCode);

            } else {
                codes = queryDsl.from(member)
                        .leftJoin(member.payAdjCode.paymentAdjustmentGLDistributionCodes).fetch()
                        .where(member.securityGroupId.in(groups))
                        .where(member.inactivateDateTime.isNull())
                        .listDistinct(member.payAdjCode);
            }

            logger.debug("Groups [" + groups.size() + "] produced Payment Adjustment Codes [" + codes.size() + "].");
            return codes;
        }

        return null;
    }
}
