package com.smilebrands.liberty.claim.model;

import com.smilebrands.liberty.claim.model.view.ClaimNote;
import com.smilebrands.liberty.constants.ClaimRoleType;
import com.smilebrands.liberty.constants.ClaimStatusType;
import com.smilebrands.liberty.model.BaseObject;
import com.smilebrands.liberty.utility.JsonDateDeSerializer;
import com.smilebrands.liberty.utility.JsonDateSerializer;
import org.codehaus.jackson.map.annotate.JsonDeserialize;
import org.codehaus.jackson.map.annotate.JsonSerialize;

import javax.persistence.*;
import java.math.BigDecimal;
import java.math.RoundingMode;
import java.util.Date;
import java.util.HashSet;
import java.util.Set;

/**
 * <AUTHOR> Smile Brands Inc., 2011
 */
@Entity
@NamedQueries({
        @NamedQuery(name = "Claim.findAllById",
                query = "select c from Claim c " +
                        "  join fetch c.claimControl " +
                        "  join fetch c.currentClaimStatus " +
                        "  left join fetch c.primaryInsurance " +
                        "  join fetch c.treatingProvider " +
                        "  join fetch c.billingProvider " +
                        "  left join fetch c.serviceLines " +
                        "  left join fetch c.secondaryInsurance " +
                        "  left join fetch c.transmitHistory " +
                        "  left join fetch c.claimNotes " +
                        "  left join fetch c.claimMessages cm" +
                        "  left join fetch cm.acknowledgementEmployee " +
                        "  left join fetch c.attachments ca " +
                        "  left join fetch ca.completionEmployee " +
                        " where c.claimId = ?"),
        @NamedQuery(name = "Claim.findAllAcceptedByPatientId",
                query = "select c from Claim c " +
                        "  join fetch c.claimControl cc " +
                        "  join fetch c.currentClaimStatus ccs " +
                        "  left join fetch c.primaryInsurance " +
                        "  join fetch c.treatingProvider " +
                        "  join fetch c.billingProvider " +
                        "  left join fetch c.serviceLines " +
                        "  left join fetch c.secondaryInsurance " +
                        "  left join fetch c.transmitHistory " +
                        "  left join fetch c.claimNotes " +
                        "  left join fetch c.claimMessages cm " +
                        "  left join fetch cm.acknowledgementEmployee " +
                        "  left join fetch c.attachments ca " +
                        "  left join fetch ca.completionEmployee " +
                        " where cc.libertyPatientId = ? " +
                        "   and c.batchDetailId is null " +
                        "   and ccs.transmit = 1")
})
@Table(name = "CLAIM")
@SuppressWarnings("serial")
public class Claim extends BaseObject {

    @Id
    @Column(name = "CLAIM_ID")
    @GeneratedValue(generator = "ClaimSeq")
    @SequenceGenerator(name = "ClaimSeq", sequenceName = "CLAIM_SEQ", allocationSize = 1)
    protected Long claimId;

    @Column(name = "CLAIM_CONTROL_NUMBER")
    protected String claimControlNumber;

    @Column(name = "PARTNER_CONTROL_NUMBER")
    protected String partnerControlNumber;

    @OneToOne(cascade = CascadeType.ALL)
    @JoinColumn(name = "CLAIM_CONTROL_ID", insertable = true, updatable = true)
    protected ClaimControl claimControl;

    @OneToOne(cascade = CascadeType.ALL)
    @JoinColumn(name = "PRIMARY_CLAIM_INS_ID", referencedColumnName = "CLAIM_INS_ID", insertable = true, updatable = true)
    private ClaimPrimaryInsurance primaryInsurance;

    @OneToOne(cascade = CascadeType.ALL)
    @JoinColumn(name = "SECONDARY_CLAIM_INS_ID", referencedColumnName = "CLAIM_INS_ID", insertable = true, updatable = true)
    private ClaimSecondaryInsurance secondaryInsurance;

    @OneToOne(cascade = CascadeType.ALL)
    @JoinColumn(name = "TREATING_PROVIDER_ID", referencedColumnName = "CLAIM_PROVIDER_ID", insertable = true, updatable = true)
    private ClaimTreatingProvider treatingProvider;

    @OneToOne(cascade = CascadeType.ALL)
    @JoinColumn(name = "BILLING_PROVIDER_ID", referencedColumnName = "CLAIM_PROVIDER_ID", insertable = true, updatable = true)
    private ClaimBillingProvider billingProvider;

    @OneToMany(cascade = {CascadeType.PERSIST, CascadeType.MERGE}, fetch = FetchType.LAZY)
    @JoinColumn(name = "CLAIM_ID", insertable = true, updatable = false)
    private Set<ClaimServiceLine> serviceLines = new HashSet<ClaimServiceLine>();

    @OneToMany(cascade = {CascadeType.PERSIST, CascadeType.MERGE}, fetch = FetchType.LAZY)
    @JoinColumn(name = "CLAIM_ID", insertable = true, updatable = false)
    private Set<ClaimNote> claimNotes = new HashSet<ClaimNote>();

    @OneToMany(cascade = {CascadeType.PERSIST, CascadeType.MERGE}, fetch = FetchType.LAZY)
    @JoinColumn(name = "CLAIM_ID", insertable = true, updatable = true)
    private Set<ClaimMessage> claimMessages = new HashSet<ClaimMessage>();

    @OneToMany(cascade = {CascadeType.PERSIST, CascadeType.MERGE}, fetch = FetchType.LAZY)
    @JoinColumn(name = "CLAIM_ID", insertable = true, updatable = true)
    private Set<ClaimRequiredAttachment> attachments = new HashSet<ClaimRequiredAttachment>();

    @OneToOne(cascade = {CascadeType.PERSIST})
    @JoinColumn(name = "CURRENT_CLAIM_STATUS_ID", referencedColumnName = "CLAIM_STATUS_ID", insertable = true, updatable = true)
    private ClaimStatus currentClaimStatus;

    @OneToOne(cascade = CascadeType.ALL)
    @JoinColumn(name = "CLAIM_TRANSMIT_ID", referencedColumnName = "CLAIM_TRANSMIT_ID", insertable = true, updatable = true)
    private ClaimTransmitHistory transmitHistory;

    @Column(name = "IS_TREATMENT")
    private boolean isTreatment = true;

    @Column(name = "IS_ENCOUNTER")
    private boolean isEncounter = false;

    @Column(name = "IS_PRIOR_AUTH")
    private boolean isPriorAuth = false;

    @Column(name = "CLAIM_ROLE")
    @Enumerated(EnumType.STRING)
    private ClaimRoleType claimRole = ClaimRoleType.PRIMARY;

    @Column(name = "PRIOR_AUTH_NUMBER")
    private String priorAuthNumber;

    @Column(name = "HOLD_FOR_EDIT")
    private boolean holdClaimForEdit = false;

    @Column(name = "HOLD_FOR_ATTACHMENT")
    private boolean holdClaimForAttachments = true;

    @Column(name = "REQ_ATTACHMENT_COUNT")
    private Integer requiredAttachmentCount = 0;

    @Column(name = "NEA_PAYOR_ID")
    private Long neaPayorId = -1L;

    @Column(name = "NEA_NUMBER")
    private String neaNumber;

    @Column(name = "NEA_ATTACHMENT_REF")
    private Long neaAttachmentReferenceNumber;

    @Column(name = "PATIENT_CONTROL_NUMBER")
    private String patientControlNumber;

    @Column(name = "PATIENT_FIRST_NAME")
    private String patientFirstName;

    @Column(name = "PATIENT_MIDDLE_NAME")
    private String patientMiddleName;

    @Column(name = "PATIENT_LAST_NAME")
    private String patientLastName;

    @Column(name = "GENDER")
    private String gender;

    @Temporal(TemporalType.DATE)
    @Column(name = "DATE_OF_BIRTH")
    private Date dateOfBirth;

    @Column(name = "ADDRESS_STREET")
    private String addressStreet;

    @Column(name = "ADDRESS_CITY")
    private String addressCity;

    @Column(name = "ADDRESS_STATE")
    private String addressState;

    @Column(name = "ZIP_CODE")
    private String addressZipCode;

    @Column(name = "PHONE_NUMBER")
    private Long phoneNumber;

    @Column(name = "IS_STUDENT")
    private boolean isStudent = false;

    @Column(name = "MISSING_PERMANENT_TEETH")
    private String missingPermanentTeeth;

    @Column(name = "MISSING_PRIMARY_TEETH")
    private String missingPrimaryTeeth;

    @Column(name = "PLACE_OF_TREATMENT")
    private String placeOfTreatment;

    @Column(name = "IS_ORTHODONTICS")
    private boolean isOrthodontics;

    @Column(name = "NUMBER_OF_ENCLOSURES")
    private Integer numberOfEnclosures;

    @Temporal(TemporalType.DATE)
    @Column(name = "ORTHO_PLACEMENT_DATE")
    private Date orthoPlacementDate;

    @Column(name = "ORTHO_REMAINING_TREATMENTS")
    private Integer orthoTreatmentsRemaining;

    @Column(name = "IS_ACCIDENT")
    private boolean isAccident;

    @Temporal(TemporalType.DATE)
    @Column(name = "ACCIDENT_DATE")
    private Date accidentDate;

    @Column(name = "ACCIDENT_STATE")
    private String accidentState;

    @Column(name = "RADIOGRAPH_COUNT")
    private Integer radiographCount;

    @Column(name = "IMAGES_COUNT")
    private Integer imagesCount;

    @Column(name = "MODELS_COUNT")
    private Integer modelsCount;

    @Column(name = "SERVICE_DOLLAR_TOTAL")
    private BigDecimal serviceDollarTotal;

    @Column(name = "SERVICE_LINE_TOTAL")
    private Integer serviceLineTotal;

    @Column(name = "PARTNER_FACILITY_ID")
    private String partnerFacilityId;

    @Temporal(TemporalType.TIMESTAMP)
    @Column(name = "CREATE_DATETIME")
    private Date creationDatetime = new Date();

    @Column(name = "CREATE_EMPLOYEE_NUMBER")
    private Integer createEmployeeNumber = 0;

    @Temporal(TemporalType.TIMESTAMP)
    @Column(name = "UPDATE_DATETIME")
    private Date updateDatetime;

    @Column(name = "UPDATE_EMPLOYEE_NUMBER")
    private Integer updateEmployeeNumber;

    @Column(name = "EOB_DOCUMENT_CLAIM_ID")
    private Long eobClaimId;

    @Temporal(TemporalType.TIMESTAMP)
    @Column(name = "EOB_LINK_DATETIME")
    private Date eobLinkDatetime;

    @Column(name = "BATCH_DETAIL_ID")
    private Long batchDetailId;

    @Temporal(TemporalType.TIMESTAMP)
    @Column(name = "PRIOR_AUTH_EXPIRE_DATETIME")
    private Date priorAuthExpireDatetime;

    @PrePersist
    protected void toDoBeforeInitialPersist() {
        // -- add the initial status
        ClaimStatus status = new ClaimStatus();
        status.setCreateDatetime(new Date());
        status.setClaimStatusType(ClaimStatusType.NEW);
        setCurrentClaimStatus(status);
    }

    @Override
    public boolean equals(Object o) {
        if (this == o) return true;
        if (o == null || getClass() != o.getClass()) return false;

        Claim claim = (Claim) o;

        if (claimId != null ? !claimId.equals(claim.claimId) : claim.claimId != null) return false;

        return true;
    }

    @Override
    public int hashCode() {
        return claimId != null ? claimId.hashCode() : 0;
    }

    public Long getClaimId() {
        return claimId;
    }

    public void setClaimId(Long claimId) {
        this.claimId = claimId;
    }

    public String getClaimControlNumber() {
        return claimControlNumber;
    }

    public void setClaimControlNumber(String claimControlNumber) {
        this.claimControlNumber = claimControlNumber;
    }

    public ClaimControl getClaimControl() {
        return claimControl;
    }

    public void setClaimControl(ClaimControl claimControl) {
        this.claimControl = claimControl;
    }

    public ClaimPrimaryInsurance getPrimaryInsurance() {
        return primaryInsurance;
    }

    public void setPrimaryInsurance(ClaimPrimaryInsurance primaryInsurance) {
        this.primaryInsurance = primaryInsurance;
    }

    public ClaimSecondaryInsurance getSecondaryInsurance() {
        return secondaryInsurance;
    }

    public void setSecondaryInsurance(ClaimSecondaryInsurance secondaryInsurance) {
        this.secondaryInsurance = secondaryInsurance;
    }

    public Set<ClaimServiceLine> getServiceLines() {
        return serviceLines;
    }

    public void setServiceLines(Set<ClaimServiceLine> serviceLines) {
        this.serviceLines = serviceLines;
    }

    public Set<ClaimNote> getClaimNotes() {
        return claimNotes;
    }

    public void setClaimNotes(Set<ClaimNote> claimNotes) {
        this.claimNotes = claimNotes;
    }

    public Set<ClaimMessage> getClaimMessages() {
        return claimMessages;
    }

    public void setClaimMessages(Set<ClaimMessage> claimMessages) {
        this.claimMessages = claimMessages;
    }

    public Set<ClaimRequiredAttachment> getAttachments() {
        return attachments;
    }

    public void setAttachments(Set<ClaimRequiredAttachment> attachments) {
        this.attachments = attachments;
    }

    public boolean isPriorAuth() {
        return isPriorAuth;
    }

    public void setPriorAuth(boolean priorAuth) {
        isPriorAuth = priorAuth;
    }

    public String getPriorAuthNumber() {
        return priorAuthNumber;
    }

    public void setPriorAuthNumber(String priorAuthNumber) {
        this.priorAuthNumber = priorAuthNumber;
    }

    public boolean isHoldClaimForEdit() {
        return holdClaimForEdit;
    }

    public void setHoldClaimForEdit(boolean holdClaimForEdit) {
        this.holdClaimForEdit = holdClaimForEdit;
    }

    public boolean isHoldClaimForAttachments() {
        return holdClaimForAttachments;
    }

    public void setHoldClaimForAttachments(boolean holdClaimForAttachments) {
        this.holdClaimForAttachments = holdClaimForAttachments;
    }

    public Long getNeaPayorId() {
        return neaPayorId;
    }

    public void setNeaPayorId(Long neaPayorId) {
        this.neaPayorId = neaPayorId;
    }

    public String getNeaNumber() {
        return neaNumber;
    }

    public void setNeaNumber(String neaNumber) {
        this.neaNumber = neaNumber;
    }

    public Long getNeaAttachmentReferenceNumber() {
        return neaAttachmentReferenceNumber;
    }

    public void setNeaAttachmentReferenceNumber(Long neaAttachmentReferenceNumber) {
        this.neaAttachmentReferenceNumber = neaAttachmentReferenceNumber;
    }

    public String getPatientControlNumber() {
        return patientControlNumber;
    }

    public void setPatientControlNumber(String patientControlNumber) {
        this.patientControlNumber = patientControlNumber;
    }

    public String getPatientFirstName() {
        return patientFirstName;
    }

    public void setPatientFirstName(String patientFirstName) {
        this.patientFirstName = patientFirstName;
    }

    public String getPatientMiddleName() {
        return patientMiddleName;
    }

    public void setPatientMiddleName(String patientMiddleName) {
        this.patientMiddleName = patientMiddleName;
    }

    public String getPatientLastName() {
        return patientLastName;
    }

    public void setPatientLastName(String patientLastName) {
        this.patientLastName = patientLastName;
    }

    public String getGender() {
        return gender;
    }

    public void setGender(String gender) {
        this.gender = gender;
    }

    @JsonSerialize(using = JsonDateSerializer.class)
    public Date getDateOfBirth() {
        return dateOfBirth;
    }

    @JsonDeserialize(using = JsonDateDeSerializer.class)
    public void setDateOfBirth(Date dateOfBirth) {
        this.dateOfBirth = dateOfBirth;
    }

    public String getAddressStreet() {
        return addressStreet;
    }

    public void setAddressStreet(String addressStreet) {
        this.addressStreet = addressStreet;
    }

    public String getAddressCity() {
        return addressCity;
    }

    public void setAddressCity(String addressCity) {
        this.addressCity = addressCity;
    }

    public String getAddressState() {
        return addressState;
    }

    public void setAddressState(String addressState) {
        this.addressState = addressState;
    }

    public String getAddressZipCode() {
        return addressZipCode;
    }

    public void setAddressZipCode(String addressZipCode) {
        this.addressZipCode = addressZipCode;
    }

    public Long getPhoneNumber() {
        return phoneNumber;
    }

    public void setPhoneNumber(Long phoneNumber) {
        this.phoneNumber = phoneNumber;
    }

    public boolean isStudent() {
        return isStudent;
    }

    public void setStudent(boolean student) {
        isStudent = student;
    }

    public String getMissingPermanentTeeth() {
        return missingPermanentTeeth;
    }

    public void setMissingPermanentTeeth(String missingPermanentTeeth) {
        this.missingPermanentTeeth = missingPermanentTeeth;
    }

    public String getMissingPrimaryTeeth() {
        return missingPrimaryTeeth;
    }

    public void setMissingPrimaryTeeth(String missingPrimaryTeeth) {
        this.missingPrimaryTeeth = missingPrimaryTeeth;
    }

    public String getPlaceOfTreatment() {
        return placeOfTreatment;
    }

    public void setPlaceOfTreatment(String placeOfTreatment) {
        this.placeOfTreatment = placeOfTreatment;
    }

    public boolean isOrthodontics() {
        return isOrthodontics;
    }

    public void setOrthodontics(boolean orthodontics) {
        isOrthodontics = orthodontics;
    }

    public Integer getNumberOfEnclosures() {
        return numberOfEnclosures;
    }

    public void setNumberOfEnclosures(Integer numberOfEnclosures) {
        this.numberOfEnclosures = numberOfEnclosures;
    }

    @JsonSerialize(using = JsonDateSerializer.class)
    public Date getOrthoPlacementDate() {
        return orthoPlacementDate;
    }

    @JsonDeserialize(using = JsonDateDeSerializer.class)
    public void setOrthoPlacementDate(Date orthoPlacementDate) {
        this.orthoPlacementDate = orthoPlacementDate;
    }

    public Integer getOrthoTreatmentsRemaining() {
        return orthoTreatmentsRemaining;
    }

    public void setOrthoTreatmentsRemaining(Integer orthoTreatmentsRemaining) {
        this.orthoTreatmentsRemaining = orthoTreatmentsRemaining;
    }

    public boolean isAccident() {
        return isAccident;
    }

    public void setAccident(boolean accident) {
        isAccident = accident;
    }

    @JsonSerialize(using = JsonDateSerializer.class)
    public Date getAccidentDate() {
        return accidentDate;
    }

    @JsonDeserialize(using = JsonDateDeSerializer.class)
    public void setAccidentDate(Date accidentDate) {
        this.accidentDate = accidentDate;
    }

    public Integer getRadiographCount() {
        return radiographCount;
    }

    public void setRadiographCount(Integer radiographCount) {
        this.radiographCount = radiographCount;
    }

    public Integer getImagesCount() {
        return imagesCount;
    }

    public void setImagesCount(Integer imagesCount) {
        this.imagesCount = imagesCount;
    }

    public Integer getModelsCount() {
        return modelsCount;
    }

    public void setModelsCount(Integer modelsCount) {
        this.modelsCount = modelsCount;
    }

    public BigDecimal getServiceDollarTotal() {
        return serviceDollarTotal;
    }

    public void setServiceDollarTotal(BigDecimal serviceDollarTotal) {
        this.serviceDollarTotal = serviceDollarTotal;
    }

    public Integer getServiceLineTotal() {
        return serviceLineTotal;
    }

    public void setServiceLineTotal(Integer serviceLineTotal) {
        this.serviceLineTotal = serviceLineTotal;
    }

    @JsonSerialize(using = JsonDateSerializer.class)
    public Date getCreationDatetime() {
        return creationDatetime;
    }

    @JsonDeserialize(using = JsonDateDeSerializer.class)
    public void setCreationDatetime(Date creationDatetime) {
        this.creationDatetime = creationDatetime;
    }

    public Integer getCreateEmployeeNumber() {
        return createEmployeeNumber;
    }

    public void setCreateEmployeeNumber(Integer createEmployeeNumber) {
        this.createEmployeeNumber = createEmployeeNumber;
    }

    @JsonSerialize(using = JsonDateSerializer.class)
    public Date getUpdateDatetime() {
        return updateDatetime;
    }

    @JsonDeserialize(using = JsonDateDeSerializer.class)
    public void setUpdateDatetime(Date updateDatetime) {
        this.updateDatetime = updateDatetime;
    }

    public Integer getUpdateEmployeeNumber() {
        return updateEmployeeNumber;
    }

    public void setUpdateEmployeeNumber(Integer updateEmployeeNumber) {
        this.updateEmployeeNumber = updateEmployeeNumber;
    }

    public Long getEobClaimId() {
        return eobClaimId;
    }

    public void setEobClaimId(Long eobClaimId) {
        this.eobClaimId = eobClaimId;
    }

    @JsonSerialize(using = JsonDateSerializer.class)
    public Date getEobLinkDatetime() {
        return eobLinkDatetime;
    }

    @JsonDeserialize(using = JsonDateDeSerializer.class)
    public void setEobLinkDatetime(Date eobLinkDatetime) {
        this.eobLinkDatetime = eobLinkDatetime;
    }

    public ClaimTransmitHistory getTransmitHistory() {
        return transmitHistory;
    }

    public void setTransmitHistory(ClaimTransmitHistory transmitHistory) {
        this.transmitHistory = transmitHistory;
    }

    public String getPartnerFacilityId() {
        return partnerFacilityId;
    }

    public void setPartnerFacilityId(String partnerFacilityId) {
        this.partnerFacilityId = partnerFacilityId;
    }

    public ClaimStatus getCurrentClaimStatus() {
        return currentClaimStatus;
    }

    public void setCurrentClaimStatus(ClaimStatus currentClaimStatus) {
        this.currentClaimStatus = currentClaimStatus;
    }

    public boolean isTreatment() {
        return isTreatment;
    }

    public void setTreatment(boolean treatment) {
        isTreatment = treatment;
    }

    public boolean isEncounter() {
        return isEncounter;
    }

    public void setEncounter(boolean encounter) {
        isEncounter = encounter;
    }

    public String getAccidentState() {
        return accidentState;
    }

    public void setAccidentState(String accidentState) {
        this.accidentState = accidentState;
    }

    public ClaimTreatingProvider getTreatingProvider() {
        return treatingProvider;
    }

    public void setTreatingProvider(ClaimTreatingProvider treatingProvider) {
        this.treatingProvider = treatingProvider;
    }

    public ClaimBillingProvider getBillingProvider() {
        return billingProvider;
    }

    public void setBillingProvider(ClaimBillingProvider billingProvider) {
        this.billingProvider = billingProvider;
    }

    public String getPartnerControlNumber() {
        return partnerControlNumber;
    }

    public void setPartnerControlNumber(String partnerControlNumber) {
        this.partnerControlNumber = partnerControlNumber;
    }

    public Integer getRequiredAttachmentCount() {
        return requiredAttachmentCount;
    }

    public void setRequiredAttachmentCount(Integer requiredAttachmentCount) {
        this.requiredAttachmentCount = requiredAttachmentCount;
    }

    public Long getBatchDetailId() {
        return batchDetailId;
    }

    public void setBatchDetailId(Long batchDetailId) {
        this.batchDetailId = batchDetailId;
    }

    @JsonSerialize(using = JsonDateSerializer.class)
    public Date getPriorAuthExpireDatetime() {
        return priorAuthExpireDatetime;
    }

    @JsonDeserialize(using = JsonDateDeSerializer.class)
    public void setPriorAuthExpireDatetime(Date priorAuthExpireDatetime) {
        this.priorAuthExpireDatetime = priorAuthExpireDatetime;
    }

    /**
     * @return the infoSign
     */
    @Transient
    public boolean isInfoSign() {
        return true;
    }

    /**
     * @return the benefitSign
     */
    @Transient
    public boolean isBenefitSign() {
        return true;
    }

    /**
     * @return the patientMemberType
     */
    @Transient
    public String getPatientMemberType() {
        return "Patient";
    }

    /**
     * @return the claimType
     */
    @Transient
    public String getClaimType() {
        if (isEncounter()) {
            return "Encounter";
        }
        if (isPriorAuth()) {
            return "Prior Authorization";
        }
        return "Claim";
    }

    public Long getInsurancePlanVersionByRole(){
        if(this.claimRole == ClaimRoleType.SECONDARY && this.secondaryInsurance != null){
            return this.secondaryInsurance.getInsurancePlanId();
        }else if(this.primaryInsurance != null){
            return this.primaryInsurance.getInsurancePlanId();
        }else{
            return 0L;
        }
    }

    public Integer getInsurancePlanNumberByRole(){
        try{
            if(this.claimRole == ClaimRoleType.SECONDARY && this.secondaryInsurance != null){
                return new Integer(this.secondaryInsurance.getInsurancePlan());
            }else if(this.primaryInsurance != null){
                return new Integer(this.primaryInsurance.getInsurancePlan());
            }else{
                return 0;
            }
        }catch (Exception ex){  //MOSTLY CATCH FOR NUMBERFORMATEXCEPTION
            return 0;
        }

    }

    public ClaimInsurance getInsuranceByRole(){
        if(this.claimRole == ClaimRoleType.SECONDARY){
            return this.secondaryInsurance;
        }else {
            return this.primaryInsurance;
        }
    }

    public String getPatientFullName(boolean lastFirst){
        String result = lastFirst ? this.getPatientLastName() : this.getPatientFirstName();
        if(result != null){
            result = result.trim();
            if(lastFirst){
                result += ",";
            }
        }
        String middleName = this.getPatientMiddleName() != null && this.getPatientMiddleName().trim().length() > 0 ? this.getPatientMiddleName().trim() : null;
        if(lastFirst){
            result += (this.getPatientFirstName() != null ? (" " + this.getPatientFirstName().trim()) : "");
            if(middleName != null){
                result += " " + middleName.charAt(0);
            }
        }else{
            if(middleName != null){
                result += " " + middleName.charAt(0);
            }
            result += (this.getPatientLastName() != null ? (" " + this.getPatientLastName()) : "");
        }
        return result.trim();
    }
    public String getPatientCSZ(){
        String result = "";
        if(this.getAddressCity() != null && this.getAddressCity().trim().length() > 0){
            result += this.getAddressCity().trim();
        }
        if(this.getAddressState() != null && this.getAddressState().trim().length() > 0){
            result += ", " + this.getAddressState().trim();
        }
        if(this.getAddressZipCode() != null && this.getAddressZipCode().trim().length() > 0){
            result += " " + this.getAddressZipCode().trim();
        }
        return result;
    }

    public int getServiceDollarTotalBy(int type){
        if(type == 1){  //thousand
            return (int)(this.getServiceDollarTotal().doubleValue()/1000);
        }else if(type == 2){    //hundred
            return (int)this.getServiceDollarTotal().doubleValue()%1000;
        }else if(type == 3){ //decimal
            BigDecimal decimal = new BigDecimal((this.getServiceDollarTotal().doubleValue() - this.getServiceDollarTotal().intValue())*100);
            decimal = decimal.setScale(0, RoundingMode.UP);
            return decimal.intValue();
        }else{
            return 0;
        }
    }

    @Transient
    private BigDecimal totalAmount;
    public void setClaimTotalAmount(String priceOptionType){
        this.totalAmount = new BigDecimal(0);
        for(ClaimServiceLine line : serviceLines){
            if(!line.isRemoved()){
                this.totalAmount = this.totalAmount.add(line.getFeeByPriceOption(priceOptionType));
            }
        }
    }
    public BigDecimal getClaimTotalAmount(){
        return this.totalAmount;
    }

    public ClaimRoleType getClaimRole() {
        return claimRole;
    }

    public void setClaimRole(ClaimRoleType claimRole) {
        this.claimRole = claimRole;
    }

    public BigDecimal getTotalAmount() {
        return totalAmount;
    }

    public void setTotalAmount(BigDecimal totalAmount) {
        this.totalAmount = totalAmount;
    }
}
