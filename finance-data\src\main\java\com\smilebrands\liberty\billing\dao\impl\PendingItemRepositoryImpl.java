package com.smilebrands.liberty.billing.dao.impl;

import com.smilebrands.liberty.billing.dao.PendingItemRepository;
import com.smilebrands.liberty.billing.dao.PendingPatientLedgerRepository;
import com.smilebrands.liberty.billing.model.PendingItem;
import com.smilebrands.liberty.billing.model.PendingPatientLedger;
import com.smilebrands.liberty.billing.model.PendingRequirement;
import com.smilebrands.liberty.charting.model.Tooth;
import com.smilebrands.liberty.dao.SimpleAbstractDao;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Qualifier;
import org.springframework.dao.DataAccessException;
import org.springframework.jdbc.core.JdbcTemplate;
import org.springframework.jdbc.core.ResultSetExtractor;
import org.springframework.jdbc.core.RowMapper;
import org.springframework.jdbc.core.RowMapperResultSetExtractor;
import org.springframework.stereotype.Repository;

import java.sql.ResultSet;
import java.sql.SQLException;
import java.sql.Types;
import java.util.*;

/**
 * Created with IntelliJ IDEA.
 * User: phongpham
 * Date: 12/6/13
 * Time: 2:55 PM
 * To change this template use File | Settings | File Templates.
 */
@Repository
public class PendingItemRepositoryImpl extends SimpleAbstractDao implements PendingItemRepository {

    @Autowired
    @Qualifier("libertyJdbcTemplate")
    private JdbcTemplate jdbcTemplate;

    @Override
    public List<PendingItem> getPendingItem(Integer facilityId, Long patientId) {
        String query = "SELECT * FROM V_PENDING_ITEM WHERE (?1 is null OR FACILITY_ID=?1) AND (?2 is null OR LIBERTY_PATIENT_ID=?2)";
        List<PendingItem> result = (List<PendingItem>)jdbcTemplate.query(
                query,
                new Object[]{facilityId, facilityId, patientId, patientId},
                new PendingItemRowExtractor());
        return result;
    }

    @Override
    public List<PendingItem> getPendingItemByFacilityDates(String type, Integer facilityId, Date startDate, Date endDate) {
        String query = "SELECT * FROM V_PENDING_ITEM WHERE FACILITY_ID=?1 " +
                "   AND COMPLETED_ON IS NULL " +
                "   AND TRUNC(STATUS_CREATE_DATETIME) >= TRUNC(?2) " +
                "   AND TRUNC(STATUS_CREATE_DATETIME) <= TRUNC(?3)";
        Object[] parameters = null;
        if(type != null && type.trim().length() > 0){
            query +=  "   AND (PENDING_TYPE=?4)";
            parameters = new Object[]{facilityId, startDate, endDate, type};
        }else{
            parameters = new Object[]{facilityId, startDate, endDate};
        }

        List<PendingItem> result = (List<PendingItem>)jdbcTemplate.query(
                query,
                parameters,
                new PendingItemRowExtractor());
        return result;
    }

    class PendingItemRowExtractor implements ResultSetExtractor{
        public Object extractData(ResultSet rs) throws SQLException, DataAccessException {
            Map<Long, PendingItem> map = new HashMap<Long, PendingItem>();
            PendingItem pendingItem = null;
            List<PendingItem> result = new ArrayList<PendingItem>();
            while (rs.next()) {
                Long claimId = rs.getLong("CLAIM_ID");
                pendingItem = map.get(claimId);
                if(pendingItem == null){
                    pendingItem = new PendingItem();
                    pendingItem.setPendingType(rs.getString("PENDING_TYPE"));
                    pendingItem.setClaimId(rs.getLong("CLAIM_ID"));
                    pendingItem.setFacilityId(rs.getInt("FACILITY_ID"));
                    pendingItem.setPatientId(rs.getLong("LIBERTY_PATIENT_ID"));
                    pendingItem.setPatientFirstName(rs.getString("PATIENT_FIRST_NAME"));
                    pendingItem.setPatientLastName(rs.getString("PATIENT_LAST_NAME"));
                    pendingItem.setDateOfBirth(rs.getDate("DATE_OF_BIRTH"));
                    pendingItem.setGender(rs.getString("GENDER"));
                    pendingItem.setClaimStatusType(rs.getString("CLAIM_STATUS_TYPE"));
                    pendingItem.setStatusCreateDateTime(rs.getTimestamp("STATUS_CREATE_DATETIME"));
                    pendingItem.setCreateDateTime(rs.getTimestamp("CREATE_DATETIME"));
                    pendingItem.setServiceDollarTotal(rs.getBigDecimal("SERVICE_DOLLAR_TOTAL"));
                    pendingItem.setDateOfService(rs.getTimestamp("DATE_OF_SERVICE"));
                    pendingItem.setPrimaryInsuranceName(rs.getString("PRI_INSURANCE_NAME"));
                    map.put(claimId, pendingItem);
                    result.add(pendingItem);
                }
                PendingRequirement pendingRequirement = new PendingRequirement();
                pendingRequirement.setRequirementType(rs.getString("REQUIREMENT_TYPE"));
                pendingRequirement.setRequirementEntityId(rs.getLong("REQUIREMENT_ENTITY_ID"));
                pendingRequirement.setRequirementDescription(rs.getString("REQUIREMENT_DESCRIPTION"));
                pendingRequirement.setRequiredOn(rs.getTimestamp("REQUIRED_ON"));
                pendingRequirement.setRequiredByEmployeeName(rs.getString("REQUIRED_BY_EMP_NAME"));
                pendingRequirement.setRequiredByEmployeeNumber(rs.getInt("REQUIRED_BY_EMP_NUMBER"));
                pendingRequirement.setCompletedOn(rs.getTimestamp("COMPLETED_ON"));
                pendingRequirement.setCompletedByEmployeeName(rs.getString("COMPLETED_BY_EMP_NAME"));
                pendingRequirement.setCompletedByEmployeeNumber(rs.getInt("COMPLETED_BY_EMP_NUMBER"));
                pendingItem.getPendingRequirements().add(pendingRequirement);
            }
            return result;
        }
    };
}
