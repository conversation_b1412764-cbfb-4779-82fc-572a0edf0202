package com.smilebrands.liberty.facility.model;

import com.smilebrands.liberty.billing.model.PatientLedger;
import com.smilebrands.liberty.billing.model.PatientPaymentAdjustmentLedger;
import com.smilebrands.liberty.calendar.model.ScheduleEvent;
import com.smilebrands.liberty.constants.BatchType;
import com.smilebrands.liberty.model.BaseObject;
import com.smilebrands.liberty.serializer.DateIsoDeSerializer;
import com.smilebrands.liberty.serializer.DateIsoSerializer;
import com.smilebrands.liberty.treatment.model.Treatment;
import org.codehaus.jackson.map.annotate.JsonDeserialize;
import org.codehaus.jackson.map.annotate.JsonSerialize;

import java.math.BigDecimal;
import java.util.Date;

/**
 * Created with IntelliJ IDEA.
 * User: Phong Pham
 * Date: 1/6/13
 * Time: 5:48 PM
 * To change this template use File | Settings | File Templates.
 */
public class FacilityFinanceSummaryReport extends BaseObject {

    private String name;

    private BatchType batchType;

    private BigDecimal summaryPatientAmount;

    private BigDecimal summaryInsuranceAmount;

    private BigDecimal summaryTotalAmount;

    public String getName() {
        return name;
    }

    public void setName(String name) {
        this.name = name;
    }

    public BatchType getBatchType() {
        return batchType;
    }

    public void setBatchType(BatchType batchType) {
        this.batchType = batchType;
    }

    public BigDecimal getSummaryPatientAmount() {
        return summaryPatientAmount;
    }

    public void setSummaryPatientAmount(BigDecimal summaryPatientAmount) {
        this.summaryPatientAmount = summaryPatientAmount;
    }

    public BigDecimal getSummaryInsuranceAmount() {
        return summaryInsuranceAmount;
    }

    public void setSummaryInsuranceAmount(BigDecimal summaryInsuranceAmount) {
        this.summaryInsuranceAmount = summaryInsuranceAmount;
    }

    public BigDecimal getSummaryTotalAmount() {
        return summaryTotalAmount;
    }

    public void setSummaryTotalAmount(BigDecimal summaryTotalAmount) {
        this.summaryTotalAmount = summaryTotalAmount;
    }
}