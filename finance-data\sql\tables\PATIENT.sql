--------------------------------------------------------
--  DDL for Table PATIENT
--------------------------------------------------------

  CREATE TABLE "LIBERTY_DATA"."PATIENT"
   (	"PATIENT_ID" NUMBER(10,0), 
	"DATE_OF_BIRTH" TIMESTAMP (6), 
	"FIRST_NAME" VARCHAR2(30 BYTE), 
	"GENDER" CHAR(1 BYTE), 
	"LAST_NAME" VARCHAR2(30 BYTE), 
	"MIDDLE_NAME" VARCHAR2(30 BYTE), 
	"DATE_ENTERED" TIMESTAMP (6), 
	"EMP_ENTERED" NUMBER(6,0), 
	"DATE_UPDATED" TIMESTAMP (6), 
	"EMP_UPDATED" NUMBER(6,0), 
	"PATIENT_STATUS_TYPE" VARCHAR2(25 BYTE), 
	"RELATION_ID" NUMBER(15,0), 
	"PATIENT_CONTACT_TYPE" VARCHAR2(35 BYTE), 
	"PATIENT_LANGUAGE_TYPE" VARCHAR2(35 BYTE), 
	"HOME_PHONE_ID" NUMBER(15,5), 
	"WORK_PHONE_ID" NUMBER(15,5), 
	"MOBILE_PHONE_ID" NUMBER(15,5), 
	"HOME_ADDRESS_ID" NUMBER(15,5), 
	"WORK_ADDRESS_ID" NUMBER(15,5), 
	"HOME_EMAIL_ID" NUMBER(15,5), 
	"WORK_EMAIL_ID" NUMBER(15,5), 
	"APPOINTMENT_PHONE_ID" NUMBER(15,5), 
	"MVP_DATE_ID" NUMBER(15,0), 
	"PRIMARY_INSURANCE_ID" NUMBER(10,0), 
	"SECONDARY_INSURANCE_ID" NUMBER(10,0), 
	"TERTIARY_INSURANCE_ID" NUMBER(10,0)
   ) PCTFREE 10 PCTUSED 40 INITRANS 1 MAXTRANS 255 NOCOMPRESS LOGGING
  STORAGE(INITIAL 65536 NEXT 1048576 MINEXTENTS 1 MAXEXTENTS **********
  PCTINCREASE 0 FREELISTS 1 FREELIST GROUPS 1 BUFFER_POOL DEFAULT)
  TABLESPACE "BNDNEW_DATA" ;
