--------------------------------------------------------
--  DDL for Table PATIENT_EMAIL
--------------------------------------------------------

  CREATE TABLE "LIBERTY_DATA"."PATIENT_EMAIL"
   (	"EMAIL_ID" NUMBER(10,0), 
	"PATIENT_ID" NUMBER(10,0), 
	"EMAIL_ACTIVE" CHAR(1 BYTE), 
	"ACTIVE_DATE" TIMESTAMP (6), 
	"INACTIVE_DATE" TIMESTAMP (6), 
	"PREFERRED" CHAR(1 BYTE), 
	"EMAIL_TYPE" VARCHAR2(10 BYTE), 
	"EMAIL_ADDRESS" VARCHAR2(50 BYTE), 
	"DATE_ENTERED" TIMESTAMP (6), 
	"EMP_ENTERED" NUMBER(6,0), 
	"DATE_UPDATED" TIMESTAMP (6), 
	"EMP_UPDATED" NUMBER(6,0), 
	"COMMUNICATION_ALLOWED" CHAR(1 BYTE)
   ) PCTFREE 10 PCTUSED 40 INITRANS 1 MAXTRANS 255 NOCOMPRESS LOGGING
  STORAGE(INITIAL 65536 NEXT 1048576 MINEXTENTS 1 MAXEXTENTS **********
  PCTINCREASE 0 FREELISTS 1 FREELIST GROUPS 1 BUFFER_POOL DEFAULT)
  TABLESPACE "BNDNEW_DATA" ;
