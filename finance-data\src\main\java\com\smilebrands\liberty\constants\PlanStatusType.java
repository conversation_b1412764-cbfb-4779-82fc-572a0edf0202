package com.smilebrands.liberty.constants;

/**
 * <AUTHOR> <PERSON><PERSON>
 *         Smile Brands Inc.
 *         Date: 04/10/13
 */
public enum PlanStatusType {

    A("Active"),
    I("Inactive"),
    W("Working Copy"),
    P("Pending"),
    C("Copy");

    private final String strValue;

    private PlanStatusType(final String strValue) {
        this.strValue = strValue;
    }

    public String getType() {
        return strValue;
    }

    @Override
    public String toString() {
        return this.strValue;
    }
}
