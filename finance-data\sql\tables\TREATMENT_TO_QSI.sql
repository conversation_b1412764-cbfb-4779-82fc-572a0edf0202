--------------------------------------------------------
--  DDL for Table TREATMENT_TO_QSI
--------------------------------------------------------

  CREATE TABLE "LIBERTY_DATA"."TREATMENT_TO_QSI"
   (	"TREATMENT_ID" NUMBER(15,0), 
	"CORRELATION_ID" VARCHAR2(25 BYTE), 
	"COLLECTED_DATE_TIME" TIMESTAMP (6), 
	"TRANSMIT_DATE_TIME" TIMESTAMP (6), 
	"PRINT_DATE_TIME" TIMESTAMP (6), 
	"PRINT_EMPLOYEE_NUMBER" NUMBER(6,0), 
	"RESPONSE_DATE_TIME" TIMESTAMP (6), 
	"RESPONSE_MESSAGE" VARCHAR2(150 BYTE), 
	"AUDIT_INDICATOR" CHAR(1 BYTE), 
	"AUDIT_DATE_TIME" TIMESTAMP (6), 
	"QSI_CLINIC_ID" NUMBER(4,0), 
	"QSI_LOCATION_ID" VARCHAR2(2 BYTE), 
	"QSI_PROVIDER_ID" NUMBER(5,0), 
	"QSI_TRANS_CODE" NUMBER(4,0), 
	"QSI_UNIQUE_PATIENT_ID" NUMBER(15,0), 
	"QSI_TOTAL" NUMBER(7,2), 
	"QSI_PATIENT" NUMBER(7,2), 
	"QSI_PRI_PLAN_ID" NUMBER(7,0), 
	"QSI_PRI_TOTAL" NUMBER(7,2), 
	"QSI_SEC_PLAN_ID" NUMBER(7,0), 
	"QSI_SEC_TOTAL" NUMBER(7,2), 
	"QSI_UCR_FEE" NUMBER(7,2), 
	"RESPONSE_ID" NUMBER(15,0)
   ) PCTFREE 10 PCTUSED 40 INITRANS 1 MAXTRANS 255 NOCOMPRESS LOGGING
  STORAGE(INITIAL 65536 NEXT 1048576 MINEXTENTS 1 MAXEXTENTS **********
  PCTINCREASE 0 FREELISTS 1 FREELIST GROUPS 1 BUFFER_POOL DEFAULT)
  TABLESPACE "BNDNEW_DATA" ;
