package com.smilebrands.liberty.finance.model;

import com.smilebrands.liberty.model.BaseObject;

import javax.persistence.Entity;
import javax.persistence.Id;
import java.math.BigDecimal;
import java.util.Date;

/**
 * <AUTHOR> <PERSON><PERSON>
 *         Smile Brands Inc.
 *         Date: 04/02/14
 */
@Entity
public class Guarantor extends BaseObject {

    private Integer achAccountType;
    private String achAccountNumber;
    private String achRoutingNumber;
    private String customerId;
    private BigDecimal availableBalance;
    private BigDecimal generalCreditLimit;
    private BigDecimal generalDentistryOTB;
    private BigDecimal orthoCreditLimit;
    private BigDecimal orthoOTB;
    private BigDecimal availableCreditLimit;
    private BigDecimal totalPastDue;
    private BigDecimal totalPastDueAmount;
    private String ssn;
    @Id
    private String guarantorAccountNumber;
    private String guarantorPrefix;
    private String guarantorFirstName;
    private String guarantorMiddleInitial;
    private String guarantorLastName;
    private String guarantorSuffix;
    private String guarantorAddressLine1;
    private String guarantorAddressLine2;
    private String guarantorCity;
    private String guarantorState;
    private String guarantorZipCode;
    private String guarantorCountry;
    private String guarantorHomePhoneNumber;
    private String guarantorMobilePhoneNumber;
    private String guarantorWorkPhoneNumber;
    private String guarantorMessagePhoneNumber;
    private String guarantorOtherPhoneNumber;
    private String guarantorSSN;
    private String patientIdActive;
    private Long patientIdGeneral;
    private Long patientIdOrtho;
    private String patientFirstName;
    private String patientLastName;
    private Date dateOfBirth;
    private Integer systemStatusCode;
    private String systemStatus;
    private Integer manualStatusCode;
    private String manualStatus;

    public Integer getAchAccountType() {
        return achAccountType;
    }

    public void setAchAccountType(Integer achAccountType) {
        this.achAccountType = achAccountType;
    }

    public String getAchAccountNumber() {
        return achAccountNumber;
    }

    public void setAchAccountNumber(String achAccountNumber) {
        this.achAccountNumber = achAccountNumber;
    }

    public String getAchRoutingNumber() {
        return achRoutingNumber;
    }

    public void setAchRoutingNumber(String achRoutingNumber) {
        this.achRoutingNumber = achRoutingNumber;
    }

    public String getCustomerId() {
        return customerId;
    }

    public void setCustomerId(String customerId) {
        this.customerId = customerId;
    }

    public BigDecimal getAvailableBalance() {
        return availableBalance;
    }

    public void setAvailableBalance(BigDecimal availableBalance) {
        this.availableBalance = availableBalance;
    }

    public BigDecimal getGeneralCreditLimit() {
        return generalCreditLimit;
    }

    public void setGeneralCreditLimit(BigDecimal generalCreditLimit) {
        this.generalCreditLimit = generalCreditLimit;
    }

    public BigDecimal getGeneralDentistryOTB() {
        return generalDentistryOTB;
    }

    public void setGeneralDentistryOTB(BigDecimal generalDentistryOTB) {
        this.generalDentistryOTB = generalDentistryOTB;
    }

    public BigDecimal getOrthoCreditLimit() {
        return orthoCreditLimit;
    }

    public void setOrthoCreditLimit(BigDecimal orthoCreditLimit) {
        this.orthoCreditLimit = orthoCreditLimit;
    }

    public BigDecimal getOrthoOTB() {
        return orthoOTB;
    }

    public void setOrthoOTB(BigDecimal orthoOTB) {
        this.orthoOTB = orthoOTB;
    }

    public BigDecimal getAvailableCreditLimit() {
        return availableCreditLimit;
    }

    public void setAvailableCreditLimit(BigDecimal availalbeCreditLimit) {
        this.availableCreditLimit = availalbeCreditLimit;
    }

    public BigDecimal getTotalPastDue() {
        return totalPastDue;
    }

    public void setTotalPastDue(BigDecimal totalPastDue) {
        this.totalPastDue = totalPastDue;
    }

    public BigDecimal getTotalPastDueAmount() {
        return totalPastDueAmount;
    }

    public void setTotalPastDueAmount(BigDecimal totalPastDueAmount) {
        this.totalPastDueAmount = totalPastDueAmount;
    }

    public String getSsn() {
        return ssn;
    }

    public void setSsn(String ssn) {
        this.ssn = ssn;
    }

    public String getGuarantorAccountNumber() {
        return guarantorAccountNumber;
    }

    public void setGuarantorAccountNumber(String guarantorAccountNumber) {
        this.guarantorAccountNumber = guarantorAccountNumber;
    }

    public String getGuarantorPrefix() {
        return guarantorPrefix;
    }

    public void setGuarantorPrefix(String guarantorPrefix) {
        this.guarantorPrefix = guarantorPrefix;
    }

    public String getGuarantorFirstName() {
        return guarantorFirstName;
    }

    public void setGuarantorFirstName(String guarantorFirstName) {
        this.guarantorFirstName = guarantorFirstName;
    }

    public String getGuarantorMiddleInitial() {
        return guarantorMiddleInitial;
    }

    public void setGuarantorMiddleInitial(String guarantorMiddleInitial) {
        this.guarantorMiddleInitial = guarantorMiddleInitial;
    }

    public String getGuarantorLastName() {
        return guarantorLastName;
    }

    public void setGuarantorLastName(String guarantorLastName) {
        this.guarantorLastName = guarantorLastName;
    }

    public String getGuarantorSuffix() {
        return guarantorSuffix;
    }

    public void setGuarantorSuffix(String guarantorSuffix) {
        this.guarantorSuffix = guarantorSuffix;
    }

    public String getGuarantorAddressLine1() {
        return guarantorAddressLine1;
    }

    public String getGuarantorAddressLine2() {
        return guarantorAddressLine2;
    }

    public void setGuarantorAddressLine2(String guarantorAddressLine2) {
        this.guarantorAddressLine2 = guarantorAddressLine2;
    }

    public void setGuarantorAddressLine1(String guarantorAddressLine1) {
        this.guarantorAddressLine1 = guarantorAddressLine1;
    }

    public String getGuarantorCity() {
        return guarantorCity;
    }

    public void setGuarantorCity(String guarantorCity) {
        this.guarantorCity = guarantorCity;
    }

    public String getGuarantorState() {
        return guarantorState;
    }

    public void setGuarantorState(String guarantorState) {
        this.guarantorState = guarantorState;
    }

    public String getGuarantorZipCode() {
        return guarantorZipCode;
    }

    public void setGuarantorZipCode(String guarantorZipCode) {
        this.guarantorZipCode = guarantorZipCode;
    }

    public String getGuarantorCountry() {
        return guarantorCountry;
    }

    public void setGuarantorCountry(String guarantorCountry) {
        this.guarantorCountry = guarantorCountry;
    }

    public String getGuarantorHomePhoneNumber() {
        return guarantorHomePhoneNumber;
    }

    public void setGuarantorHomePhoneNumber(String guarantorHomePhoneNumber) {
        this.guarantorHomePhoneNumber = guarantorHomePhoneNumber;
    }

    public String getGuarantorMobilePhoneNumber() {
        return guarantorMobilePhoneNumber;
    }

    public void setGuarantorMobilePhoneNumber(String guarantorMobilePhoneNumber) {
        this.guarantorMobilePhoneNumber = guarantorMobilePhoneNumber;
    }

    public String getGuarantorWorkPhoneNumber() {
        return guarantorWorkPhoneNumber;
    }

    public void setGuarantorWorkPhoneNumber(String guarantorWorkPhoneNumber) {
        this.guarantorWorkPhoneNumber = guarantorWorkPhoneNumber;
    }

    public String getGuarantorMessagePhoneNumber() {
        return guarantorMessagePhoneNumber;
    }

    public void setGuarantorMessagePhoneNumber(String guarantorMessagePhoneNumber) {
        this.guarantorMessagePhoneNumber = guarantorMessagePhoneNumber;
    }

    public String getGuarantorOtherPhoneNumber() {
        return guarantorOtherPhoneNumber;
    }

    public void setGuarantorOtherPhoneNumber(String guarantorOtherPhoneNumber) {
        this.guarantorOtherPhoneNumber = guarantorOtherPhoneNumber;
    }

    public String getGuarantorSSN() {
        return guarantorSSN;
    }

    public void setGuarantorSSN(String guarantorSSN) {
        this.guarantorSSN = guarantorSSN;
    }

    public String getPatientIdActive() {
        return patientIdActive;
    }

    public void setPatientIdActive(String patientIdActive) {
        this.patientIdActive = patientIdActive;
    }

    public Long getPatientIdGeneral() {
        return patientIdGeneral;
    }

    public void setPatientIdGeneral(Long patientIdGeneral) {
        this.patientIdGeneral = patientIdGeneral;
    }

    public Long getPatientIdOrtho() {
        return patientIdOrtho;
    }

    public void setPatientIdOrtho(Long patientIdOrtho) {
        this.patientIdOrtho = patientIdOrtho;
    }

    public String getPatientFirstName() {
        return patientFirstName;
    }

    public void setPatientFirstName(String patientFirstName) {
        this.patientFirstName = patientFirstName;
    }

    public String getPatientLastName() {
        return patientLastName;
    }

    public void setPatientLastName(String patientLastName) {
        this.patientLastName = patientLastName;
    }

    public Date getDateOfBirth() {
        return dateOfBirth;
    }

    public void setDateOfBirth(Date dateOfBirth) {
        this.dateOfBirth = dateOfBirth;
    }

    public Integer getSystemStatusCode() {
        return systemStatusCode;
    }

    public void setSystemStatusCode(Integer systemStatusCode) {
        this.systemStatusCode = systemStatusCode;
    }

    public String getSystemStatus() {
        return systemStatus;
    }

    public void setSystemStatus(String systemStatus) {
        this.systemStatus = systemStatus;
    }

    public Integer getManualStatusCode() {
        return manualStatusCode;
    }

    public void setManualStatusCode(Integer manualStatusCode) {
        this.manualStatusCode = manualStatusCode;
    }

    public String getManualStatus() {
        return manualStatus;
    }

    public void setManualStatus(String manualStatus) {
        this.manualStatus = manualStatus;
    }
}
