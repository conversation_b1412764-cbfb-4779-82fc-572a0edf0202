--------------------------------------------------------
--  DDL for Table TOOTH
--------------------------------------------------------

  CREATE TABLE "LIBERTY_DATA"."TOOTH"
   (	"TOOTH_ID" NUMBER(10,0), 
	"TOOTH_NUMBER" VARCHAR2(2 BYTE), 
	"TOOTH_NAME" VARCHAR2(25 BYTE), 
	"TOOTH_TYPE" VARCHAR2(25 BYTE), 
	"ARCH" VARCHAR2(25 BYTE), 
	"QUADRANT" VARCHAR2(25 BYTE), 
	"NUMBER_OF_ROOTS" NUMBER(1,0)
   ) PCTFREE 10 PCTUSED 40 INITRANS 1 MAXTRANS 255 NOCOMPRESS LOGGING
  STORAGE(INITIAL 65536 NEXT 1048576 MINEXTENTS 1 MAXEXTENTS 2147483645
  PCTINCREASE 0 FREELISTS 1 FREELIST GROUPS 1 BUFFER_POOL DEFAULT)
  TABLESPACE "BNDNEW_DATA" ;
