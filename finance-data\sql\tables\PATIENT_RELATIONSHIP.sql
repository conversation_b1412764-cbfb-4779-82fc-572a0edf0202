--------------------------------------------------------
--  DDL for Table PATIENT_RELATIONSHIP
--------------------------------------------------------

  CREATE TABLE "LIBERTY_DATA"."PATIENT_RELATIONSHIP"
   (	"RELATION_ID" NUMBER(15,0), 
	"PATIENT_ID" NUMBER(10,0), 
	"PARENT_PATIENT_ID" NUMBER(10,0), 
	"RELATIONSHIP" VARCHAR2(25 BYTE), 
	"ACTIVATION_DATETIME" TIMESTAMP (6), 
	"ACTIVATION_EMPLOYEE" NUMBER(6,0), 
	"EXPIRATION_DATETIME" TIMESTAMP (6), 
	"EXPIRATION_EMPLOYEE" NUMBER(6,0), 
	"CREATE_DATETIME" TIMESTAMP (6), 
	"CREATE_EMPLOYEE" NUMBER(6,0)
   ) PCTFREE 10 PCTUSED 40 INITRANS 1 MAXTRANS 255 NOCOMPRESS LOGGING
  STORAGE(INITIAL 65536 NEXT 1048576 MINEXTENTS 1 MAXEXTENTS **********
  PCTINCREASE 0 FREELISTS 1 FREELIST GROUPS 1 BUFFER_POOL DEFAULT)
  TABLESPACE "BNDNEW_DATA" ;
