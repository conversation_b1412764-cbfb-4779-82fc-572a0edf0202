package com.smilebrands.liberty.document.model;

import com.smilebrands.liberty.model.BaseObject;
import org.springframework.data.annotation.Transient;
import org.springframework.data.mongodb.core.mapping.Field;

public class Context extends BaseObject {

    @Field("::/liberty")
    private Liberty office;

    @Field("::/claims")
    private Liberty claims;

    @Field("::/touch")
    private Liberty touch;

    @Field("::/pricing")
    private Liberty pricing;

    @Field("::/admin")
    private Liberty admin;

    public Liberty getLiberty() {
        if(this.office != null){
            return this.office;
        }else if(this.claims != null){
            return this.claims;
        }else if(this.touch != null){
            return this.touch;
        }else if(this.pricing != null){
            return this.pricing;
        }else if (this.admin != null){
            return this.admin;
        }else{
            return null;
        }
    }



}
