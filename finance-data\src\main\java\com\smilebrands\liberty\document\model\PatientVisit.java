package com.smilebrands.liberty.document.model;

import com.smilebrands.liberty.model.BaseObject;
import org.springframework.data.annotation.Id;
import org.springframework.data.mongodb.core.mapping.Document;

import java.math.BigDecimal;
import java.util.Date;
import java.util.List;

/**
 * User: <PERSON><PERSON>
 * Date: 8/21/13
 */
@Document
public class PatientVisit extends BaseObject {

    @Id
    private Long patientVisitId;
    private Facility facility;

    private Long patientId;
    private String patientName;
    private String patientGender;
    private Integer patientAge;

    private Integer insurancePlanNumber;
    private String insurancePlan;

    private Date dateOfService;
    private Integer serviceProviderId;
    private String serviceProviderName;
    private String serviceProviderType;
    private boolean firstVisit;
    private boolean emergencyVisit;
    private boolean orthodonticVisit;

    private TreatmentPlan today;

    private BigDecimal insuranceEstimate;
    private BigDecimal insuranceCompleted;
    private BigDecimal insuranceDiscounted;

    private BigDecimal patientEstimate;
    private BigDecimal patientCompleted;
    private BigDecimal patientDiscounted;
    private BigDecimal patientCollected;

    private List<Treatment> planned;
    private List<Treatment> completed;
    private List<Treatment> incomplete;
    private List<Treatment> rescheduled;

    private TreatmentPlan nextVisit;

    public Long getPatientVisitId() {
        return patientVisitId;
    }

    public void setPatientVisitId(Long patientVisitId) {
        this.patientVisitId = patientVisitId;
    }

    public Long getPatientId() {
        return patientId;
    }

    public void setPatientId(Long patientId) {
        this.patientId = patientId;
    }

    public String getPatientName() {
        return patientName;
    }

    public void setPatientName(String patientName) {
        this.patientName = patientName;
    }

    public String getPatientGender() {
        return patientGender;
    }

    public void setPatientGender(String patientGender) {
        this.patientGender = patientGender;
    }

    public Integer getPatientAge() {
        return patientAge;
    }

    public void setPatientAge(Integer patientAge) {
        this.patientAge = patientAge;
    }

    public Integer getInsurancePlanNumber() {
        return insurancePlanNumber;
    }

    public void setInsurancePlanNumber(Integer insurancePlanNumber) {
        this.insurancePlanNumber = insurancePlanNumber;
    }

    public String getInsurancePlan() {
        return insurancePlan;
    }

    public void setInsurancePlan(String insurancePlan) {
        this.insurancePlan = insurancePlan;
    }

    public Date getDateOfService() {
        return dateOfService;
    }

    public void setDateOfService(Date dateOfService) {
        this.dateOfService = dateOfService;
    }

    public Integer getServiceProviderId() {
        return serviceProviderId;
    }

    public void setServiceProviderId(Integer serviceProviderId) {
        this.serviceProviderId = serviceProviderId;
    }

    public String getServiceProviderName() {
        return serviceProviderName;
    }

    public void setServiceProviderName(String serviceProviderName) {
        this.serviceProviderName = serviceProviderName;
    }

    public TreatmentPlan getToday() {
        return today;
    }

    public void setToday(TreatmentPlan today) {
        this.today = today;
    }

    public BigDecimal getInsuranceEstimate() {
        return insuranceEstimate;
    }

    public void setInsuranceEstimate(BigDecimal insuranceEstimate) {
        this.insuranceEstimate = insuranceEstimate;
    }

    public BigDecimal getInsuranceCompleted() {
        return insuranceCompleted;
    }

    public void setInsuranceCompleted(BigDecimal insuranceCompleted) {
        this.insuranceCompleted = insuranceCompleted;
    }

    public BigDecimal getInsuranceDiscounted() {
        return insuranceDiscounted;
    }

    public void setInsuranceDiscounted(BigDecimal insuranceDiscounted) {
        this.insuranceDiscounted = insuranceDiscounted;
    }

    public BigDecimal getPatientEstimate() {
        return patientEstimate;
    }

    public void setPatientEstimate(BigDecimal patientEstimate) {
        this.patientEstimate = patientEstimate;
    }

    public BigDecimal getPatientCompleted() {
        return patientCompleted;
    }

    public void setPatientCompleted(BigDecimal patientCompleted) {
        this.patientCompleted = patientCompleted;
    }

    public BigDecimal getPatientCollected() {
        return patientCollected;
    }

    public void setPatientCollected(BigDecimal patientCollected) {
        this.patientCollected = patientCollected;
    }

    public BigDecimal getPatientDiscounted() {
        return patientDiscounted;
    }

    public void setPatientDiscounted(BigDecimal patientDiscounted) {
        this.patientDiscounted = patientDiscounted;
    }

    public List<Treatment> getPlanned() {
        return planned;
    }

    public void setPlanned(List<Treatment> planned) {
        this.planned = planned;
    }

    public List<Treatment> getCompleted() {
        return completed;
    }

    public void setCompleted(List<Treatment> completed) {
        this.completed = completed;
    }

    public List<Treatment> getIncomplete() {
        return incomplete;
    }

    public void setIncomplete(List<Treatment> incomplete) {
        this.incomplete = incomplete;
    }

    public List<Treatment> getRescheduled() {
        return rescheduled;
    }

    public void setRescheduled(List<Treatment> rescheduled) {
        this.rescheduled = rescheduled;
    }

    public TreatmentPlan getNextVisit() {
        return nextVisit;
    }

    public void setNextVisit(TreatmentPlan nextVisit) {
        this.nextVisit = nextVisit;
    }

    public Facility getFacility() {
        return facility;
    }

    public void setFacility(Facility facility) {
        this.facility = facility;
    }

    public String getServiceProviderType() {
        return serviceProviderType;
    }

    public void setServiceProviderType(String serviceProviderType) {
        this.serviceProviderType = serviceProviderType;
    }

    public boolean isFirstVisit() {
        return firstVisit;
    }

    public void setFirstVisit(boolean firstVisit) {
        this.firstVisit = firstVisit;
    }

    public boolean isEmergencyVisit() {
        return emergencyVisit;
    }

    public void setEmergencyVisit(boolean emergencyVisit) {
        this.emergencyVisit = emergencyVisit;
    }

    public boolean isOrthodonticVisit () {
        return orthodonticVisit;
    }

    public void setOrthodonticVisit (boolean orthodonticVisit) {
        this.orthodonticVisit = orthodonticVisit;
    }
}
