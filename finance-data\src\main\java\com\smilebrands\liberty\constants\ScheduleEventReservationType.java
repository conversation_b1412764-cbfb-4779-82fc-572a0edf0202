package com.smilebrands.liberty.constants;

/**
 * Java5 style enum class to represent the various reservation types of schedule event reservations.
 * 
 * <AUTHOR> Smile Brands Inc, 2012
 */
public enum ScheduleEventReservationType {

    COMPLEX("complex"){
        @Override
        public String getDescription(){
            return "Complex event reservation.";
        }
    },
    MAJOR("major"){
        @Override
        public String getDescription(){
            return "Major production event reservation.";
        }
    },
    MINOR("minor"){
        @Override
        public String getDescription(){
            return "Minor production event reservation.";
        }
    },
    ADULT("adult"){
        @Override
        public String getDescription(){
            return "Adult exam event reservation.";
        }
    },
    CHILD("child"){
        @Override
        public String getDescription(){
            return "Child exam event reservation.";
        }
    },
    NEW_PATIENT("new patient"){
        @Override
        public String getDescription() {
            return "New Patient reservations";
        }
    },
    EMERGENCY("emergency"){
        @Override
        public String getDescription(){
            return "Emergency event reservation.";
        }
    },
    RECALL("recall"){
        @Override
        public String getDescription(){
            return "Recall event reservation.";
        }
    },
    MANAGED_CARE("managed care"){
        @Override
        public String getDescription(){
            return "Managed Care event reservation.";
        }
    },
    ZERO("zero"){
        @Override
        public String getDescription(){
            return "Zero production event reservations.";
        }
    },
    HYGIENE("hygiene"){
        @Override
        public String getDescription(){
            return "Hygienist column event reservations.";
        }
    },
    BANDING("banding"){
        @Override
        public String getDescription(){
            return "Banding columns";
        }
    },
    ADJUSTMENT("adjustment"){
        @Override
        public String getDescription(){
            return "Adjustment columns";
        }
    },
    CONSULT("hygiene"){
        @Override
        public String getDescription(){
            return "Hygienist column event reservations.";
        }
    },
    CONSULT_CC("call center consult"){
        @Override
        public String getDescription(){
            return "Consultations created by the call center";
        }
    };
    private final String status;
    private ScheduleEventReservationType(String in){
        this.status = in;
    }

    public String getDescription(){
        return "";
    }

    @Override
    public String toString(){
        return this.status;
    }
}
