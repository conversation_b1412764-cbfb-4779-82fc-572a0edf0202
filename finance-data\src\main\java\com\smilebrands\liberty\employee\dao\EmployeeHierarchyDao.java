package com.smilebrands.liberty.employee.dao;

import com.smilebrands.liberty.employee.model.Employee;
import java.util.List;
import org.springframework.dao.DataAccessException;

/**
 * Interface to define access methods for SBI employee.
 * 
 * <AUTHOR> Smile Brands Inc., 2011
 */
public interface EmployeeHierarchyDao {

    public List<Employee> getEmployeeHierarchy(int facilityId) throws DataAccessException;

    public List<Employee> getOfficeManager(int facilityId) throws DataAccessException;

    public List<Employee> getDdo(int facilityId) throws DataAccessException;

    public List<Employee> getAvp(int facilityId) throws DataAccessException;
}
