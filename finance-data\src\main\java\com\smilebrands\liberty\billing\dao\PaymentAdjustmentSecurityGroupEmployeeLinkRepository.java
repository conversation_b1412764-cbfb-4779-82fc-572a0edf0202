package com.smilebrands.liberty.billing.dao;

import com.smilebrands.liberty.billing.model.PaymentAdjustmentSecurityGroupEmployeeLink;
import org.springframework.data.repository.CrudRepository;

import java.util.List;

/**
 * User: <PERSON><PERSON>
 * Date: 12/27/13
 */
public interface PaymentAdjustmentSecurityGroupEmployeeLinkRepository extends CrudRepository<PaymentAdjustmentSecurityGroupEmployeeLink, Long>, PaymentAdjustmentSecurityEmployeeCustom {

    public List<PaymentAdjustmentSecurityGroupEmployeeLink> getEmployeePaymentAdjustmentGroups(Integer employeeNumber);
}
