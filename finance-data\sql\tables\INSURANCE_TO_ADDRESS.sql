--------------------------------------------------------
--  DDL for Table INSURANCE_TO_ADDRESS
--------------------------------------------------------

  CREATE TABLE "LIBERTY_DATA"."INSURANCE_TO_ADDRESS"
   (	"INS_TO_ADDRESS_ID" NUMBER(10,0), 
	"ADDRESS_TYPE" VARCHAR2(10 BYTE), 
	"INS_CO_ADDRESS_ID" NUMBER(10,0), 
	"INS_CO_PLAN_ID" NUMBER(10,0), 
	"INS_CO_PLAN_CODE" CHAR(1 BYTE), 
	"CREATE_EMPLOYEE" NUMBER(10,0), 
	"CREATE_DATETIME" TIMESTAMP (6), 
	"UPDATE_EMPLOYEE" NUMBER(10,0), 
	"UPDATE_DATETIME" TIMESTAMP (6)
   ) PCTFREE 10 PCTUSED 40 INITRANS 1 MAXTRANS 255 NOCOMPRESS LOGGING
  STORAGE(INITIAL 65536 NEXT 1048576 MINEXTENTS 1 MAXEXTENTS 2147483645
  PCTINCREASE 0 FREELISTS 1 FREELIST GROUPS 1 BUFFER_POOL DEFAULT)
  TABLESPACE "BNDNEW_DATA" ;
