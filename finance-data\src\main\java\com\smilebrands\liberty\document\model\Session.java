package com.smilebrands.liberty.document.model;

import com.smilebrands.liberty.model.BaseObject;
import org.springframework.data.annotation.Id;
import org.springframework.data.mongodb.core.mapping.Document;
import org.springframework.data.mongodb.core.mapping.Field;

@Document(collection="sessions")
public class Session extends BaseObject {

    @Field("id")
    private String sessionId;

    private Long accessed;

    private Long created;

    private boolean valid;

    private Context context;

    public String getSessionId() {
        return sessionId;
    }

    public void setSessionId(String sessionId) {
        this.sessionId = sessionId;
    }

    public Long getAccessed() {
        return accessed;
    }

    public void setAccessed(Long accessed) {
        this.accessed = accessed;
    }

    public Long getCreated() {
        return created;
    }

    public void setCreated(Long created) {
        this.created = created;
    }

    public boolean isValid() {
        return valid;
    }

    public void setValid(boolean valid) {
        this.valid = valid;
    }

    public Context getContext() {
        return context;
    }

    public void setContext(Context context) {
        this.context = context;
    }
}
