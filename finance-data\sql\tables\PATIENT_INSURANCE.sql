--------------------------------------------------------
--  DDL for Table PATIENT_INSURANCE
--------------------------------------------------------

  CREATE TABLE "LIBERTY_DATA"."PATIENT_INSURANCE"
   (	"INSURANCE_ID" NUMBER(10,0), 
	"PATIENT_ID" NUMBER(10,0), 
	"INSURANCE_SUBSCRIBER_ID" NUMBER(10,0), 
	"INSURANCE_ACTIVE" CHAR(1 BYTE), 
	"ACTIVE_DATE" TIMESTAMP (6), 
	"INACTIVE_DATE" TIMESTAMP (6), 
	"INSURANCE_COVERAGE_TYPE" VARCHAR2(10 BYTE), 
	"RELATIONSHIP_TYPE" VARCHAR2(10 BYTE), 
	"DATE_ENTERED" TIMESTAMP (6), 
	"EMP_ENTERED" NUMBER(6,0), 
	"DATE_UPDATED" TIMESTAMP (6), 
	"EMP_UPDATED" NUMBER(6,0)
   ) PCTFREE 10 PCTUSED 40 INITRANS 1 MAXTRANS 255 NOCOMPRESS LOGGING
  STORAGE(INITIAL 65536 NEXT 1048576 MINEXTENTS 1 MAXEXTENTS **********
  PCTINCREASE 0 FREELISTS 1 FREELIST GROUPS 1 BUFFER_POOL DEFAULT)
  TABLESPACE "BNDNEW_DATA" ;
