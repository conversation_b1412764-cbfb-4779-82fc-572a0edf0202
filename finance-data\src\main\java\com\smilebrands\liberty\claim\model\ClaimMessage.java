package com.smilebrands.liberty.claim.model;

import com.smilebrands.liberty.constants.ClaimMessageType;
import com.smilebrands.liberty.model.BaseObject;
import com.smilebrands.liberty.utility.JsonDateDeSerializer;
import com.smilebrands.liberty.utility.JsonDateSerializer;
import org.codehaus.jackson.map.annotate.JsonDeserialize;
import org.codehaus.jackson.map.annotate.JsonSerialize;
import org.hibernate.annotations.NotFound;
import org.hibernate.annotations.NotFoundAction;

import javax.persistence.*;
import java.util.Date;

/**
 * User: <PERSON><PERSON>
 * Date: 8/20/12
 */
@Entity
@Table(name = "CLAIM_MESSAGE")
@NamedQueries({
        @NamedQuery(name = "ClaimMessage.getClaimMessages",
                    query = "select m from ClaimMessage m " +
                            "  left join fetch m.requirement " +
                            "  left join fetch m.acknowledgementEmployee " +
                            "  left join fetch m.createEmployee " +
                            "where m.claimId = ?"),
        @NamedQuery(name = "ClaimMessage.getClaimMessagesByClaimIds",
                query = "select m from ClaimMessage m " +
                        "  left join fetch m.requirement " +
                        "  left join fetch m.acknowledgementEmployee " +
                        "  left join fetch m.createEmployee " +
                        "where m.claimId in ?1"),
        @NamedQuery(name = "ClaimMessage.getClaimMessagesByType",
                    query = "select m from ClaimMessage m " +
                            "  left join fetch m.requirement " +
                            "  left join fetch m.acknowledgementEmployee " +
                            "  left join fetch m.createEmployee " +
                            "where m.claimId = ? " +
                            "and m.claimMessageType = ?"),
        @NamedQuery(name = "ClaimMessage.getClaimMessagesByUserType",
                query = "select m from ClaimMessage m " +
                        "  join fetch m.requirement r " +
                        "  left join fetch m.acknowledgementEmployee " +
                        "  left join fetch m.createEmployee " +
                        "where m.claimId = ?1 " +
                        "and r.selectionUserType = ?2")
})
@SuppressWarnings("serial")
public class ClaimMessage extends BaseObject {

    @Id
    @Column(name = "CLAIM_MSG_ID")
    @GeneratedValue(generator = "ClaimMessageSeq")
    @SequenceGenerator(name = "ClaimMessageSeq", sequenceName = "CLAIM_MSG_SEQ", allocationSize = 1)
    private Long claimMessageId;

    @Column(name = "CLAIM_ID")
    protected Long claimId;

    @Transient
    protected Integer messageCount;

    @Column(name = "MESSAGE_TYPE")
    @Enumerated(EnumType.STRING)
    private ClaimMessageType claimMessageType;

    @Column(name = "MESSAGE")
    private String message;

    @Temporal(TemporalType.TIMESTAMP)
    @Column(name = "CREATION_DATE")
    private Date creationDateTime;

    @Column(name = "ACK_EMPLOYEE_NUMBER")
    private Integer acknowledgementEmployeeNumber;

    @NotFound(action= NotFoundAction.IGNORE)
    @ManyToOne(fetch=FetchType.LAZY)
    @JoinColumn(name="ACK_EMPLOYEE_NUMBER", referencedColumnName = "EMPLOYEE_NUMBER", insertable=false, updatable=false)
    private ClaimEmployee acknowledgementEmployee;

    @Temporal(TemporalType.TIMESTAMP)
    @Column(name = "ACK_DATE_TIME")
    private Date acknowledgementDateTime;

    @Column(name = "REQUIREMENT_ID")
    private Integer requirementId;

    @OneToOne
    @JoinColumn(name = "REQUIREMENT_ID", insertable = false, updatable = false)
    private Requirement requirement;

    @Column(name = "CREATE_EMPLOYEE_NUMBER")
    private Integer createEmployeeNumber;

    @NotFound(action= NotFoundAction.IGNORE)
    @ManyToOne(fetch=FetchType.LAZY)
    @JoinColumn(name="CREATE_EMPLOYEE_NUMBER", referencedColumnName = "EMPLOYEE_NUMBER", insertable=false, updatable=false)
    private ClaimEmployee createEmployee;


    public ClaimMessageType getClaimMessageType() {
        return claimMessageType;
    }

    public void setClaimMessageType(ClaimMessageType claimMessageType) {
        this.claimMessageType = claimMessageType;
    }

    public String getMessage() {
        return message;
    }

    public void setMessage(String message) {
        this.message = message;
    }

    @JsonSerialize(using=JsonDateSerializer.class)
    public Date getCreationDateTime() {
        return creationDateTime;
    }

    @JsonDeserialize(using = JsonDateDeSerializer.class)
    public void setCreationDateTime(Date creationDateTime) {
        this.creationDateTime = creationDateTime;
    }

    public Integer getAcknowledgementEmployeeNumber() {
        return acknowledgementEmployeeNumber;
    }

    public void setAcknowledgementEmployeeNumber(Integer acknowledgementEmployeeNumber) {
        this.acknowledgementEmployeeNumber = acknowledgementEmployeeNumber;
    }

    @JsonSerialize(using=JsonDateSerializer.class)
    public Date getAcknowledgementDateTime() {
        return acknowledgementDateTime;
    }

    @JsonDeserialize(using = JsonDateDeSerializer.class)
    public void setAcknowledgementDateTime(Date acknowledgementDateTime) {
        this.acknowledgementDateTime = acknowledgementDateTime;
    }

    /**
     * @return the claimMessageId
     */
    public Long getClaimMessageId() {
        return claimMessageId;
    }

    /**
     * @param claimMessageId the claimMessageId to set
     */
    public void setClaimMessageId(Long claimMessageId) {
        this.claimMessageId = claimMessageId;
    }

    public Long getClaimId() {
        return claimId;
    }

    public void setClaimId(Long claimId) {
        this.claimId = claimId;
    }

    public Integer getMessageCount() {
        return messageCount;
    }

    public void setMessageCount(Integer messageCount) {
        this.messageCount = messageCount;
    }

    public ClaimEmployee getAcknowledgementEmployee() {
        return acknowledgementEmployee;
    }

    public void setAcknowledgementEmployee(ClaimEmployee acknowledgementEmployee) {
        this.acknowledgementEmployee = acknowledgementEmployee;
    }

    public Integer getRequirementId() {
        return requirementId;
    }

    public void setRequirementId(Integer requirementId) {
        this.requirementId = requirementId;
    }

    public Requirement getRequirement() {
        return requirement;
    }

    public void setRequirement(Requirement requirement) {
        this.requirement = requirement;
    }

    public ClaimEmployee getCreateEmployee() {
        return createEmployee;
    }

    public void setCreateEmployee(ClaimEmployee createEmployee) {
        this.createEmployee = createEmployee;
    }

    public Integer getCreateEmployeeNumber() {
        return createEmployeeNumber;
    }

    public void setCreateEmployeeNumber(Integer createEmployeeNumber) {
        this.createEmployeeNumber = createEmployeeNumber;
    }

    @Override
    public boolean equals(Object o) {
        if (this == o) return true;
        if (o == null || getClass() != o.getClass()) return false;

        ClaimMessage that = (ClaimMessage) o;

        if (claimMessageId != null ? !claimMessageId.equals(that.claimMessageId) : that.claimMessageId != null)
            return false;
        if (messageCount != null ? !messageCount.equals(that.messageCount) : that.messageCount != null) return false;

        return true;
    }

    @Override
    public int hashCode() {
        int result = claimMessageId != null ? claimMessageId.hashCode() : 0;
        result = 31 * result + (messageCount != null ? messageCount.hashCode() : 0);
        return result;
    }
}
