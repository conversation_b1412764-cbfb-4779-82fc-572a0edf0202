package com.smilebrands.liberty.constants;

/**
 * Created with IntelliJ IDEA.
 * User: danta
 * Date: 1/17/13
 * Time: 2:05 PM
 * To change this template use File | Settings | File Templates.
 */
public enum ReferralType {

    CONSULT("Consult") {
        @Override
        public String getDescription() {
            return "Consultation only requested";
        }
    },
    TREATMENT("Treatment"){
        @Override
        public String getDescription() {
            return "Treatment execution and completion";
        }
    },
    CONSULT_TREATMENT("Consult & Treatment") {
        @Override
        public String getDescription() {
            return "Both Consultation and treatment execution";
        }
    };

    private final String strValue;

    private ReferralType(String strValue) {
        this.strValue = strValue;
    }

    public String getDescription() {
        return "";
    }

    @Override
    public  String toString() {
        return this.strValue;
    }
}
