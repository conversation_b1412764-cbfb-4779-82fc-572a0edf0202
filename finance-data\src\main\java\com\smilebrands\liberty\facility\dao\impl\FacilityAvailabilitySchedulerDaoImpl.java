package com.smilebrands.liberty.facility.dao.impl;

import com.smilebrands.liberty.facility.dao.FacilityAvailabilitySchedulerDao;
import com.smilebrands.liberty.calendar.dao.OccurrenceConfigurationDao;
import com.smilebrands.liberty.dao.AbstractBaseDao;
import com.smilebrands.liberty.utility.CalendarUtil;
import java.util.ArrayList;
import java.util.Arrays;
import java.util.Calendar;
import java.util.HashSet;
import java.util.List;
import java.util.Set;
import javax.persistence.Query;

import com.smilebrands.liberty.facility.model.FacilityAvailability;
import com.smilebrands.liberty.facility.model.FacilityOccurrence;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Qualifier;
import org.springframework.stereotype.Repository;

/**
 *
 * <AUTHOR>
 *
 * This class builds and validates Facility availability
 * based on configurations.
 *
 */
@Repository
public class FacilityAvailabilitySchedulerDaoImpl extends AbstractBaseDao implements FacilityAvailabilitySchedulerDao {

    final Logger logger = LoggerFactory.getLogger(FacilityAvailabilitySchedulerDaoImpl.class);
    @Autowired
    @Qualifier("OccurrenceConfigurationDao")
    protected OccurrenceConfigurationDao configDao;

    /*
     * Set of entity classes managed by this DAO. Typically a DAO manages a single entity.
     */
    private final static Set<Class<?>> dataTypes = new HashSet<Class<?>>(Arrays.asList(new Class<?>[]{FacilityAvailability.class}));

    /**
     * Returns the set of entity classes managed by this DAO.
     */
    @Override
    public Set<Class<?>> getTypes() {
        return dataTypes;
    }

    /**
     * Rebuild Facility Availability (FACILITY_AVAIL) using active
     * configurations found in FACILITY_OCCURRENCE_CFG.  Each active
     * occurrence instance is converted to into many availability
     * instances.
     *
     * @param   Integer facilityId
     */
    @Override
    public void rebuildFacilityAvailability(Integer facilityId) {
        // -- collect active configs
        List<FacilityOccurrence> occrs = configDao.getActiveFacilityOccurrence(facilityId);
        for (FacilityOccurrence occur : occrs) {
            rebuildFacilityAvailability(occur);
        }
    }

    /**
     * Rebuild Facility Availability (FACILITY_AVAIL) using active
     * configurations found in FACILITY_OCCURRENCE_CFG.  Each active
     * occurrence instance is converted to into many availability
     * instances.
     *
     * @param   FacilityOccurrence occurrence
     */
    @Override
    public void rebuildFacilityAvailability(FacilityOccurrence occurrence) {
        List<FacilityAvailability> avail = convert(occurrence);
        removeFacilityAvailability(occurrence.getConfigId());
        storeCollection(avail);

    }

    /**
     * Remove existing FacilityAvailability entities by configId.
     *
     * @param   Long configId
     */
    @Override
    public void removeFacilityAvailability(Long configId) {
        Query query = em.createNamedQuery("FacilityAvailability.removeById");
        query.setParameter(1, configId);
        query.executeUpdate();
    }

    /**
     * Convert a Facility's configuration into availability.  Logic for
     * weekly and monthly entries are dissected here passed to the appropriate
     * conversion method.
     *
     * @param   FacilityOccurrence occur
     * @return  List<FacilityAvailability>
     */
    protected List<FacilityAvailability> convert(FacilityOccurrence occur) {
        if (occur.getOccurrenceType() == 0) {
            return convertWeekly(occur);

        } else if (occur.getOccurrenceType() == 1) {
            return convertMonthly(occur);

        } else {
            logger.warn("Unknown Occurrence Type[" + occur.getOccurrenceType() + "]! ");
            throw new RuntimeException("Unknown Occurrence Type["
                    + occur.getOccurrenceType() + "]! ");
        }
    }

    /**
     * Converts a weekly configuration into a collection of availability instances.
     *
     * Rules:
     *  OCCUR_FREQUENCY - 1=every week, 2=every other week
     *  DAY_OF_WEEK 	- 0=Sunday, 1=Monday, ...
     *  DURATION 	- #=repeats # number of weeks
     *  OCCR_TYPE       - always W
     *
     * @param   FacilityOccurrence occur
     * @return  List<FacilityAvailability>
     */
    protected List<FacilityAvailability> convertWeekly(FacilityOccurrence occur) {
        List<FacilityAvailability> avail = new ArrayList<FacilityAvailability>();

        // -- add 1, JavaScript Day of Week [] is 0 based, Java Calendar is 1 based.
        int dayOfWeek = occur.getDayOfWeek() + 1;

        logger.info("Weekly Facility Occurrence: "
                + "ID=" + occur.getFacilityId()
                + ", Freq=" + occur.getOccurrenceFrequency()
                + ", Day=" + dayOfWeek
                + ", Dura=" + occur.getDuration());

        // -- calculate number of weeks to repeat.
        int interval = 52;
        if (occur.getDuration() != null && occur.getDuration() > 0) {
            interval = occur.getDuration();
        }

        // -- calculate frequency
        int frequency = 7;
        if (occur.getOccurrenceFrequency() != null && occur.getOccurrenceFrequency() > 0) {
            frequency = frequency * occur.getOccurrenceFrequency();
        }

        Calendar cal = CalendarUtil.getNextOccurenceOfADay(dayOfWeek);
        logger.debug("Availability Start Date: " + cal.getTime() + " with frequency["
                + frequency + "] and interval[" + interval + "]");

        // -- iterate number of intervals.
        for (int x = 0; x < interval; x++) {
            FacilityAvailability fa = populate(occur);
            fa.setDay(cal.get(cal.DAY_OF_MONTH));

            // -- add 1, Java Calendar Month is 0 based.
            fa.setMonth(cal.get(cal.MONTH) + 1);
            fa.setYear(cal.get(cal.YEAR));
            avail.add(fa);

            // -- updated for the next iteration
            cal = CalendarUtil.updateCalendar(cal, frequency);
        }

        logger.info(avail.size() + " weekly records have been created for Facility "
                + occur.getFacilityId() + " and day " + dayOfWeek
                + " of the week.");
        return avail;
    }

    /**
     * Converts a monthly configuration into a collection of availability instances.
     *
     * * Rules:
     *  OCCUR_FREQUENCY - 1=first occur of the month, 2=second occur ...
     *  DAY_OF_WEEK 	- 0=Sunday, 1=Monday, ...
     *  DURATION 	- #=repeats # number of months
     *  OCCR_TYPE       - always M
     *
     * @param   FacilityOccurrence occur
     * @return  List<FacilityAvailability>
     */
    protected List<FacilityAvailability> convertMonthly(FacilityOccurrence occur) {
        List<FacilityAvailability> avail = new ArrayList<FacilityAvailability>();

        // -- add 1, JavaScript Day of Week [] is 0 based, Java Calendar is 1 based.
        int dayOfWeek = occur.getDayOfWeek() + 1;

        logger.info("Monthly Facility Occurrence: "
                + "ID=" + occur.getFacilityId()
                + ", Freq=" + occur.getOccurrenceFrequency()
                + ", Day=" + dayOfWeek
                + ", Dura=" + occur.getDuration());

        // -- calculate number of weeks to repeat.
        int interval = 12;
        if (occur.getDuration() != null && occur.getDuration() > 0) {
            interval = occur.getDuration();
        }

        // -- calculate frequency
        int frequency = 1;
        if (occur.getOccurrenceFrequency() != null && occur.getOccurrenceFrequency() > 0) {
            frequency = occur.getOccurrenceFrequency();
        }

        Calendar cal = Calendar.getInstance();
        cal = CalendarUtil.getNextOccurenceOfADay(cal, frequency, dayOfWeek);
        logger.debug("Availability Start Date: " + cal.getTime() + " with frequency["
                + frequency + "] and interval[" + interval + "]");

        // -- iterate number of intervals.
        for (int x = 0; x < interval; x++) {
            FacilityAvailability fa = populate(occur);
            fa.setConfigId(occur.getConfigId());
            fa.setDay(cal.get(cal.DAY_OF_MONTH));

            // -- add 1, Java Calendar Month is 0 based.
            fa.setMonth(cal.get(cal.MONTH) + 1);
            fa.setYear(cal.get(cal.YEAR));
            avail.add(fa);

            // -- updated for the next iteration
            cal = CalendarUtil.updateCalendarMonth(cal, 1);
            cal = CalendarUtil.getNextOccurenceOfADay(cal, frequency, dayOfWeek);
        }

        logger.info(avail.size() + " monthly records have been created for Facility "
                + occur.getFacilityId() + " and day " + dayOfWeek
                + " of the week.");
        return avail;
    }

    /**
     * Pre-populates a FacilityAvailability instance from a FacilityOccurrence
     *
     * @param   FacilityOccurrence occur
     * @return  FacilityAvailability
     */
    private FacilityAvailability populate(FacilityOccurrence occur) {
        FacilityAvailability fa = new FacilityAvailability();
        fa.setFacilityId(occur.getFacilityId());
        fa.setConfigId(occur.getConfigId());
        fa.setDay(occur.getDayOfWeek());
        fa.setStartHour(occur.getStartHour());
        fa.setEndHour(occur.getEndHour());

        return fa;
    }
}
