--------------------------------------------------------
--  DDL for Table CDT_CODES
--------------------------------------------------------

  CREATE TABLE "LIBERTY_DATA"."CDT_CODES"
   (	"CDT_CODE" VARCHAR2(5 BYTE), 
	"CDT_DESCRIPTION" VARCHAR2(100 BYTE), 
	"SORT_SEQUENCE" NUMBER(5,0), 
	"QSI_ADA_CODE" CHAR(6 BYTE), 
	"IS_XRAY" CHAR(1 BYTE), 
	"NEXT_VISIT_WARNING_TYPE" CHAR(1 BYTE), 
	"NEXT_VISIT_VALUE" NUMBER(5,0), 
	"TOOTH_NBR_RESTRICTION" CHAR(1 BYTE), 
	"AREA_REQUIRED" CHAR(1 BYTE), 
	"SURFACE_RESTRICTION" CHAR(1 BYTE), 
	"CHILD_RESTRICTION" CHAR(1 BYTE), 
	"FROM_AGE" NUMBER(3,0), 
	"TO_AGE" NUMBER(3,0), 
	"FREQUENCY_WARNING_TYPE" CHAR(1 BYTE), 
	"FREQUENCY" NUMBER(5,0), 
	"BENEFIT_CATEGORY_ID" NUMBER(5,0), 
	"CROWN_BRIDGE_PARTIAL" CHAR(1 BYTE), 
	"PROMPT_FLEXITE" CHAR(1 BYTE), 
	"CREATE_EMPLOYEE" NUMBER(6,0), 
	"CREATE_TIMESTAMP" TIMESTAMP (0), 
	"UPDATE_EMPLOYEE" NUMBER(6,0), 
	"UPDATE_TIMESTAMP" TIMESTAMP (0)
   ) PCTFREE 10 PCTUSED 40 INITRANS 1 MAXTRANS 255 NOCOMPRESS LOGGING
  STORAGE(INITIAL 65536 NEXT 1048576 MINEXTENTS 1 MAXEXTENTS 2147483645
  PCTINCREASE 0 FREELISTS 1 FREELIST GROUPS 1 BUFFER_POOL DEFAULT)
  TABLESPACE "BNDNEW_DATA" ;
