package com.smilebrands.liberty.billing.dao;

import com.smilebrands.liberty.billing.model.PendingPatientLedger;

import java.util.List;

/**
 * Created with IntelliJ IDEA.
 * User: phongpham
 * Date: 12/6/13
 * Time: 2:42 PM
 * To change this template use File | Settings | File Templates.
 */
public interface PendingPatientLedgerRepository {

    List<PendingPatientLedger> getTransactionValidationByFacility(Integer facilityId);
}
