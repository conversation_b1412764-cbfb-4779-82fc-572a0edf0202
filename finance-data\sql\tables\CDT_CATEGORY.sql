--------------------------------------------------------
--  DDL for Table CDT_CATEGORY
--------------------------------------------------------

  CREATE TABLE "LIBERTY_DATA"."CDT_CATEGORY"
   (	"SERVICE_CATEGORY_ID" NUMBER(10,0), 
	"PARENT_CATEGORY_ID" NUMBER(10,0), 
	"CATEGORY_TYPE" NUMBER(2,0), 
	"SERVICE_CATEGORY_DESCRIPTION" VARCHAR2(50 BYTE), 
	"SORT_SEQUENCE" NUMBER(4,0), 
	"CDT_CODE_FROM_RANGE" VARCHAR2(5 BYTE), 
	"CDT_CODE_TO_RANGE" CHAR(5 BYTE), 
	"CREATE_DATETIME" TIMESTAMP (0), 
	"CREATE_EMPLOYEE" NUMBER(6,0), 
	"UPDATE_DATETIME" TIMESTAMP (0), 
	"UPDATE_EMPLOYEE" NUMBER(6,0)
   ) PCTFREE 10 PCTUSED 40 INITRANS 1 MAXTRANS 255 NOCOMPRESS LOGGING
  STORAGE(INITIAL 65536 NEXT 1048576 MINEXTENTS 1 MAXEXTENTS 2147483645
  PCTINCREASE 0 FREELISTS 1 FREELIST GROUPS 1 BUFFER_POOL DEFAULT)
  TABLESPACE "BNDNEW_DATA" ;
