package com.smilebrands.liberty.billing.dao.impl;

import com.smilebrands.liberty.billing.dao.PatientAccountBalanceDao;
import com.smilebrands.liberty.billing.model.PatientAccountBalance;
import com.smilebrands.liberty.dao.SimpleAbstractDao;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Qualifier;
import org.springframework.jdbc.core.JdbcTemplate;
import org.springframework.jdbc.core.RowMapper;
import org.springframework.stereotype.Repository;

import java.sql.ResultSet;
import java.sql.SQLException;


/**
 * <AUTHOR>
 * @version 03/07/2014
 *
 * Simple DAO to get patient account balance info wo/ having to use full hibernate entities
 */
@Repository
public class PatientAccountBalanceDaoImpl extends SimpleAbstractDao implements PatientAccountBalanceDao {

    @Autowired
    @Qualifier("libertyJdbcTemplate")
    private JdbcTemplate jdbcTemplate;

    @Autowired
    @Qualifier("patientBalancesSql")
    private String patientBalancesSql;


    @Override
    public PatientAccountBalance getPatientAccountBalance(Long patientId) {
        return (PatientAccountBalance) jdbcTemplate.queryForObject(patientBalancesSql, new Object[] {patientId}, new PatientAccountBalanceRowMapper());
    }


    class PatientAccountBalanceRowMapper implements RowMapper {

        @Override
        public Object mapRow(ResultSet rs, int i) throws SQLException {
            PatientAccountBalance pab = new PatientAccountBalance();
            pab.setPatientBalance(rs.getBigDecimal("PATIENT_AMOUNT"));
            pab.setPrimaryBalance(rs.getBigDecimal("INSURANCE_PRIMARY"));
            pab.setSecondaryBalance(rs.getBigDecimal("INSURANCE_SECONDARY"));
            return pab;
        }

    }

}