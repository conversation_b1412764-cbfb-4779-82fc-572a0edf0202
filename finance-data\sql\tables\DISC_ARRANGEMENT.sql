--------------------------------------------------------
--  DDL for Table DISC_ARRANGEMENT
--------------------------------------------------------

  CREATE TABLE "LIBERTY_DATA"."DISC_ARRANGEMENT"
   (	"ARRANGEMENT_ID" NUMBER(10,0), 
	"ARRANGEMENT_DESCRIPTION" VARCHAR2(30 BYTE), 
	"EFFECTIVE_DATE" DATE, 
	"INACTIVE_DATE" DATE, 
	"ARRANGEMENT_TYPE" CHAR(1 BYTE), 
	"IS_STANDALONE" CHAR(1 BYTE), 
	"TOTAL_SERVICE_CHARGE" NUMBER(9,2), 
	"DISCOUNT_PERCENT" NUMBER(5,4), 
	"DISCOUNT_AMOUNT" CHAR(10 BYTE), 
	"MINIMUM_CHARGE_AMT" NUMBER(9,2), 
	"MAXIMUM_DISCOUNT" NUMBER(9,2), 
	"ARRANGEMENT_ADJ_CODE" NUMBER(7,0), 
	"CREATE_EMPLOYEE" NUMBER(10,0), 
	"CREATE_DATETIME" TIMESTAMP (6), 
	"UPDATE_EMPLOYEE" NUMBER(10,0), 
	"UPDATE_DATETIME" TIMESTAMP (6)
   ) PCTFREE 10 PCTUSED 40 INITRANS 1 MAXTRANS 255 NOCOMPRESS LOGGING
  STORAGE(INITIAL 65536 NEXT 1048576 MINEXTENTS 1 MAXEXTENTS 2147483645
  PCTINCREASE 0 FREELISTS 1 FREELIST GROUPS 1 BUFFER_POOL DEFAULT)
  TABLESPACE "BNDNEW_DATA" ;
