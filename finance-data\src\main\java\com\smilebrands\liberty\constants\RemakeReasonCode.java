package com.smilebrands.liberty.constants;

/**
 * Created using IntelliJ IDEA.
 * User: <PERSON><PERSON><PERSON>
 * Date: 4/24/12
 * Time: 11:35 AM
 */
public enum RemakeReasonCode {

    DOESNOTFIT("Does Not Fit") {

        @Override
        public String getDescription() {
            return "Does Not Fit";
        }
    },
    WRONGSHADE("Wrong Shade") {

        @Override
        public String getDescription() {
            return "Wrong Shade";
        }
    },
    DEFFECTIVE("Deffective/Not Accepted By Provider") {

        @Override
        public String getDescription() {
            return "Deffective/Not Accepted By Provider";
        }
    },
    OTHER("Other") {

        @Override
        public String getDescription() {
            return "Other";
        }
    },
    UNKNOWN("Unknown") {

        @Override
        public String getDescription() {
            return "Unknown";
        }
    };

    private final String strValue;

    private RemakeReasonCode(final String strValue) {
        this.strValue = strValue;
    }

    public String getDescription() {
        return "";
    }

    @Override
    public String toString() {
        return this.strValue;
    }

}
