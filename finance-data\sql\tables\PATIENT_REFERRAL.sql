--------------------------------------------------------
--  DDL for Table PATIENT_REFERRAL
--------------------------------------------------------

  CREATE TABLE "LIBERTY_DATA"."PATIENT_REFERRAL"
   (	"REFERRAL_ID" NUMBER(10,0), 
	"PATIENT_ID" NUMBER(10,0), 
	"FACILITY_ID" NUMBER(8,0), 
	"SERVICE_PROVIDER_ID" NUMBER(8,0), 
	"TEETH_ASSOCIATION" VARCHAR2(150 BYTE), 
	"REFERRAL_TYPE" VARCHAR2(30 BYTE), 
	"REFERRAL_SPECIALIST" VARCHAR2(30 BYTE), 
	"CREATE_EMPLOYEE_NUMBER" NUMBER(6,0), 
	"CREATE_DATETIME" TIMESTAMP (6), 
	"NOTE" VARCHAR2(2000 BYTE), 
	"UPDATE_EMPLOYEE_NUMBER" NUMBER(6,0), 
	"UPDATE_PROVIDER_ID" NUMBER(8,0), 
	"UPDATE_DATETIME" TIMESTAMP (6), 
	"INACTIVATION_EMPLOYEE_NUMBER" NUMBER(6,0), 
	"INACTIVATION_PROVIDER_ID" NUMBER(8,0), 
	"INACTIVATION_DATETIME" TIMESTAMP (6)
   ) PCTFREE 10 PCTUSED 40 INITRANS 1 MAXTRANS 255 NOCOMPRESS LOGGING
  STORAGE(INITIAL 65536 NEXT 1048576 MINEXTENTS 1 MAXEXTENTS **********
  PCTINCREASE 0 FREELISTS 1 FREELIST GROUPS 1 BUFFER_POOL DEFAULT)
  TABLESPACE "BNDNEW_DATA" ;
