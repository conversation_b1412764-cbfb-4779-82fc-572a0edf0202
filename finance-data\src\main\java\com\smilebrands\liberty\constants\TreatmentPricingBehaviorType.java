package com.smilebrands.liberty.constants;

/**
 * Created with IntelliJ IDEA.
 * User: danta
 * Date: 7/15/13
 * Time: 9:38 AM
 * To change this template use File | Settings | File Templates.
 */
public enum TreatmentPricingBehaviorType {

    I("Insurance") {
        @Override
        public String getDescription() {
            return "Default payment behavior where the normal insurance and patient co-pay rules exists";
        }
    },
    E("Elective") {
        @Override
        public String getDescription () {
            return "All payment responsibility will be transferred to the patient responsibility. (Used for elective procedures)";
        }
    },
    D("Denied") {
        @Override
        public String getDescription () {
            return "All payment responsibility will be transferred to the patient due to denied prior authorization from insurance";
        }
    },
    P("Patient") {
        @Override
        public String getDescription () {
            return "All payment responsibility will be transferred to the patient responsibility. (Used for non covered benefit items and over annual max)";
        }
    },
    C("Cosmetic") {
        @Override
        public String getDescription () {
            return "All payment responsibility will be transferred to the patient responsibility. (Used for cosmetic enhancements and non covered benefit items)";
        }
    },
    R("Redo") {
        @Override
        public String getDescription () {
            return "Regardless of pricing calculations, both patient and insurance amounts will be overridden to zero due to redo TX";
        }
    },
    S("Customer Satisfaction") {
        @Override
        public String getDescription () {
            return "Regardless of pricing calculations, both patient and insurance amounts will be overridden to zero due to customer satisfaction";
        }
    },
    N("None") {
        @Override
        public String getDescription () {
            return "No behavior has been assigned to this treatment and thus requires pricing to be done upon it)";
        }
    },
    U("Unauthorized") {
        @Override
        public String getDescription () {
            return "Pricing moved towards patient until prior authorization results have been returned";
        }
    },
    V("Verified") {
        @Override
        public String getDescription () {
            return "Payer information has been verified and or established this Treatment eligible ";
        }
    },
    O("Overridden") {
        @Override
        public String getDescription () {
            return "Treatment pricing has been overridden";
        }
    },
    Z("Zero") {
        @Override
        public String getDescription () {
            return "Price treatment at zero dollars";
        }
    };

    private final String pricingBehavior;

    private TreatmentPricingBehaviorType(String pricingBehavior) {
        this.pricingBehavior = pricingBehavior;
    }

    public String getDescription() {
        return "";
    }

    public String toString() {
        return this.pricingBehavior;
    }
}
