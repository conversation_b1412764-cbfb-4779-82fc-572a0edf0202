package com.smilebrands.liberty.document.model;

import com.smilebrands.liberty.model.BaseObject;
import org.springframework.data.annotation.Id;
import org.springframework.data.mongodb.core.mapping.Document;


/**
 * User: <PERSON><PERSON>
 * Date: 9/12/13
 */
@Document
public class ControllerRequestArgument extends BaseObject {
    @Id
    private Long controllerRequestArgumentId;
    private String argumentName;
    private String argumentClassName;
    private Object value;
    private Integer stringValueLength;

    public Long getControllerRequestArgumentId() {
        return controllerRequestArgumentId;
    }

    public void setControllerRequestArgumentId(Long controllerRequestArgumentId) {
        this.controllerRequestArgumentId = controllerRequestArgumentId;
    }

    public String getArgumentName() {
        return argumentName;
    }

    public void setArgumentName(String argumentName) {
        this.argumentName = argumentName;
    }

    public String getArgumentClassName() {
        return argumentClassName;
    }

    public void setArgumentClassName(String argumentClassName) {
        this.argumentClassName = argumentClassName;
    }

    public Object getValue() {
        return value;
    }

    public void setValue(Object value) {
        this.value = value;
    }

    public Integer getStringValueLength() {
        return stringValueLength;
    }

    public void setStringValueLength(Integer stringValueLength) {
        this.stringValueLength = stringValueLength;
    }

    public Long generateId(Long controllerRequestId){
        int hashCode = controllerRequestId != null ? controllerRequestId.hashCode() : 0;
        hashCode += this.getArgumentName() != null ? this.getArgumentName().hashCode() : 0;
        hashCode += this.getArgumentClassName() != null ? this.getArgumentClassName().hashCode() : 0;
        hashCode += this.getValue() != null ? this.getValue().hashCode() : 0;
        return new Long(hashCode);
    }
}
