--------------------------------------------------------
--  DDL for Table GL_UPLOAD
--------------------------------------------------------

  CREATE TABLE "LIBERTY_DATA"."GL_UPLOAD"
   (	"GL_ID" NUMBER(11,0), 
	"FACILITY_ID" NUMBER(10,0), 
	"QSI_TRANS_CODE" NUMBER(10,0), 
	"QSI_CLINIC" NUMBER(5,0), 
	"GL_SOURCE" CHAR(1 BYTE), 
	"GL_ACCOUNT" VARCHAR2(20 BYTE), 
	"TRANSACTION_DESCRIPTION" VARCHAR2(40 BYTE), 
	"DEBIT_AMOUNT" NUMBER(9,2), 
	"CREDIT_AMOUNT" NUMBER(9,2), 
	"CLASSIFICATION" VARCHAR2(40 BYTE), 
	"GL_NAME" VARCHAR2(10 BYTE), 
	"IC_GL" VARCHAR2(10 BYTE)
   ) PCTFREE 10 PCTUSED 40 INITRANS 1 MAXTRANS 255 NOCOMPRESS LOGGING
  STORAGE(INITIAL 65536 NEXT 1048576 MINEXTENTS 1 MAXEXTENTS **********
  PCTINCREASE 0 FREELISTS 1 FREELIST GROUPS 1 BUFFER_POOL DEFAULT)
  TABLESPACE "BNDNEW_DATA" ;
