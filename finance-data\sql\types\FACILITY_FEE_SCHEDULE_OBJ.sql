--------------------------------------------------------
--  DDL for Type FACILITY_FEE_SCHEDULE_OBJ
--------------------------------------------------------

  CREATE OR REPLACE TYPE "LIBERTY_DATA"."FACILITY_FEE_SCHEDULE_OBJ" is object (

	FACILITY_FEE_SCHED_SEQ  	NUMBER(10),
    IS_UPGRADE_DOWNGRADE        CHAR(1),    
	FACILITY_ID             	NUMBER(10),
	INS_PLAN_ID             	NUMBER(10),
	INSURANCE_PLAN          	NUMBER(10),
	INS_COMPANY_ID          	NUMBER(10),
	IS_NON_COVERED          	CHAR(1),
    NON_COVERED_DISC            NUMBER(5,4),
	CDT_CODE                	VARCHAR2(5),
    PRODUCT_SERVICE_CODE        NUMBER(7,0),
	BENEFIT_CATEGORY        	NUMBER(5),
	BENEFIT_PERCENTAGE      	NUMBER(5,4),
	DEDUCTABLE_METHOD       	VARCHAR2(15),
	EFFECTIVE_DATE          	DATE,
	INACTIVE_DATE           	DATE,

    PAT_AMT_GENERAL             NUMBER(9,2),
    PAT_AMT_OS                  NUMBER(9,2),
    PAT_AMT_ORTHO               NUMBER(9,2),
    PAT_AMT_PEDO                NUMBER(9,2),
    PAT_AMT_ENDO                NUMBER(9,2),
    PAT_AMT_PERIO               NUMBER(9,2),

    UPG_AMT_GENERAL             NUMBER(9,2),
    UPG_AMT_OS                  NUMBER(9,2),
    UPG_AMT_ORTHO               NUMBER(9,2),
    UPG_AMT_PEDO                NUMBER(9,2),
    UPG_AMT_ENDO                NUMBER(9,2),
    UPG_AMT_PERIO               NUMBER(9,2),
    
    MOLAR_UPGRADE               NUMBER(9,2),
    METAL_LAB_UPGRADE           NUMBER(9,2),
    FLEXITE_UPGRADE             NUMBER(9,2),
    

    INS_AMT_GENERAL             NUMBER(9,2),
    INS_AMT_OS                  NUMBER(9,2),
    INS_AMT_ORTHO               NUMBER(9,2),
    INS_AMT_PEDO                NUMBER(9,2),
    INS_AMT_ENDO                NUMBER(9,2),
    INS_AMT_PERIO               NUMBER(9,2),

    GENERAL_INS                 NUMBER(9,2),
    OS_INS                      NUMBER(9,2),
    ORTHO_INS                   NUMBER(9,2),
    PEDO_INS                    NUMBER(9,2),
    ENDO_INS                    NUMBER(9,2),
    PERIO_INS                   NUMBER(9,2),

    GENERAL_MAX                 NUMBER(9,2),
    OS_MAX                      NUMBER(9,2),
    ORTHO_MAX                   NUMBER(9,2),
    PEDO_MAX                    NUMBER(9,2),
    ENDO_MAX                    NUMBER(9,2),
    PERIO_MAX                   NUMBER(9,2),

    GENERAL_CO_PAY              NUMBER(9,2),
    OS_CO_PAY                   NUMBER(9,2),
    ORTHO_CO_PAY                NUMBER(9,2),
    PEDO_CO_PAY                 NUMBER(9,2),
    ENDO_CO_PAY                 NUMBER(9,2),
    PERIO_CO_PAY                NUMBER(9,2),
    
    GENERAL_UCR                 NUMBER(9,2),
    OS_UCR                      NUMBER(9,2),
    ORTHO_UCR                   NUMBER(9,2),
    PEDO_UCR                    NUMBER(9,2),
    ENDO_UCR                    NUMBER(9,2),
    PERIO_UCR                   NUMBER(9,2),
    IS_COMPLEX_TREATMENT        CHAR(1),
    COMPLEX_TREAT_QTY           NUMBER(2,0),
    COMPLEX_CHG_PER_TOOTH       NUMBER(9,2),
 
    
    PROMPT_FOR_FLEXITE          CHAR(1),
	NEXT_VISIT_RESTRICTION  	CHAR(1),
	NEXT_VISIT              	NUMBER(3),
	AGE_RESTRICTION         	CHAR(1),
	FROM_AGE                	NUMBER(3),
	TO_AGE                  	NUMBER(3),
	FREQUENCE_RESTRICTION   	CHAR(1),
	FREQUENCY               	NUMBER(3),
	QTY_RESTRICTION         	CHAR(1),
	MAX_QTY                 	NUMBER(3),
	UPGRADE_DOWNGRADE       	CHAR(2),
	UP_DOWN_CDT_CODE        	VARCHAR2(5))

/
