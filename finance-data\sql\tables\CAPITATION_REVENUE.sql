--------------------------------------------------------
--  DDL for Table CAPITATION_REVENUE
--------------------------------------------------------

  CREATE TABLE "LIBERTY_DATA"."CAPITATION_REVENUE"
   (	"CAP_REVENUE_ID" NUMBER(11,0), 
	"BATCH_DETAIL_ID" NUMBER(11,0), 
	"FACILITY_ID" NUMBER(10,0), 
	"CAP_AMOUNT_PM_PM" NUMBER(9,2), 
	"ENCOUNTER_AMOUNT" NUMBER(9,2), 
	"OTHER_AMOUNT" NUMBER(9,2), 
	"SUPPLEMENTAL_AMOUNT" NUMBER(9,2), 
	"CAP_ADJUSTMENT" NUMBER(9,2), 
	"MEMBERSHIP" NUMBER(7,0), 
	"CLINIC_ID" NUMBER(10,0), 
	"TR_ID" NUMBER(10,0), 
	"CAP_YEAR" NUMBER(4,0), 
	"CAP_MONTH" NUMBER(2,0), 
	"GL_AUDIT_CODE" NUMBER(11,0), 
	"CREATE_EMPLOYEE" NUMBER(10,0), 
	"CREATE_DATETIME" TIMESTAMP (6), 
	"UPDATE_EMPLOYEE" NUMBER(10,0), 
	"UPDATE_DATETIME" TIMESTAMP (6)
   ) PCTFREE 10 PCTUSED 40 INITRANS 1 MAXTRANS 255 NOCOMPRESS LOGGING
  STORAGE(INITIAL 65536 NEXT 1048576 MINEXTENTS 1 MAXEXTENTS 2147483645
  PCTINCREASE 0 FREELISTS 1 FREELIST GROUPS 1 BUFFER_POOL DEFAULT)
  TABLESPACE "BNDNEW_DATA" ;
