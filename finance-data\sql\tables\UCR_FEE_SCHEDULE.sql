--------------------------------------------------------
--  DDL for Table UCR_FEE_SCHEDULE
--------------------------------------------------------

  CREATE TABLE "LIBERTY_DATA"."UCR_FEE_SCHEDULE"
   (	"UCR_FEE_SCHEDULE" NUMBER(10,0), 
	"FEE_SCHEDULE_DESCRIPTION" VARCHAR2(40 BYTE), 
	"FEE_SCHEDULE_TYPE" CHAR(1 BYTE), 
	"IS_ACTIVE" CHAR(1 BYTE), 
	"CREATE_EMPLOYEE" NUMBER(10,0), 
	"CREATE_DATETIME" TIMESTAMP (6), 
	"UPDATE_EMPLOYEE" NUMBER(10,0), 
	"UPDATE_TIMESTAMP" TIMESTAMP (6)
   ) PCTFREE 10 PCTUSED 40 INITRANS 1 MAXTRANS 255 NOCOMPRESS LOGGING
  STORAGE(INITIAL 65536 NEXT 1048576 MINEXTENTS 1 MAXEXTENTS 2147483645
  PCTINCREASE 0 FREELISTS 1 FREELIST GROUPS 1 BUFFER_POOL DEFAULT)
  TABLESPACE "BNDNEW_DATA" ;
