--------------------------------------------------------
--  DDL for Table INSURANCE_COMPANY
--------------------------------------------------------

  CREATE TABLE "LIBERTY_DATA"."INSURANCE_COMPANY"
   (	"INS_COMPANY_ID" NUMBER(10,0), 
	"INSURANCE_COMPANY_NAME" VARCHAR2(50 BYTE), 
	"CO_NETWORK_CODE" CHAR(1 BYTE), 
	"CREATE_EMPLOYEE" NUMBER(10,0), 
	"CREATE_DATETIME" TIMESTAMP (6), 
	"UPDATE_EMPLOYEE" NUMBER(10,0), 
	"UPDATE_DATETIME" TIMESTAMP (6)
   ) PCTFREE 10 PCTUSED 40 INITRANS 1 MAXTRANS 255 NOCOMPRESS LOGGING
  STORAGE(INITIAL 65536 NEXT 1048576 MINEXTENTS 1 MAXEXTENTS 2147483645
  PCTINCREASE 0 FREELISTS 1 FREELIST GROUPS 1 BUFFER_POOL DEFAULT)
  TABLESPACE "BNDNEW_DATA" ;
