--------------------------------------------------------
--  DDL for Type PLAN_PRICING_OBJ
--------------------------------------------------------

  CREATE OR REPLACE TYPE "LIBERTY_DATA"."PLAN_PRICING_OBJ" is object(
    PRICING_DETAIL_SEQ          NUMBER(10, 0),
    INS_PLAN_ID                 NUMBER(10, 0),
    INS_PLAN_VERSION            NUMBER(10, 0),
    ADA_CODE                    VARCHAR2(5),
    PRODUCT_SERVICES_CODE       NUMBER(7, 0),
    FEE_SCHEDULE_ID             NUMBER(10, 0),
    FEE_SCHEDULE_VERSION        NUMBER(10, 0),
    FEE_SCHEDULE_SEQUENCE       NUMBER(7, 0),
    TAXONOMY_CODE               VARCHAR2(10),
    B<PERSON><PERSON>IT_CATEGORY            NUMBER(5, 0),
    <PERSON><PERSON><PERSON><PERSON>_PERCENTAGE          NUMBER(5, 4),
    DEDUCTIBLE_TYPE             NUMBER(2,0),
    FAMILY_DEDUCTIBLE           NUMBER(9,2),
    INDIVIDUAL_DEDUCTIBLE       NUMBER(9,2),
    MAXIMUM_TYPE                NUMBER(2,0),
    INDIVIDUAL_MAX_AMT          NUMBER(9,2),
    FAMILY_MAX_AMT              NUMBER(9,2),
    ORTHO_MAX_AMOUNT            NUMBER(9,2),
    FROM_ZIPCODE                NUMBER(5, 0),
    TO_ZIPCODE                  NUMBER(5, 0),
    EFFECTIVE_DATE              DATE,
    TERM_DATE                   DATE,
    UCR_FEE                     NUMBER(7, 2),
    CASH_FEE                    NUMBER(7, 2),
    FEE_SCHED_AMOUNT            NUMBER(7, 2),
    INSURANCE_FEE               NUMBER(7, 2),
    MAXIMUM_ALLOWABLE_CHARGE    NUMBER(7, 2),
    PERCENTAGE_UCR              NUMBER(5, 4),
    LAB_FEE                     NUMBER(7, 2),
    CO_PAY                      NUMBER(7, 2),
    MC_SUPPLEMENTAL             NUMBER(7, 2),
    MC_SUPPLEMENTAL_CODE        CHAR(1),
    CHILD_RESTRICTION           CHAR(1),
    CHILD_AGE_LIMIT             NUMBER(2, 0),
    NEXT_VISIT_RESTRICTION      CHAR(1),
    NEXT_VISIT_VALUE            NUMBER(5, 0),
    FREQUENCY_RESTRICTION       CHAR(1),
    FREQUENCY_VALUE             NUMBER(5, 0),
    TOOTH_RESTRICTION           CHAR(1),
    SURFACE_RESTRICTION         CHAR(1),
    AREA_REQUIRED               CHAR(1),
    DOWNGRADE                   NUMBER(10, 0),
    UPGRADE                     NUMBER(10, 0))

/
