--------------------------------------------------------
--  DDL for Table TREATMENT_TYPE
--------------------------------------------------------

  CREATE TABLE "LIBERTY_DATA"."TREATMENT_TYPE"
   (	"TREATMENT_TYPE_ID" NUMBER(15,5), 
	"ADACODE" VARCHAR2(10 BYTE), 
	"TREATMENT_TYPE_NAME" VARCHAR2(200 BYTE), 
	"TREATMENT_TYPE_DESCRIPTION" VARCHAR2(200 BYTE), 
	"EVENT_DURATION_TIME_VALUE" NUMBER(15,5), 
	"DOCTOR_TIME_VALUE" NUMBER(15,5), 
	"TREATMENT_TYPE_CATEGORY" VARCHAR2(50 BYTE), 
	"REQUIRES_TEETH" CHAR(1 BYTE) DEFAULT '0', 
	"REQUIRES_SURFACE" CHAR(1 BYTE) DEFAULT '0', 
	"REQUIRES_QUAD" CHAR(1 BYTE) DEFAULT '0'
   ) PCTFREE 10 PCTUSED 40 INITRANS 1 MAXTRANS 255 NOCOMPRESS LOGGING
  STORAGE(INITIAL 65536 NEXT 1048576 MINEXTENTS 1 MAXEXTENTS 2147483645
  PCTINCREASE 0 FREELISTS 1 FREELIST GROUPS 1 BUFFER_POOL DEFAULT)
  TABLESPACE "BNDNEW_DATA" ;
