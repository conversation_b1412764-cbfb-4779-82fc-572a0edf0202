package com.smilebrands.liberty.billing.dao.impl;

import com.smilebrands.liberty.billing.dao.ArrangementCustom;
import com.smilebrands.liberty.dao.SimpleAbstractDao;

import java.math.BigDecimal;
import java.util.List;

/**
 * Created with IntelliJ IDEA.
 * User: phongpham
 * Date: 1/4/13
 * Time: 12:27 PM
 * To change this template use File | Settings | File Templates.
 */
public class ArrangementRepositoryImpl extends SimpleAbstractDao implements ArrangementCustom{
    @Override
    public Long getArrangementCorelation() {
        String query = "SELECT ARRANGEMENT_CORELATION_SEQ.NEXTVAL FROM DUAL";
        List<BigDecimal> list = em.createNativeQuery(query).getResultList();
        if(list.size() > 0){
            return list.get(0).longValue();
        }else{
            return 0L;
        }
    }
}
