package com.smilebrands.liberty.document.repo.impl;

import com.smilebrands.liberty.document.model.Facility;
import com.smilebrands.liberty.document.repo.PatientVisitDocumentRepositoryCustom;
import com.smilebrands.liberty.document.model.PatientVisit;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Qualifier;
import org.springframework.data.mongodb.core.MongoTemplate;
import org.springframework.data.mongodb.core.query.Query;

import java.util.Calendar;
import java.util.Date;
import java.util.GregorianCalendar;
import java.util.List;

import static org.springframework.data.mongodb.core.query.Criteria.where;

/**
 * User: <PERSON><PERSON>
 * Date: 8/21/13
 */
public class PatientVisitDocumentRepositoryImpl implements PatientVisitDocumentRepositoryCustom {

    @Autowired
    @Qualifier("mongoTemplate")
    protected MongoTemplate mongoTemplate;

    @Override
    public List<PatientVisit> findPatientVisitByFacilityDate(Facility facility, Date serviceDate) {
        Date start = cutTimeOffDate(serviceDate);
        Date end = add(start, 1);

        return mongoTemplate.find(new Query(where("facility").is(facility).and("dateOfService").gte(start).lte(end)), PatientVisit.class);
    }

    private Date cutTimeOffDate(Date date) {
        Calendar calendar = new GregorianCalendar();

        calendar.setTime(date);
        calendar.set(Calendar.HOUR_OF_DAY, 0);
        calendar.set(Calendar.MINUTE, 0);
        calendar.set(Calendar.SECOND, 0);
        calendar.set(Calendar.MILLISECOND, 0);

        return calendar.getTime();
    }

    private Date add(Date date, int days) {
        Calendar calendar = new GregorianCalendar();
        calendar.setTime(date);
        calendar.add(Calendar.DATE, days);
        return calendar.getTime();
    }
}
