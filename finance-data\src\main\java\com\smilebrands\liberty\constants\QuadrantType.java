package com.smilebrands.liberty.constants;

/**
 * Created with IntelliJ IDEA.
 * User: danta
 * Date: 1/5/13
 * Time: 10:04 AM
 * To change this template use File | Settings | File Templates.
 */
public enum QuadrantType {

    UA("Upper Arch"){
        @Override
        public String getDescription() {
            return "Covers Teeth 1-16";
        }
    },
    UL("Upper Left"){
        @Override
        public String getDescription() {
            return "Covers Teeth 9-16";
        }
    },
    LA("Lower Arch"){
        @Override
        public String getDescription() {
            return "Covers Teeth 17-32";
        }
    },
    LL("Lower Left"){
        @Override
        public String getDescription() {
            return "Covers Teeth 17-24";
        }
    },
    UR("Upper Right"){
        @Override
        public String getDescription() {
            return "Covers Teeth 1-8";
        }
    },
    LR("Lower Right"){

        @Override
        public String getDescription() {
            return "Covers Teeth 25-32";
        }
    },
    BOTTOM_LEFT("Bottom Left"){
        @Override
        public String getDescription() {
            return "Same as LL enum type";
        }
    },
    BOTTOM_RIGHT("Bottom Right"){
        @Override
        public String getDescription() {
            return "Same as LR enum type";
        }
    },
    TOP_LEFT("Top Left"){
        @Override
        public String getDescription() {
            return "Same as UL enum type";
        }
    },
    TOP_RIGHT("Top Right"){
        @Override
        public String getDescription() {
            return "Same as UR enum type";
        }
    };

    private final String strType;

    private QuadrantType(String strType) {
        this.strType = strType;
    }

    public String getDescription() {
        return "";
    }

    @Override
    public String toString() {
        return this.strType;
    }

}
