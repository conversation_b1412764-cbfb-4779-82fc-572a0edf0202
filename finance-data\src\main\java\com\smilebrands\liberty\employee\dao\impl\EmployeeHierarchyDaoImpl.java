package com.smilebrands.liberty.employee.dao.impl;

import com.smilebrands.liberty.employee.dao.EmployeeHierarchyDao;
import com.smilebrands.liberty.employee.model.Employee;
import java.sql.ResultSet;
import java.sql.SQLException;
import java.util.ArrayList;
import java.util.List;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Qualifier;
import org.springframework.dao.DataAccessException;
import org.springframework.jdbc.core.JdbcTemplate;
import org.springframework.jdbc.core.RowMapper;
import org.springframework.stereotype.Repository;

/**
 * JDBC Dao used to gather the management hierarch info for given facility.
 *
 * <AUTHOR> Smile Brands Incs., 2011
 */
@Repository(value = "employeeHierarchyDao")
public class EmployeeHierarchyDaoImpl implements EmployeeHierarchyDao {

    final Logger logger = LoggerFactory.getLogger(EmployeeHierarchyDaoImpl.class);

    @Autowired
    @Qualifier("libertyJdbcTemplate")
    protected JdbcTemplate jdbcTemplate;    
    
    @Autowired
    @Qualifier("facilityMgmtHierarchySql")
    protected String facilityMgmtHierarchySql;

    @Autowired
    @Qualifier("structureMgmtHierarchySql")
    protected String structureMgmtHierarchySql;

    @Autowired
    @Qualifier("facilityOfficeManagerSql")
    protected String facilityOfficeManagerSql;

    @Autowired
    @Qualifier("facilityDistrictDirectorSql")
    protected String facilityDistrictDirectorSql;

    @Autowired
    @Qualifier("facilityAreaVPSql")
    protected String facilityAreaVPSql;

    @Override
    public List<Employee> getEmployeeHierarchy(int facilityId) throws DataAccessException {
        int employeeId = 0;
        List<Employee> results = new ArrayList<Employee>();

        // Look for OM
        results = getOfficeManager(facilityId);

        if (results.isEmpty()) {
            logger.warn("Office Manager was not found for facility id: " + facilityId);

            // Look for DDO
            results = getDdo(facilityId);
            if (results.isEmpty()) {
                logger.warn("District Director was not found for facility id: " + facilityId);

                // Look for AVP
                results = getAvp(facilityId);
                if (results.isEmpty()) {
                    logger.warn("Unable to find an OM, DMO or AVP for facility id: " + facilityId);
                } else {
                    employeeId = results.get(0).getEmployeeNumber();
                }
            } else {
                employeeId = results.get(0).getEmployeeNumber();
            }
        } else {
            employeeId = results.get(0).getEmployeeNumber();
        }

        results = jdbcTemplate.query(facilityMgmtHierarchySql, new FacilityMgmtRowMapper(), new Object[]{employeeId});

        return results;

    }

    @Override
    public List<Employee> getOfficeManager(int facilityId) throws DataAccessException {
        logger.debug("Searching for Office Manager for facility id: " + facilityId);
        return jdbcTemplate.query(facilityOfficeManagerSql, new FacilityMgmtRowMapper(), new Object[]{facilityId});
    }

    @Override
    public List<Employee> getDdo(int facilityId) throws DataAccessException {
        logger.debug("Searching for District Director for facility id: " + facilityId);
        return jdbcTemplate.query(facilityDistrictDirectorSql, new FacilityMgmtRowMapper(), new Object[]{facilityId});
    }

    @Override
    public List<Employee> getAvp(int facilityId) throws DataAccessException {
        logger.debug("Searching for Area VP for facility id: " + facilityId);
        return jdbcTemplate.query(facilityAreaVPSql, new FacilityMgmtRowMapper(), new Object[]{facilityId});
    }

    class FacilityMgmtRowMapper implements RowMapper {

        public Object mapRow(ResultSet rs, int i) throws SQLException {
            Employee em = new Employee();
            em.setEmployeeNumber(rs.getInt("EMPLOYEE_NUMBER"));
            em.setFirstName(rs.getString("FIRST_NAME"));
            em.setLastName(rs.getString("LAST_NAME"));
            em.setTitle(rs.getString("TITLE"));
            em.setEmailAddress(rs.getString("EMAIL_ADDRESS"));
            return em;
        }
    }
}
