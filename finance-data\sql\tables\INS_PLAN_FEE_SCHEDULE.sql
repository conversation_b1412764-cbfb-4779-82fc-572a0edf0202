--------------------------------------------------------
--  DDL for Table INS_PLAN_FEE_SCHEDULE
--------------------------------------------------------

  CREATE TABLE "LIBERTY_DATA"."INS_PLAN_FEE_SCHEDULE"
   (	"INSURANCE_PLAN" NUMBER(10,0), 
	"INS_PLAN_ID" NUMBER(10,0), 
	"INS_COMPANY_ID" NUMBER(10,0), 
	"EFFECTIVE_DATE" DATE, 
	"INACTIVE_DATE" DATE, 
	"FROM_ZIPCODE" NUMBER(5,0), 
	"TO_ZIPCODE" NUMBER(5,0), 
	"CDT_CODE" VARCHAR2(5 BYTE), 
	"DEDUCTABLE_METHOD" VARCHAR2(15 BYTE), 
	"BENEFIT_CATEGORY" NUMBER(5,0), 
	"BENEFIT_PERCENT" NUMBER(5,4), 
	"GENERAL_MAX" NUMBER(9,2), 
	"OS_MAX" NUMBER(9,2), 
	"ORTHO_MAX" NUMBER(9,2), 
	"PEDO_MAX" NUMBER(9,2), 
	"ENDO_MAX" NUMBER(9,2), 
	"PERIO_MAX" NUMBER(9,2), 
	"GENERAL_INS" NUMBER(9,2), 
	"OS_INS" NUMBER(9,2), 
	"ORTHO_INS" NUMBER(9,2), 
	"PEDO_INS" NUMBER(9,2), 
	"ENDO_INS" NUMBER(9,2), 
	"PERIO_INS" NUMBER(9,2), 
	"GENERAL_CO_PAY" NUMBER(9,2), 
	"OS_CO_PAY" NUMBER(9,2), 
	"ORTHO_CO_PAY" NUMBER(9,2), 
	"PEDO_CO_PAY" NUMBER(9,2), 
	"ENDO_CO_PAY" NUMBER(9,2), 
	"PERIO_CO_PAY" NUMBER(9,2), 
	"DETAIL_STATUS" CHAR(1 BYTE), 
	"CREATE_EMPLOYEE" NUMBER(10,0), 
	"CREATE_DATETIME" TIMESTAMP (6)
   ) PCTFREE 10 PCTUSED 40 INITRANS 1 MAXTRANS 255 NOCOMPRESS LOGGING
  STORAGE(INITIAL 65536 NEXT 1048576 MINEXTENTS 1 MAXEXTENTS 2147483645
  PCTINCREASE 0 FREELISTS 1 FREELIST GROUPS 1 BUFFER_POOL DEFAULT)
  TABLESPACE "BNDNEW_DATA" ;
