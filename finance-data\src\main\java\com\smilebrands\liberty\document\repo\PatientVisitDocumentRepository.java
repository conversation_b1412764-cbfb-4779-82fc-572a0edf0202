package com.smilebrands.liberty.document.repo;

import com.smilebrands.liberty.document.model.Facility;
import com.smilebrands.liberty.document.model.PatientVisit;
import org.springframework.data.mongodb.repository.MongoRepository;

import java.util.List;

/**
 * User: <PERSON><PERSON>
 * Date: 8/21/13
 */
public interface PatientVisitDocumentRepository extends MongoRepository<PatientVisit, Long>, PatientVisitDocumentRepositoryCustom {

    public List<PatientVisit> findPatientVisitByPatientId(Long patientId);

    public List<PatientVisit> findPatientVisitByFacility(Facility facility);
}
