--------------------------------------------------------
--  DDL for Table LIBERTY_ROLE_EMPLOYEE
--------------------------------------------------------

  CREATE TABLE "LIBERTY_DATA"."LIBERTY_ROLE_EMPLOYEE"
   (	"LIBERTY_ROLE_EMPLOYEE_ID" NUMBER(10,0), 
	"LIBERTY_ROLE_ID" NUMBER(10,0), 
	"EMPLOYEE_NUMBER" NUMBER(10,0), 
	"ACTIVE" CHAR(1 BYTE) DEFAULT 1, 
	"CREATE_DATETIME" TIMESTAMP (6) DEFAULT SYSDATE, 
	"UPDATE_DATETIME" TIMESTAMP (6) DEFAULT SYSDATE, 
	"CREATE_EMPLOYEE_NUMBER" NUMBER(10,0), 
	"UPDATE_EMPLOYEE_NUMBER" NUMBER(10,0)
   ) PCTFREE 10 PCTUSED 40 INITRANS 1 MAXTRANS 255 NOCOMPRESS LOGGING
  STORAGE(INITIAL 65536 NEXT 1048576 MINEXTENTS 1 MAXEXTENTS 2147483645
  PCTINCREASE 0 FREELISTS 1 FREELIST GROUPS 1 BUFFER_POOL DEFAULT)
  TABLESPACE "BNDNEW_DATA" ;
