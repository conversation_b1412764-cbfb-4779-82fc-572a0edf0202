--------------------------------------------------------
--  DDL for Table PATIENT_COMMUNICATION
--------------------------------------------------------

  CREATE TABLE "LIBERTY_DATA"."PATIENT_COMMUNICATION"
   (	"COMM_ID" VARCHAR2(50 BYTE), 
	"PATIENT_ID" NUMBER(15,0), 
	"COMM_TO" VARCHAR2(15 BYTE), 
	"COMM_FROM" VARCHAR2(15 BYTE), 
	"COMM_MESSAGE" VARCHAR2(500 BYTE), 
	"EMPLOYEE_NUMBER" NUMBER(8,0), 
	"SEND_DATETIME" TIMESTAMP (6), 
	"RESPONSE_CODE" VARCHAR2(50 BYTE), 
	"RESPONSE_DATETIME" TIMESTAMP (6), 
	"CLINIC_ID" NUMBER(6,0), 
	"UNIQUE_ID" NUMBER(25,0), 
	"COMM_TYPE" VARCHAR2(25 BYTE), 
	"FACILITY_ID" NUMBER(6,0)
   ) PCTFREE 10 PCTUSED 40 INITRANS 1 MAXTRANS 255 NOCOMPRESS LOGGING
  STORAGE(INITIAL 65536 NEXT 1048576 MINEXTENTS 1 MAXEXTENTS **********
  PCTINCREASE 0 FREELISTS 1 FREELIST GROUPS 1 BUFFER_POOL DEFAULT)
  TABLESPACE "BNDNEW_DATA" ;
