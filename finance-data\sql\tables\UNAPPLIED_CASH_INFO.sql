--------------------------------------------------------
--  DDL for Table UNAPPLIED_CASH_INFO
--------------------------------------------------------

  CREATE TABLE "LIBERTY_DATA"."UNAPPLIED_CASH_INFO"
   (	"TRANSACTION_ID" NUMBER(11,0), 
	"FIRST_NAME" VARCHAR2(30 BYTE), 
	"LAST_NAME" VARCHAR2(30 BYTE), 
	"DATE_OF_SERVICE" DATE, 
	"DATE_OF_BIRTH" DATE, 
	"PLAN_ID" VARCHAR2(25 BYTE), 
	"OTHER_DETAILS" VARCHAR2(200 BYTE), 
	"SERVICE_PROVIDER_ID" NUMBER(10,0), 
	"CREATE_EMPLOYEE" NUMBER(10,0), 
	"CREATE_DATETIME" TIMESTAMP (6), 
	"UPDATE_EMPLOYEE" NUMBER(10,0), 
	"UPDATE_DATETIME" TIMESTAMP (6)
   ) PCTFREE 10 PCTUSED 40 INITRANS 1 MAXTRANS 255 NOCOMPRESS LOGGING
  STORAGE(INITIAL 65536 NEXT 1048576 MINEXTENTS 1 MAXEXTENTS **********
  PCTINCREASE 0 FREELISTS 1 FREELIST GROUPS 1 BUFFER_POOL DEFAULT)
  TABLESPACE "BNDNEW_DATA" ;
