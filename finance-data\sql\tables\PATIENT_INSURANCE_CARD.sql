--------------------------------------------------------
--  DDL for Table PATIENT_INSURANCE_CARD
--------------------------------------------------------

  CREATE TABLE "LIBERTY_DATA"."PATIENT_INSURANCE_CARD"
   (	"INSURANCE_CARD_ID" NUMBER(10,0), 
	"PATIENT_ID" NUMBER(10,0), 
	"SUBSCRIBER_NAME" VARCHAR2(35 BYTE), 
	"EMPLOYER_NAME" VARCHAR2(35 BYTE), 
	"INSURANCE_NAME" VARCHAR2(35 BYTE), 
	"GROUP_NUMBER" VARCHAR2(35 BYTE), 
	"MEMBER_NUMBER" VARCHAR2(35 BYTE), 
	"CREATE_DATETIME" TIMESTAMP (6), 
	"CREATE_EMPLOYEE" NUMBER(10,0), 
	"ACTIVATION_DATE" DATE, 
	"EXPIRATION_DATE" DATE, 
	"INSURANCE_TYPE" VARCHAR2(35 BYTE), 
	"IS_ACTIVE" CHAR(1 BYTE), 
	"COVERAGE_TYPE" VARCHAR2(10 BYTE), 
	"INSURANCE_PHONE_EXT" VARCHAR2(10 BYTE), 
	"INSURANCE_PHONE_NUMBER" VARCHAR2(14 BYTE), 
	"SUBSCRIBER_DOB" TIMESTAMP (6), 
	"SUBSCRIBER_REL" VARCHAR2(25 BYTE), 
	"EFFECTIVE_DATE" TIMESTAMP (6)
   ) PCTFREE 10 PCTUSED 40 INITRANS 1 MAXTRANS 255 NOCOMPRESS LOGGING
  STORAGE(INITIAL 65536 NEXT 1048576 MINEXTENTS 1 MAXEXTENTS **********
  PCTINCREASE 0 FREELISTS 1 FREELIST GROUPS 1 BUFFER_POOL DEFAULT)
  TABLESPACE "BNDNEW_DATA" ;
