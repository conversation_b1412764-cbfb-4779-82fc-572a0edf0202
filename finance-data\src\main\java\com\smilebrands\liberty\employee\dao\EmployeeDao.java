package com.smilebrands.liberty.employee.dao;

import com.smilebrands.liberty.employee.model.Employee;
import com.smilebrands.liberty.employee.model.EmployeeBase;
import java.util.List;

/**
 *
 * <AUTHOR> May 6, 2011
 */
public interface EmployeeDao {

    public Employee getEmployee(Integer employeeId);

    public EmployeeBase getBaseEmployee(Integer employeeId);

    public List<Employee> getEmployees(Integer facilityId);

    public List<EmployeeBase> getBaseEmployees(Integer facilityId);

    public List<Employee> getEmployees(String title);

    public List<EmployeeBase> getBaseEmployees(String title);

    public List<Employee> getEmployeesByManager(Integer managerEmployeeNumber);

    public List<EmployeeBase> getBaseEmployeeUsingPricing();
}
