--------------------------------------------------------
--  DDL for Table FEE_SCHEDULE
--------------------------------------------------------

  CREATE TABLE "LIBERTY_DATA"."FEE_SCHEDULE"
   (	"FEE_SCHEDULE" NUMBER(10,0), 
	"FEE_SCHEDULE_DESCRIPTION" VARCHAR2(50 BYTE), 
	"FEE_SCHEDULE_STATUS" CHAR(1 BYTE), 
	"FEE_SCHEDULE_TYPE" CHAR(1 BYTE), 
	"LINE_OF_BUSINESS" VARCHAR2(5 BYTE), 
	"CREATE_EMPLOYEE" NUMBER(10,0), 
	"CREATE_DATETIME" TIMESTAMP (6), 
	"UPDATE_EMPLOYEE" NUMBER(10,0), 
	"UPDATE_DATETIME" TIMESTAMP (6)
   ) PCTFREE 10 PCTUSED 40 INITRANS 1 MAXTRANS 255 NOCOMPRESS LOGGING
  STORAGE(INITIAL 65536 NEXT 1048576 MINEXTENTS 1 MAXEXTENTS 2147483645
  PCTINCREASE 0 FREELISTS 1 FREELIST GROUPS 1 BUFFER_POOL DEFAULT)
  TABLESPACE "BNDNEW_DATA" ;
