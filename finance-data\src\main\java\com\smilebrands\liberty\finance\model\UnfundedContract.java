package com.smilebrands.liberty.finance.model;

import com.fasterxml.jackson.databind.annotation.JsonDeserialize;
import com.fasterxml.jackson.databind.annotation.JsonSerialize;
import com.smilebrands.liberty.model.BaseObject;
import com.smilebrands.liberty.serializer.NewDateIsoDeSerializer;
import com.smilebrands.liberty.serializer.NewDateIsoSerializer;
import org.apache.commons.lang.builder.ToStringBuilder;
import org.apache.commons.lang.builder.ToStringStyle;

import javax.persistence.Temporal;
import javax.persistence.TemporalType;
import java.util.Date;

/**
 * <AUTHOR>
 * @version 08/13/2015
 */
public class UnfundedContract extends BaseObject {

    private String accountCreationDate;
    private Integer contractType;
    private Integer paymentType;
    private String loanClassification;
    private String officeSiteCode;
    private String loanCreationDate;
    private String guarantorFirstName;
    private String guarantorLastName;
    private String patientName;
    private String patientChartNumber;
    private Date contractApprovedDate;
    private Date photoIdApprovedDate;
    private Date proofBankingApprovedDate;
    private String collectedApplication;
    private String requiredDownPayment;
    private String actualDownPaymentCollected;
    private String remainingDownPayment;
    private String comments1;
    private String comments2;
    private String financedAmount;
    private String accountNumber;
    private String loanNumber;
    private String createUser;
    private String updateUser;
    private String qsiAccountType;
    private String sbiCollectorAccount;
    private String requiredDocumentsSent;

    @Temporal(TemporalType.TIMESTAMP)
    private Date updateDatetime;

    private String recentAttachmentUser;

    @Temporal(TemporalType.TIMESTAMP)
    private Date recentAttachmentDate;

    public String getAccountCreationDate() {
        return accountCreationDate;
    }

    public void setAccountCreationDate(String accountCreationDate) {
        this.accountCreationDate = accountCreationDate;
    }

    public Integer getContractType() {
        return contractType;
    }

    public void setContractType(Integer contractType) {
        this.contractType = contractType;
    }

    public Integer getPaymentType() {
        return paymentType;
    }

    public void setPaymentType(Integer paymentType) {
        this.paymentType = paymentType;
    }

    public String getLoanClassification() {
        return loanClassification;
    }

    public void setLoanClassification(String loanClassification) {
        this.loanClassification = loanClassification;
    }

    public String getOfficeSiteCode() {
        return officeSiteCode;
    }

    public void setOfficeSiteCode(String officeSiteCode) {
        this.officeSiteCode = officeSiteCode;
    }

    public String getLoanCreationDate() {
        return loanCreationDate;
    }

    public void setLoanCreationDate(String loanCreationDate) {
        this.loanCreationDate = loanCreationDate;
    }

    public String getGuarantorFirstName() {
        return guarantorFirstName;
    }

    public void setGuarantorFirstName(String guarantorFirstName) {
        this.guarantorFirstName = guarantorFirstName;
    }

    public String getGuarantorLastName() {
        return guarantorLastName;
    }

    public void setGuarantorLastName(String guarantorLastName) {
        this.guarantorLastName = guarantorLastName;
    }

    public String getPatientName() {
        return patientName;
    }

    public void setPatientName(String patientName) {
        this.patientName = patientName;
    }

    public String getPatientChartNumber() {
        return patientChartNumber;
    }

    public void setPatientChartNumber(String patientChartNumber) {
        this.patientChartNumber = patientChartNumber;
    }

    public Date getContractApprovedDate() {
        return contractApprovedDate;
    }

    public void setContractApprovedDate(Date contractApprovedDate) {
        this.contractApprovedDate = contractApprovedDate;
    }

    public Date getPhotoIdApprovedDate() {
        return photoIdApprovedDate;
    }

    public void setPhotoIdApprovedDate(Date photoIdApprovedDate) {
        this.photoIdApprovedDate = photoIdApprovedDate;
    }

    public Date getProofBankingApprovedDate() {
        return proofBankingApprovedDate;
    }

    public void setProofBankingApprovedDate(Date proofBankingApprovedDate) {
        this.proofBankingApprovedDate = proofBankingApprovedDate;
    }

    public String getCollectedApplication() {
        return collectedApplication;
    }

    public void setCollectedApplication(String collectedApplication) {
        this.collectedApplication = collectedApplication;
    }

    public String getRequiredDownPayment() {
        return requiredDownPayment;
    }

    public void setRequiredDownPayment(String requiredDownPayment) {
        this.requiredDownPayment = requiredDownPayment;
    }

    public String getActualDownPaymentCollected() {
        return actualDownPaymentCollected;
    }

    public void setActualDownPaymentCollected(String actualDownPaymentCollected) {
        this.actualDownPaymentCollected = actualDownPaymentCollected;
    }

    public String getRemainingDownPayment() {
        return remainingDownPayment;
    }

    public void setRemainingDownPayment(String remainingDownPayment) {
        this.remainingDownPayment = remainingDownPayment;
    }

    public String getComments1() {
        return comments1;
    }

    public void setComments1(String comments1) {
        this.comments1 = comments1;
    }

    public String getComments2() {
        return comments2;
    }

    public void setComments2(String comments2) {
        this.comments2 = comments2;
    }

    public String getFinancedAmount() {
        return financedAmount;
    }

    public void setFinancedAmount(String financedAmount) {
        this.financedAmount = financedAmount;
    }

    public String getAccountNumber() {
        return accountNumber;
    }

    public void setAccountNumber(String accountNumber) {
        this.accountNumber = accountNumber;
    }

    public String getLoanNumber() {
        return loanNumber;
    }

    public void setLoanNumber(String loanNumber) {
        this.loanNumber = loanNumber;
    }

    public String getRecentAttachmentUser() {
        return recentAttachmentUser;
    }

    public void setRecentAttachmentUser(String recentAttachmentUser) {
        this.recentAttachmentUser = recentAttachmentUser;
    }

    @JsonSerialize(using = NewDateIsoSerializer.class)
    public Date getRecentAttachmentDate() {
        return recentAttachmentDate;
    }

    @JsonDeserialize(using = NewDateIsoDeSerializer.class)
    public void setRecentAttachmentDate(Date recentAttachmentDate) {
        this.recentAttachmentDate = recentAttachmentDate;
    }

    public String getCreateUser() {
        return createUser;
    }

    public void setCreateUser(String createUser) {
        this.createUser = createUser;
    }

    public String getUpdateUser() {
        return updateUser;
    }

    public void setUpdateUser(String updateUser) {
        this.updateUser = updateUser;
    }

    public String getQsiAccountType() {
        return qsiAccountType;
    }

    public void setQsiAccountType(String qsiAccountType) {
        this.qsiAccountType = qsiAccountType;
    }

    public String getSbiCollectorAccount() {
        return sbiCollectorAccount;
    }

    public void setSbiCollectorAccount(String sbiCollectorAccount) {
        this.sbiCollectorAccount = sbiCollectorAccount;
    }

    public String getRequiredDocumentsSent() {
        return requiredDocumentsSent;
    }

    public void setRequiredDocumentsSent(String requiredDocumentsSent) {
        this.requiredDocumentsSent = requiredDocumentsSent;
    }

    @JsonSerialize(using = NewDateIsoSerializer.class)
    public Date getUpdateDatetime() {
        return updateDatetime;
    }

    @JsonDeserialize(using = NewDateIsoDeSerializer.class)
    public void setUpdateDatetime(Date updateDatetime) {
        this.updateDatetime = updateDatetime;
    }

    @Override
    public String toString() {
        return ToStringBuilder.reflectionToString(this,
                ToStringStyle.MULTI_LINE_STYLE);
    }

}