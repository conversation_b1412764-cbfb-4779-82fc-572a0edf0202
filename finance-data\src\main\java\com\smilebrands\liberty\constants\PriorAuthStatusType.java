package com.smilebrands.liberty.constants;

/**
 * Created with IntelliJ IDEA.
 * User: phongpham
 * Date: 6/24/13
 * Time: 9:54 AM
 * To change this template use File | Settings | File Templates.
 */
public enum PriorAuthStatusType {

    CREATED("CREATED"),
    PRINTED("PRINTED"),
    SENT("SENT"),
    RECEIVED("RECEIVED"),
    APPROVED("APPROVED"),
    DENIED("DENIED"),
    COMPLETED("COMPLETED");

    private final String strValue;

    private PriorAuthStatusType(final String strValue) {
        this.strValue = strValue;
    }

    public String getStatus() {
        return strValue;
    }

    @Override
    public String toString() {
        return this.strValue;
    }

}
