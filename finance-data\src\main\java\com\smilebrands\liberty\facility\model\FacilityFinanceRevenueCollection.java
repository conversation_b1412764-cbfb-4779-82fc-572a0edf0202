package com.smilebrands.liberty.facility.model;

import com.smilebrands.liberty.model.BaseObject;

import java.math.BigDecimal;

/**
 * <AUTHOR>
 * @version 01/08/2014
 *
 * Model to describe a financial summary by revenue/collection for an office
 */
public class FacilityFinanceRevenueCollection extends BaseObject {

    private String transactionGroup;
    private String transactionType;
    private String transactionDate;
    private BigDecimal dailyTotal;
    private BigDecimal monthToDateTotal;

    public String getTransactionGroup() {
        return transactionGroup;
    }

    public void setTransactionGroup(String transactionGroup) {
        this.transactionGroup = transactionGroup;
    }

    public String getTransactionType() {
        return transactionType;
    }

    public void setTransactionType(String transactionType) {
        this.transactionType = transactionType;
    }

    public String getTransactionDate() {
        return transactionDate;
    }

    public void setTransactionDate(String transactionDate) {
        this.transactionDate = transactionDate;
    }

    public BigDecimal getDailyTotal() {
        return dailyTotal;
    }

    public void setDailyTotal(BigDecimal dailyTotal) {
        this.dailyTotal = dailyTotal;
    }

    public BigDecimal getMonthToDateTotal() {
        return monthToDateTotal;
    }

    public void setMonthToDateTotal(BigDecimal monthToDateTotal) {
        this.monthToDateTotal = monthToDateTotal;
    }

}