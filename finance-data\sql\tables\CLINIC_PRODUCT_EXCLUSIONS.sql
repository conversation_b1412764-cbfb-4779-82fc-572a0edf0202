--------------------------------------------------------
--  DDL for Table CLINIC_PRODUCT_EXCLUSIONS
--------------------------------------------------------

  CREATE TABLE "LIBERTY_DATA"."CLINIC_PRODUCT_EXCLUSIONS"
   (	"CLINIC_PROD_EXCLUSION_ID" NUMBER(10,0), 
	"FACILITY_ID" NUMBER(10,0), 
	"PRODUCT_SERVICE_CODE" NUMBER(7,0), 
	"EFFECTIVE_DATE" DATE, 
	"INACTIVE_DATE" INTERVAL YEAR (2) TO MONTH, 
	"IS_ACTIVE" CHAR(1 BYTE), 
	"CREATE_EMPLOYEE" NUMBER(10,0), 
	"CREATE_TIMESTAMP" TIMES<PERSON><PERSON> (6), 
	"UPDATE_EMPLOYEE" NUMBER(10,0), 
	"UPDATE_TIMESTAMP" TIMESTAMP (6)
   ) PCTFREE 10 PCTUSED 40 INITRANS 1 MAXTRANS 255 NOCOMPRESS LOGGING
  STORAGE(INITIAL 65536 NEXT 1048576 MINEXTENTS 1 MAXEXTENTS 2147483645
  PCTINCREASE 0 FREELISTS 1 FREELIST GROUPS 1 BUFFER_POOL DEFAULT)
  TABLESPACE "BNDNEW_DATA" ;
