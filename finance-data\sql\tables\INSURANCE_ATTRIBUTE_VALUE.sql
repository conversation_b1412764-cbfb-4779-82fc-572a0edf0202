--------------------------------------------------------
--  DDL for Table INSURANCE_ATTRIBUTE_VALUE
--------------------------------------------------------

  CREATE TABLE "LIBERTY_DATA"."INSURANCE_ATTRIBUTE_VALUE"
   (	"INS_ATTR_VALUE_ID" NUMBER(15,0), 
	"INS_ATTRIBUTE_ID" NUMBER(15,0), 
	"INS_ATTRIBUTE_TYPE" CHAR(1 BYTE), 
	"INS_COMP_PLAN_ID" NUMBER(15,0), 
	"INS_ATTRIBUTE_VALUE" VARCHAR2(50 BYTE), 
	"IS_ACTIVE" CHAR(1 BYTE), 
	"CREATE_EMPLOYEE_NUMBER" NUMBER(6,0), 
	"CREATE_DATETIME" TIMESTAMP (6), 
	"ACTIVATION_EMPLOYEE_NUMBER" NUMBER(6,0), 
	"ACTIVATION_DATETIME" TIMESTAMP (6), 
	"INACTIVATION_EMPLOYEE_NUMBER" NUMBER(6,0), 
	"INACTIVATION_DATETIME" TIMESTAMP (6)
   ) PCTFREE 10 PCTUSED 40 INITRANS 1 MAXTRANS 255 NOCOMPRESS LOGGING
  STORAGE(INITIAL 65536 NEXT 1048576 MINEXTENTS 1 MAXEXTENTS 2147483645
  PCTINCREASE 0 FREELISTS 1 FREELIST GROUPS 1 BUFFER_POOL DEFAULT)
  TABLESPACE "BNDNEW_DATA" ;
