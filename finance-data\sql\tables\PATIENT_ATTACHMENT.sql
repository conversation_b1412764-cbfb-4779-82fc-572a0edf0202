--------------------------------------------------------
--  DDL for Table PATIENT_ATTACHMENT
--------------------------------------------------------

  CREATE TABLE "LIBERTY_DATA"."PATIENT_ATTACHMENT"
   (	"ATTACHMENT_ID" NUMBER(15,0), 
	"PATIENT_ID" NUMBER(15,0), 
	"ATTACHMENT_TYPE_ENUM" VARCHAR2(35 BYTE), 
	"ATTACHMENT_EXTENSION" VARCHAR2(4 BYTE), 
	"ATTACHMENT_DESCRIPTION" VARCHAR2(150 BYTE), 
	"ATTACHMENT_FILEPATH" VARCHAR2(255 BYTE), 
	"ATTACHMENT_FILENAME" VARCHAR2(50 BYTE), 
	"CREATE_EMPLOYEE_NUMBER" NUMBER(6,0), 
	"CREATE_DATETIME" TIMESTAMP (6), 
	"IS_VISIBLE" VARCHAR2(1 BYTE)
   ) PCTFREE 10 PCTUSED 40 INITRANS 1 MAXTRANS 255 NOCOMPRESS LOGGING
  STORAGE(INITIAL 65536 NEXT 1048576 MINEXTENTS 1 MAXEXTENTS **********
  PCTINCREASE 0 FREELISTS 1 FREELIST GROUPS 1 BUFFER_POOL DEFAULT)
  TABLESPACE "BNDNEW_DATA" ;
