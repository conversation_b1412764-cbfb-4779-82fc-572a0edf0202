package com.smilebrands.liberty.constants;

/**
 *
 * <AUTHOR> Bright Now! Dental, 2009
 */
public enum RefundDistributionType {

    CARE_CREDIT("Care Credit"),
    CHASE_HEALTH("Chase Health"),
    CHECK("Check"),
    CHECK_TO_INSURANCE("Check to Insurance"),
    CREDIT_CARD("Credit Card"),
    SBF("Smile Brand Finance");

    private final String strValue;

    private RefundDistributionType(final String strValue) {
        this.strValue = strValue;
    }

    @Override
    public String toString() {
        return this.strValue;
    }

}