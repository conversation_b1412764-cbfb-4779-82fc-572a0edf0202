package com.smilebrands.liberty.facility.model.vo;

import java.io.Serializable;
import java.util.*;

/**
 * Represents a Hierarchy Structure.
 *
 * <AUTHOR> Smile Brands Inc., 2010
 * <AUTHOR> Smile Brands Inc., 2010
 *
 */
public class Structure implements Serializable {

    public String text;
    public Integer id;
    public Integer instanceId;
    public Integer parentId;
    public Integer priority;
    public Integer clinic;
    public String location;
    public String type;
    public boolean leaf = false;
    public boolean active = true;
    public List<Structure> data = new ArrayList<Structure>();

    public boolean isActive() {
        return active;
    }

    public void setActive(boolean active) {
        this.active = active;
    }

    public List<Structure> getData() {
        return data;
    }

    public void setData(List<Structure> data) {
        this.data = data;
    }

    public Integer getClinic() {
        return clinic;
    }

    public void setClinic(Integer clinic) {
        this.clinic = clinic;
    }

    public Integer getId() {
        return id;
    }

    public void setId(Integer id) {
        this.id = id;
    }

    public boolean isLeaf() {
        return leaf;
    }

    public void setLeaf(boolean leaf) {
        this.leaf = leaf;
    }

    public String getLocation() {
        return location;
    }

    public void setLocation(String location) {
        this.location = location;
    }

    public Integer getParentId() {
        return parentId;
    }

    public void setParentId(Integer parentId) {
        this.parentId = parentId;
    }

    public Integer getPriority() {
        return priority;
    }

    public void setPriority(Integer priority) {
        this.priority = priority;
    }

    public String getText() {
        return text;
    }

    public void setText(String text) {
        this.text = text;
    }

    public String getType() {
        return type;
    }

    public void setType(String type) {
        this.type = type;
    }

    public Integer getInstanceId() {
        return instanceId;
    }

    public void setInstanceId(Integer instanceId) {
        this.instanceId = instanceId;
    }
    
    public String toString() {
        return id + " " + text + " " + data.size() + " kids.";
    }
}
