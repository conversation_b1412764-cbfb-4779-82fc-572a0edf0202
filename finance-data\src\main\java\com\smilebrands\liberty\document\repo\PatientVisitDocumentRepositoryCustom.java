package com.smilebrands.liberty.document.repo;

import com.smilebrands.liberty.document.model.Facility;
import com.smilebrands.liberty.document.model.PatientVisit;

import java.util.Date;
import java.util.List;

/**
 * User: <PERSON><PERSON>
 * Date: 8/21/13
 */
public interface PatientVisitDocumentRepositoryCustom {

    public List<PatientVisit> findPatientVisitByFacilityDate(Facility facility, Date serviceDate);

}
