package com.smilebrands.liberty.claim.model;

import com.smilebrands.liberty.constants.VendorType;
import com.smilebrands.liberty.model.BaseObject;
import com.smilebrands.liberty.utility.JsonDateDeSerializer;
import com.smilebrands.liberty.utility.JsonDateSerializer;
import org.codehaus.jackson.map.annotate.JsonDeserialize;
import org.codehaus.jackson.map.annotate.JsonSerialize;

import javax.persistence.*;
import java.util.Date;

/**
 * User: <PERSON><PERSON>
 * Date: 4/5/13
 */
@Entity
@NamedQueries(value = {
        @NamedQuery(name =  "AttachmentRequirement.findRequiredAttachmentByPlanId",
                    query = "SELECT p FROM AttachmentRequirement p " +
                            "WHERE p.insurancePlanId = ?1 " +
                            "AND p.active = true"),
        @NamedQuery(name =  "AttachmentRequirement.findRequiredAttachmentByCompanyProcedure",
                    query = "SELECT p FROM AttachmentRequirement p " +
                            "WHERE p.insuranceCompanyId = ?1 " +
                            "AND p.procedureCode = ?2 " +
                            "AND p.active = true"),
        @NamedQuery(name =  "AttachmentRequirement.findRequiredAttachmentByPlanProcedure",
                    query = "SELECT p FROM AttachmentRequirement p " +
                            "WHERE p.insurancePlanId = ?1 " +
                            "AND p.procedureCode = ?2 " +
                            "AND p.active = true"),
        @NamedQuery(name =  "AttachmentRequirement.findAllActiveRequiredAttachment",
                    query = "SELECT p FROM AttachmentRequirement p " +
                            "WHERE p.active = true"),
        @NamedQuery(name =  "AttachmentRequirement.findAttachmentRequirements",
                    query = "SELECT p FROM AttachmentRequirement p " +
                            "WHERE (p.insurancePlanType is null or p.insurancePlanType = ?1) " +
                            "AND (p.insuranceCompanyId is null or p.insuranceCompanyId = ?2) " +
                            "AND (p.insurancePlanId is null or p.insurancePlanId = ?3) " +
                            "AND (p.providerTaxonomy is null or p.providerTaxonomy = ?4) " +
                            "AND p.active = true " +
                            "AND p.vendorType = 'SBI' " +
                            "AND p.procedureCode is null " +
                            "AND not (p.insurancePlanType is null " +
                            "AND p.insuranceCompanyId is null " +
                            "AND p.insurancePlanId is null " +
                            "AND p.providerTaxonomy is null)"),
        @NamedQuery(name =  "AttachmentRequirement.findAttachmentRequirementsByCdtCode",
                    query = "SELECT p FROM AttachmentRequirement p " +
                            "WHERE p.procedureCode = ?1 " +
                            "AND p.active = true " +
                            "AND p.vendorType = 'SBI' " +
                            "AND (p.insurancePlanType is null or p.insurancePlanType = ?2) " +
                            "AND (p.insuranceCompanyId is null or p.insuranceCompanyId = ?3) " +
                            "AND (p.insurancePlanId is null or p.insurancePlanId = ?4) " +
                            "AND (p.providerTaxonomy is null or p.providerTaxonomy = ?5)")
})
@Table(name = "ATTACHMENT_REQUIREMENT")
public class AttachmentRequirement extends BaseObject {

    @Id
    @Column(name = "ATTACHMENT_REQUIREMENT_ID")
    @GeneratedValue(generator = "AttachmentReqSeq")
    @SequenceGenerator(name = "AttachmentReqSeq", sequenceName = "ATTACHMENT_REQ_ID_SEQ", allocationSize = 1)
    protected Long attachmentRequirementId;

    @Column(name = "PLAN_TYPE")
    protected String insurancePlanType;

    @Column(name = "INS_COMPANY_ID")
    protected Integer insuranceCompanyId;

    @Column(name = "INSURANCE_PLAN")
    protected Long insurancePlanId;

    @Column(name = "PROVIDER_TAXONOMY")
    protected String providerTaxonomy;

    @Column(name = "PROCEDURE_CODE")
    protected String procedureCode;

    @OneToOne
    @JoinColumn(name = "REQUIREMENT_ID", insertable = false, updatable = false)
    protected Requirement requirement;

    @Column(name = "REQUIREMENT_ID")
    protected Integer requirementId;

    @Column(name = "VENDOR_TYPE")
    @Enumerated(EnumType.STRING)
    protected VendorType vendorType;

    @Temporal(TemporalType.TIMESTAMP)
    @Column(name = "CREATE_DATETIME")
    private Date creationDatetime;

    @Column(name = "CREATE_EMPLOYEE_NUMBER")
    private Integer createEmployeeNumber;

    @Column(name = "IS_ACTIVE")
    private Boolean active;

    @Temporal(TemporalType.TIMESTAMP)
    @Column(name = "UPDATE_DATETIME")
    private Date updateDatetime;

    @Column(name = "UPDATE_EMPLOYEE_NUMBER")
    private Integer updateEmployeeNumber;

    public Long getAttachmentRequirementId() {
        return attachmentRequirementId;
    }

    public void setAttachmentRequirementId(Long attachmentRequirementId) {
        this.attachmentRequirementId = attachmentRequirementId;
    }

    public Long getInsurancePlanId() {
        return insurancePlanId;
    }

    public void setInsurancePlanId(Long insurancePlanId) {
        this.insurancePlanId = insurancePlanId;
    }

    public Integer getInsuranceCompanyId() {
        return insuranceCompanyId;
    }

    public void setInsuranceCompanyId(Integer insuranceCompanyId) {
        this.insuranceCompanyId = insuranceCompanyId;
    }

    public String getProcedureCode() {
        return procedureCode;
    }

    public void setProcedureCode(String procedureCode) {
        this.procedureCode = procedureCode;
    }

    public String getInsurancePlanType() { return insurancePlanType; }

    public void setInsurancePlanType(String insurancePlanType) { this.insurancePlanType = insurancePlanType; }

    public String getProviderTaxonomy() { return providerTaxonomy; }

    public void setProviderTaxonomy(String providerTaxonomy) { this.providerTaxonomy = providerTaxonomy; }

    public Requirement getRequirement() {
        return requirement;
    }

    public void setRequirement(Requirement requirement) {
        this.requirement = requirement;
    }

    public Integer getRequirementId() {
        return requirementId;
    }

    public void setRequirementId(Integer requirementId) {
        this.requirementId = requirementId;
    }

    public VendorType getVendorType() {
        return vendorType;
    }

    public void setVendorType(VendorType vendorType) {
        this.vendorType = vendorType;
    }

    @JsonSerialize(using= JsonDateSerializer.class)
    public Date getCreationDatetime() {
        return creationDatetime;
    }

    @JsonDeserialize(using = JsonDateDeSerializer.class)
    public void setCreationDatetime(Date creationDatetime) {
        this.creationDatetime = creationDatetime;
    }

    public Integer getCreateEmployeeNumber() {
        return createEmployeeNumber;
    }

    public void setCreateEmployeeNumber(Integer createEmployeeNumber) {
        this.createEmployeeNumber = createEmployeeNumber;
    }

    public Boolean getActive() {
        return active;
    }

    public void setActive(Boolean active) {
        this.active = active;
    }

    @JsonSerialize(using= JsonDateSerializer.class)
    public Date getUpdateDatetime() {
        return updateDatetime;
    }


    @JsonDeserialize(using = JsonDateDeSerializer.class)
    public void setUpdateDatetime(Date updateDatetime) {
        this.updateDatetime = updateDatetime;
    }

    public Integer getUpdateEmployeeNumber() {
        return updateEmployeeNumber;
    }

    public void setUpdateEmployeeNumber(Integer updateEmployeeNumber) {
        this.updateEmployeeNumber = updateEmployeeNumber;
    }

    @Override
    public boolean equals(Object o) {
        if (this == o) return true;
        if (o == null || getClass() != o.getClass()) return false;

        AttachmentRequirement that = (AttachmentRequirement) o;

        if (!attachmentRequirementId.equals(that.attachmentRequirementId)) return false;

        return true;
    }

    @Override
    public int hashCode() {
        return attachmentRequirementId != null ? attachmentRequirementId.hashCode() : 0;
    }
}
