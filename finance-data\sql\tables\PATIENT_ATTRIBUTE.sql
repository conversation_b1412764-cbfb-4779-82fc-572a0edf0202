--------------------------------------------------------
--  DDL for Table PATIENT_ATTRIBUTE
--------------------------------------------------------

  CREATE TABLE "LIBERTY_DATA"."PATIENT_ATTRIBUTE"
   (	"PATIENT_ATTRIBUTE_ID" NUMBER(5,0), 
	"ATTRIBUTE_NAME" VARCHAR2(35 BYTE), 
	"ATTRIBUTE_DISPLAY" VARCHAR2(250 BYTE), 
	"ATTRIBUTE_OPTION_TYPE" VARCHAR2(35 BYTE), 
	"ATTRIBUTE_GROUP_TYPE" VARCHAR2(35 BYTE), 
	"ALLOW_MULTIPLE_SELECTIONS" CHAR(1 BYTE), 
	"IS_ACTIVE" CHAR(1 BYTE), 
	"ALLOW_USER_INPUT" CHAR(1 BYTE), 
	"DISPLAY_ORDER" NUMBER(5,0), 
	"PARENT_ATTRIBUTE_ID" NUMBER(5,0)
   ) PCTFREE 10 PCTUSED 40 INITRANS 1 MAXTRANS 255 NOCOMPRESS LOGGING
  STORAGE(INITIAL 65536 NEXT 1048576 MINEXTENTS 1 MAXEXTENTS **********
  PCTINCREASE 0 FREELISTS 1 FREELIST GROUPS 1 BUFFER_POOL DEFAULT)
  TABLESPACE "BNDNEW_DATA" ;
