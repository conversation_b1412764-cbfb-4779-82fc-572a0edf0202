package com.smilebrands.liberty.claim.model;

import com.smilebrands.liberty.constants.ClaimReasonCodeType;
import com.smilebrands.liberty.model.BaseObject;
import com.smilebrands.liberty.utility.JsonDateDeSerializer;
import com.smilebrands.liberty.utility.JsonDateSerializer;
import org.codehaus.jackson.map.annotate.JsonDeserialize;
import org.codehaus.jackson.map.annotate.JsonSerialize;

import java.util.Date;

//import javax.persistence.*;

/**
 * <AUTHOR>
 */
//@Entity
//@Table(name = "CLAIM_REASON_CODE")
@SuppressWarnings("serial")
public class ClaimReasonCode extends BaseObject {

    //@Id
    //@Column(name = "CLAIM_REASON_ID")
    //@GeneratedValue(generator = "ClaimReasonSeq")
    //@SequenceGenerator(name = "ClaimReasonSeq", sequenceName = "CLAIM_REASON_SEQ", allocationSize = 5)
    protected Long claimReasonId;

    //@Column(name = "CLAIM_ID")
    protected Long claimId;

    //@Column(name = "CLAIM_REASON_CODE_TYPE")
    //@Enumerated(EnumType.STRING)
    protected ClaimReasonCodeType claimReasonCodeType;

    //@Temporal(TemporalType.TIMESTAMP)
    //@Column(name = "CREATE_DATETIME")
    private Date createDatetime;

    public Long getClaimReasonId() {
        return claimReasonId;
    }

    public void setClaimReasonId(Long claimReasonId) {
        this.claimReasonId = claimReasonId;
    }

    public Long getClaimId() {
        return claimId;
    }

    public void setClaimId(Long claimId) {
        this.claimId = claimId;
    }

    @JsonSerialize(using = JsonDateSerializer.class)
    public Date getCreateDatetime() {
        return createDatetime;
    }

    @JsonDeserialize(using = JsonDateDeSerializer.class)
    public void setCreateDatetime(Date createDatetime) {
        this.createDatetime = createDatetime;
    }

    @Override
    public boolean equals(Object o) {
        if (this == o) return true;
        if (o == null || getClass() != o.getClass()) return false;

        ClaimReasonCode that = (ClaimReasonCode) o;

        if (claimReasonId != null ? !claimReasonId.equals(that.claimReasonId) : that.claimReasonId != null)
            return false;

        return true;
    }

    @Override
    public int hashCode() {
        return claimReasonId != null ? claimReasonId.hashCode() : 0;
    }
}