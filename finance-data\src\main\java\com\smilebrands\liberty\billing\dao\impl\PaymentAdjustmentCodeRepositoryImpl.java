package com.smilebrands.liberty.billing.dao.impl;

import com.mysema.query.jpa.JPQLQuery;
import com.mysema.query.jpa.impl.JPAQuery;
import com.smilebrands.liberty.billing.dao.PaymentAdjustmentCodeCustom;
import com.smilebrands.liberty.billing.dao.PaymentAdjustmentCodeRepository;
import com.smilebrands.liberty.billing.model.PaymentAdjustmentCode;
import com.smilebrands.liberty.billing.model.QPaymentAdjustmentCode;
import com.smilebrands.liberty.billing.model.QPaymentAdjustmentGLDistributionCode;
import com.smilebrands.liberty.cache.ActionType;
import com.smilebrands.liberty.cache.Cacheable;
import com.smilebrands.liberty.dao.SimpleAbstractDao;
import org.springframework.beans.factory.annotation.Autowired;

import java.util.List;

/*
* Mar<PERSON> Clark - Smile Brands Inc. 11/20/12
*
*/
public class PaymentAdjustmentCodeRepositoryImpl extends SimpleAbstractDao implements PaymentAdjustmentCodeCustom {

    private static final Integer SPECIAL_PAYMENT_ADJ_CODE = 9000;

    @Autowired
    private PaymentAdjustmentCodeRepository paymentAdjustmentCodeRepository;

    @Override
    public List<PaymentAdjustmentCode> getPaymentAdjustmentCodes(boolean includePatient, boolean includePrimary, boolean includeSecondary) {
        JPQLQuery queryDsl = new JPAQuery(em);
        QPaymentAdjustmentCode code = QPaymentAdjustmentCode.paymentAdjustmentCode;

        queryDsl.from(code);
        if (includePatient) {
            queryDsl.where(code.allowOnPatient.isNotNull());
        }
        if (includePrimary) {
            queryDsl.where(code.allowOnPrimaryInsurance.isNotNull());
        }
        if (includeSecondary) {
            queryDsl.where(code.allowOnSecondaryInsurance.isNotNull());
        }

        return queryDsl.list(code);

    }

    @Override
    @Cacheable(cacheName="CodeCollections")
    public List<PaymentAdjustmentCode> getPaymentAdjustmentCodesCached() {
        return this.getPaymentAdjustmentCodes();
    }

    @Override
    public List<PaymentAdjustmentCode> getPaymentAdjustmentCodesUnCached() {
        return this.getPaymentAdjustmentCodes();
    }

    @Override
    public List<PaymentAdjustmentCode> getPaymentAdjustmentCodes() {
        JPQLQuery queryDsl = new JPAQuery(em);
        QPaymentAdjustmentCode code = QPaymentAdjustmentCode.paymentAdjustmentCode;
        QPaymentAdjustmentGLDistributionCode glCode = QPaymentAdjustmentGLDistributionCode.paymentAdjustmentGLDistributionCode;
        return queryDsl.from(code)
                .leftJoin(code.paymentAdjustmentGLDistributionCodes, glCode).fetch()
                .where(code.paymentAdjustmentCodeId.lt(SPECIAL_PAYMENT_ADJ_CODE))
                .list(code);
    }
}