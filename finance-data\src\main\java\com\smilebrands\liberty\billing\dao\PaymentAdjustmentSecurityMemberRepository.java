package com.smilebrands.liberty.billing.dao;

import com.smilebrands.liberty.billing.model.PaymentAdjustmentSecurityMember;
import org.springframework.data.repository.CrudRepository;

import java.util.List;

/**
 * User: <PERSON><PERSON>
 * Date: 12/27/13
 */
public interface PaymentAdjustmentSecurityMemberRepository extends CrudRepository<PaymentAdjustmentSecurityMember, Long> {

    public List<PaymentAdjustmentSecurityMember> getPaymentAdjustmentCodeMembers(Integer securityGroupId);
}
