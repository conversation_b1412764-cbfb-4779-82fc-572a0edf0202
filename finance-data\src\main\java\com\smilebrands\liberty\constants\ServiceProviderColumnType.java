package com.smilebrands.liberty.constants;

/**
 * Java5 style enum class to represent the various provider column types.
 * 
 * <AUTHOR> Smile Brands Inc, 2011
 */
public enum ServiceProviderColumnType{

//   	NEW("New Patient"){
//   		@Override
//   		public String getDescription(){
//   			return "Providers New Patient Column.";
//   		}
//   	},
	EXAM("Exam"){
		@Override
		public String getDescription(){
			return "Providers Exam Column.";
		}
	},
   	MAJOR("Major"){
   		@Override
   		public String getDescription(){
   			return "Providers Major Column.";
   		}
   	},
//   	COMPLEX("Complex"){
//   		@Override
//   		public String getDescription(){
//   			return "Providers Complex Column.";
//   		}
//   	},
   	MINOR("Minor"){
   		@Override
   		public String getDescription(){
   			return "Providers Minor Column";
   		}
   	},
   	ORTHO("Ortho"){
   		@Override
   		public String getDescription(){
   			return "Othordontists Column.";
   		}
   	},
    NEW_PATIENT("New Patient"){
        @Override
        public String getDescription(){
            return "Column for New Patients";
        }
    },
    EMERGENCY("Emergency"){
        @Override
        public String getDescription() {
            return "Column for same day emergencies";
        }
    };
   	private final String column;
   	private ServiceProviderColumnType(String in){
   		this.column = in;
   	}
   	
   	public String getDescription(){
   		return "";
   	}
   	
   	@Override
   	public String toString(){
   		return this.column;
   	}
   	
   	

	
}
