package com.smilebrands.liberty.facility.dao;

import com.smilebrands.liberty.facility.model.LaboratoryCase;
import org.springframework.data.repository.CrudRepository;

import java.util.List;

/**
 * Repository for LaboritoryOrder Entities.   Extends Spring's CrudRepository which
 * provides provides the base methods of save, findById, findAll, count, exits
 * and delete.
 *
 * Created using IntelliJ IDEA.
 * User: <PERSON><PERSON><PERSON>
 * Date: 3/12/12
 */

public interface LaboratoryCaseRepository extends CrudRepository<LaboratoryCase, Long>  {

    List<LaboratoryCase> findAllLabCasesByCaseType(String caseType);

    List<LaboratoryCase> findAllLabCasesByPatientId(Long patientId);

    List<LaboratoryCase> findAllLabCasesByFacilityId(Integer facilityId);

    List<LaboratoryCase> findCurrentLabCasesByFacilityId(Integer facilityId);

    List<LaboratoryCase> findAllLabCasesByProviderId(Integer providerId);

    List<LaboratoryCase> findUndeliveredLabCasesByFacilityId(Integer facilityId);

    List<LaboratoryCase> findUndeliveredLabCasesByProviderId(Integer providerId);

    List<LaboratoryCase> findUndeliveredLabCasesByPatientId(Long patientId);

    List<LaboratoryCase> findAllLabCasesByDeliverySchEventId(Long deliverySchEventId);


}
