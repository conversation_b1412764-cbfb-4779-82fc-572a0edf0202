package com.smilebrands.liberty.facility.dao;

import com.smilebrands.liberty.facility.model.LaboratoryCaseStatus;
import org.springframework.data.repository.CrudRepository;

import java.util.List;

/**
 * Created using IntelliJ IDEA.
 * User: m<PERSON><PERSON>
 */
public interface LaboratoryCaseStatusRepository extends CrudRepository<LaboratoryCaseStatus, Long> {


    public List<LaboratoryCaseStatus> findAllCaseStatusByCaseId(Long caseId);

}
