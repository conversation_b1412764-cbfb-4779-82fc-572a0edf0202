package com.smilebrands.liberty.billing.dao.impl;

import com.smilebrands.liberty.billing.dao.PaymentAdjustmentBatchDao;
import com.smilebrands.liberty.billing.dao.PaymentAdjustmentBatchRepository;
import com.smilebrands.liberty.billing.model.vo.PaymentAdjustmentBatchVo;
import com.smilebrands.liberty.dao.SimpleAbstractDao;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Qualifier;
import org.springframework.jdbc.core.JdbcTemplate;
import org.springframework.jdbc.core.RowMapper;
import org.springframework.stereotype.Repository;

import java.sql.ResultSet;
import java.sql.SQLException;
import java.util.List;

/**
 * Created with IntelliJ IDEA.
 * User: kevintran
 * Date: 3/13/13
 * Time: 4:59 PM
 * To change this template use File | Settings | File Templates.
 */
@Repository(value = "paymentAdjustmentBatchDao")
public class PaymentAdjustmentBatchDaoImpl extends SimpleAbstractDao implements PaymentAdjustmentBatchDao {

    final Logger logger = LoggerFactory.getLogger(PaymentAdjustmentBatchDaoImpl.class);

    @Autowired
    @Qualifier("libertyJdbcTemplate")
    protected JdbcTemplate jdbcTemplate;

    @Autowired
    @Qualifier("openBatchesSummary")
    protected String openBatchesSummary;

    @Override
    public List<PaymentAdjustmentBatchVo> getOpenBatchesSummary() {
        return jdbcTemplate.query(openBatchesSummary,
                new Object[]{},
                new RowMapper() {

                    @Override
                    public Object mapRow(ResultSet rs, int i) throws SQLException {
                        PaymentAdjustmentBatchVo vo = new PaymentAdjustmentBatchVo();
                        vo.paymentAdjustmentBatchId = rs.getLong("PAY_ADJ_BATCH_ID");
                        vo.employeeNumber = rs.getLong("EMPLOYEE_NUMBER");
                        vo.firstName = rs.getString("FIRST_NAME");
                        vo.lastName = rs.getString("LAST_NAME");
                        vo.createDateTime = rs.getTimestamp("CREATE_DATETIME");

                        return vo;
                    }

                });
    }

}
