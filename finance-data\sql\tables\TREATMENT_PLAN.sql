--------------------------------------------------------
--  DDL for Table TREATMENT_PLAN
--------------------------------------------------------

  CREATE TABLE "LIBERTY_DATA"."TREATMENT_PLAN"
   (	"TREATMENT_PLAN_ID" NUMBER(15,5), 
	"TREATMENT_PLAN_NAME" VARCHAR2(50 BYTE), 
	"TREATMENT_PLAN_DESCRIPTION" VARCHAR2(200 BYTE), 
	"PATIENT_ID" NUMBER(15,5), 
	"SCHEDULE_EVENT_ID" NUMBER(15,5), 
	"CREATE_EMPLOYEE_NUMBER" NUMBER(6,0), 
	"CREATE_DATETIME" TIMESTAMP (6), 
	"UPDATE_EMPLOYEE_NUMBER" NUMBER(6,0), 
	"UPDATE_DATETIME" TIMESTAMP (6), 
	"EVENT_LINK_EMPLOYEE_NUMBER" NUMBER(6,0), 
	"EVENT_LINK_DATETIME" TIMESTAMP (6), 
	"PLAN_ESTIMATE" NUMBER(7,2), 
	"PATIENT_ESTIMATE" NUMBER(7,2), 
	"PRIMARY_ESTIMATE" NUMBER(7,2), 
	"SECONDARY_ESTIMATE" NUMBER(7,2)
   ) PCTFREE 10 PCTUSED 40 INITRANS 1 MAXTRANS 255 NOCOMPRESS LOGGING
  STORAGE(INITIAL 65536 NEXT 1048576 MINEXTENTS 1 MAXEXTENTS **********
  PCTINCREASE 0 FREELISTS 1 FREELIST GROUPS 1 BUFFER_POOL DEFAULT)
  TABLESPACE "BNDNEW_DATA" ;
