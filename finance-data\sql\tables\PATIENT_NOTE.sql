--------------------------------------------------------
--  DDL for Table PATIENT_NOTE
--------------------------------------------------------

  CREATE TABLE "LIBERTY_DATA"."PATIENT_NOTE"
   (	"NOTE_ID" NUMBER(15,0), 
	"PATIENT_ID" NUMBER(10,0), 
	"NOTE" VARCHAR2(2000 BYTE), 
	"PRIMARY_CONTEXT_ID" NUMBER(3,0), 
	"SECONDARY_CONTEXT_ID" NUMBER(3,0), 
	"TERTIARY_CONTEXT_ID" NUMBER(3,0), 
	"PRIMARY_FOREIGN_KEY" VARCHAR2(15 BYTE), 
	"SECONDARY_FOREIGN_KEY" VARCHAR2(15 BYTE), 
	"TERTIARY_FOREIGN_KEY" VARCHAR2(15 BYTE), 
	"PARENT_NOTE_ID" NUMBER(15,0), 
	"CREATE_EMPLOYEE_NUMBER" NUMBER(6,0), 
	"CREATE_DATETIME" TIMESTAMP (6), 
	"ORIGIN_MODULE_ENUM" VARCHAR2(25 BYTE), 
	"VISIBLE_ON_PRINT" VARCHAR2(1 BYTE), 
	"TEETH_ASSOCIATION" VARCHAR2(150 BYTE), 
	"UPDATE_EMPLOYEE_NUMBER" NUMBER(6,0), 
	"UPDATE_DATETIME" TIMESTAMP (6), 
	"SERVICE_PROVIDER_ID" VARCHAR2(15 BYTE), 
	"DRAFT" CHAR(1 BYTE) DEFAULT '0'
   ) PCTFREE 10 PCTUSED 40 INITRANS 1 MAXTRANS 255 NOCOMPRESS LOGGING
  STORAGE(INITIAL 65536 NEXT 1048576 MINEXTENTS 1 MAXEXTENTS **********
  PCTINCREASE 0 FREELISTS 1 FREELIST GROUPS 1 BUFFER_POOL DEFAULT)
  TABLESPACE "BNDNEW_DATA" ;
