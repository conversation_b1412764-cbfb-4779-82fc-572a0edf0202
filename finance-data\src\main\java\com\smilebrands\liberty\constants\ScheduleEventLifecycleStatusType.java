package com.smilebrands.liberty.constants;

/**
 * Created using IntelliJ IDEA.
 * User: <PERSON><PERSON><PERSON>
 * Date: 2/1/12
 */
public enum ScheduleEventLifecycleStatusType
{

    SCHEDULED("scheduled"){
         @Override
         public String getDescription(){
             return "The event has been scheduled.";
         }
     },
     CONFIRMED("confirmed"){
         @Override
         public String getDescription(){
             return "The patient has confirmed the event, insurance has been verified.";
         }
     },
     ARRIVED("arrived"){
         @Override
         public String getDescription(){
             return "The patient has arrived in the office.";
         }
     },
     COMPLETED("completed"){
         @Override
         public String getDescription(){
             return "The patient's treatment has been completed.";
         }
     },
     MISSED("missed"){
         @Override
         public String getDescription(){
             return "The patient missed the appointment.";
         }
     },
     CANCELLED("cancelled"){
         @Override
         public String getDescription(){
             return "The appointment has been cancelled.";
         }
     },
     XRAY("x-ray"){
         @Override
         public String getDescription(){
             return "Patient x-rays have been taken.";
         }
     },
     OPERATORY("operatory"){
         @Override
         public String getDescription(){
             return "The patient has been seated in an operatory.";
         }
     };
     private final String status;
     private ScheduleEventLifecycleStatusType(String in){
         this.status = in;
     }

     public String getDescription(){
         return "";
     }

     @Override
     public String toString(){
         return this.status;
     }

}
