package com.smilebrands.liberty.facility.dao.impl;

import com.smilebrands.liberty.dao.SimpleAbstractDao;
import com.smilebrands.liberty.facility.dao.FacilityFinanceDao;
import com.smilebrands.liberty.facility.model.FacilityFinanceProviderRevenue;
import com.smilebrands.liberty.facility.model.FacilityFinanceRevenueCollection;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Qualifier;
import org.springframework.jdbc.core.JdbcTemplate;
import org.springframework.jdbc.core.RowMapper;
import org.springframework.stereotype.Repository;

import java.sql.ResultSet;
import java.sql.SQLException;
import java.text.SimpleDateFormat;
import java.util.Calendar;
import java.util.Date;
import java.util.List;


/**
 * <AUTHOR>
 * @version 01/02/2014
 */
@Repository
public class FacilityFinanceDaoImpl extends SimpleAbstractDao implements FacilityFinanceDao {

    @Autowired
    @Qualifier("libertyJdbcTemplate")
    private JdbcTemplate jdbcTemplate;

    @Autowired
    @Qualifier("facilityFinanceSummarySql")
    private String facilityFinanceSummarySql;

    @Autowired
    @Qualifier("facilityFinanceRevenueByProviderSql")
    private String facilityFinanceRevenueByProviderSql;


    @Override
    public List<FacilityFinanceRevenueCollection> getFacilityFinanceRevenueCollection(Integer facilityId, Date date) {
        SimpleDateFormat sdf = new SimpleDateFormat("dd-MMM-yyyy");
        String startDate = sdf.format(getFirstDayOfMonth(date));
        String endDate = sdf.format(date);

        String sql = facilityFinanceSummarySql;
        sql = sql.replaceAll("#FACILITY_ID#", facilityId.toString());
        sql = sql.replaceAll("#START_DATE#", "'" + startDate + "'");
        sql = sql.replaceAll("#END_DATE#", "'" + endDate + "'");
        sql = sql.replaceAll("#TRX_DATE#", "'" + new SimpleDateFormat("MM/dd/yyyy").format(date) + "'");
        sql = sql.replaceAll("#CAP_YEAR#", "'" + new SimpleDateFormat("yyyy").format(date) + "'");
        sql = sql.replaceAll("#CAP_MONTH#", "'" + new SimpleDateFormat("MM").format(date) + "'");

        return jdbcTemplate.query(sql, new FacilityFinanceRevenueCollectionRowMapper());
    }

    @Override
    public List<FacilityFinanceProviderRevenue> getFacilityFinanceProviderRevenue(Integer facilityId, Date date) {
        SimpleDateFormat sdf = new SimpleDateFormat("dd-MMM-yyyy");
        String startDate = sdf.format(getFirstDayOfMonth(date));
        String endDate = sdf.format(date);

        String sql = facilityFinanceRevenueByProviderSql;
        sql = sql.replaceAll("#FACILITY_ID#", facilityId.toString());
        sql = sql.replaceAll("#START_DATE#", "'" + startDate + "'");
        sql = sql.replaceAll("#END_DATE#", "'" + endDate + "'");

        return jdbcTemplate.query(sql, new FacilityFinanceProviderRevenueRowMapper());
    }


    private Date getFirstDayOfMonth(Date date) {
        Calendar calendar = Calendar.getInstance();
        calendar.setTime(date);
        calendar.set(Calendar.DAY_OF_MONTH, 1);
        return calendar.getTime();
    }


    class FacilityFinanceRevenueCollectionRowMapper implements RowMapper {

        @Override
        public Object mapRow(ResultSet rs, int i) throws SQLException {
            FacilityFinanceRevenueCollection r = new FacilityFinanceRevenueCollection();
            r.setTransactionGroup(rs.getString("TRANSACTION_GROUP"));
            r.setTransactionType(rs.getString("TRANSACTION_TYPE"));
            r.setTransactionDate(rs.getString("TRANSACTION_DATE"));
            r.setDailyTotal(rs.getBigDecimal("DAILY_TOTAL"));
            r.setMonthToDateTotal(rs.getBigDecimal("MTD_TOTAL"));
            return r;
        }
    }


    class FacilityFinanceProviderRevenueRowMapper implements RowMapper {

        @Override
        public Object mapRow(ResultSet rs, int i) throws SQLException {
            FacilityFinanceProviderRevenue r = new FacilityFinanceProviderRevenue();
            r.setProviderId(rs.getInt("SERVICE_PROVIDER_ID"));
            r.setFirstName(rs.getString("FIRST_NAME"));
            r.setLastName(rs.getString("LAST_NAME"));
            r.setTaxonomyCode(rs.getString("TAXONOMY_CODE"));
            r.setProviderType(rs.getString("PROVIDER_TYPE"));
            r.setDailyTotal(rs.getBigDecimal("DAILY_TOTAL"));
            r.setMonthToDateTotal(rs.getBigDecimal("MTD_TOTAL"));
            r.setDailyProduction(rs.getBigDecimal("DAILY_PRODUCTION"));
            r.setDailyAdjustments(rs.getBigDecimal("DAILY_ADJUSTMENTS"));
            r.setAverageDailyTotal(rs.getBigDecimal("AVG_DAILY_TOTAL"));
            r.setDaysWorked(rs.getInt("DAYS_WORKED"));
            return r;
        }

    }

}