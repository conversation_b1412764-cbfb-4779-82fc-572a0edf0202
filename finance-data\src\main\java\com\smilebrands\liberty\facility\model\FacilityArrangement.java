package com.smilebrands.liberty.facility.model;

import com.smilebrands.liberty.billing.model.Arrangement;
import com.smilebrands.liberty.model.BaseObject;
import com.smilebrands.liberty.serializer.DateIsoDeSerializer;
import com.smilebrands.liberty.serializer.DateIsoSerializer;
import org.codehaus.jackson.map.annotate.JsonDeserialize;
import org.codehaus.jackson.map.annotate.JsonSerialize;
import org.hibernate.annotations.CacheConcurrencyStrategy;
import org.hibernate.annotations.NotFound;
import org.hibernate.annotations.NotFoundAction;
import org.hibernate.annotations.Where;

import javax.persistence.*;
import java.util.Date;

/**
 * Created with IntelliJ IDEA.
 * User: phongpham
 * Date: 12/20/12
 * Time: 12:57 PM
 * To change this template use File | Settings | File Templates.
 */
@Entity
@Table(name = "FACILITY_DISCOUNT_ARRANGEMENTS")
@NamedQueries({
        @NamedQuery(name = "FacilityArrangement.findArrangementByFacilityId", query = "from FacilityArrangement fa where fa.facilityId = ?"),
        @NamedQuery(name = "FacilityArrangement.findActiveArrangementByFacilityId",
                    query = "from FacilityArrangement fa " +
                            " join fetch fa.arrangement arr " +
                            " left join fetch arr.arrangementDetails " +
                            " where fa.facilityId = ? and fa.inactiveDate is null" +
                            " and trunc(fa.effectiveDate) <= trunc(sysdate)" +
                            " and trunc(arr.effectiveDate) <= trunc(sysdate)" +
                            " and (arr.inactiveDate is null or trunc(arr.inactiveDate) > trunc(sysdate))"),
        @NamedQuery(name = "FacilityArrangement.findActiveArrangementByArrangementId", query = "from FacilityArrangement fa where fa.arrangementId = ? and fa.inactiveDate = null"),
        @NamedQuery(name = "FacilityArrangement.findArrangementByArrangementId", query = "from FacilityArrangement fa where fa.arrangementId = ?")
})
public class FacilityArrangement extends BaseObject {

    @Id
    @Column(name = "FACILITY_ARRANGEMENT_ID")
    @GeneratedValue(generator = "FacilityArrangementSeq")
    @SequenceGenerator(name = "FacilityArrangementSeq", sequenceName = "FACILITY_DISC_ARRANGEMENT_SEQ", allocationSize = 1)
    private Long facilityArrangementId;

    @Column(name = "FACILITY_ID")
    private Integer facilityId;

    @Column(name = "ARRANGEMENT_ID")
    private Long arrangementId;

    @NotFound(action = NotFoundAction.IGNORE)
    @OneToOne(fetch = FetchType.EAGER)
    @JoinColumn(name = "ARRANGEMENT_ID", updatable = false, insertable = false)
    private Arrangement arrangement;

    @Temporal(TemporalType.TIMESTAMP)
    @Column(name = "EFFECTIVE_DATE")
    private Date effectiveDate;

    @Temporal(TemporalType.TIMESTAMP)
    @Column(name = "INACTIVE_DATE")
    private Date inactiveDate;

    @Column(name = "CREATE_EMPLOYEE")
    private Integer createEmployeeNumber;

    @Temporal(TemporalType.TIMESTAMP)
    @Column(name = "CREATE_DATETIME")
    private Date createDateTime = new Date();

    @Column(name = "UPDATE_EMPLOYEE")
    private Integer updateEmployeeNumber;

    @Temporal(TemporalType.TIMESTAMP)
    @Column(name = "UPDATE_DATETIME")
    private Date updateDateTime;

    public Long getFacilityArrangementId() {
        return facilityArrangementId;
    }

    public void setFacilityArrangementId(Long facilityArrangementId) {
        this.facilityArrangementId = facilityArrangementId;
    }

    public Integer getFacilityId() {
        return facilityId;
    }

    public void setFacilityId(Integer facilityId) {
        this.facilityId = facilityId;
    }

    public Long getArrangementId() {
        return arrangementId;
    }

    public void setArrangementId(Long arrangementId) {
        this.arrangementId = arrangementId;
    }

    public Arrangement getArrangement() {
        return arrangement;
    }

    public void setArrangement(Arrangement arrangement) {
        this.arrangement = arrangement;
    }

    @JsonSerialize(using= DateIsoSerializer.class)
    public Date getEffectiveDate() {
        return effectiveDate;
    }

    @JsonDeserialize(using=DateIsoDeSerializer.class)
    public void setEffectiveDate(Date effectiveDate) {
        this.effectiveDate = effectiveDate;
    }

    @JsonSerialize(using= DateIsoSerializer.class)
    public Date getInactiveDate() {
        return inactiveDate;
    }

    @JsonDeserialize(using=DateIsoDeSerializer.class)
    public void setInactiveDate(Date inactiveDate) {
        this.inactiveDate = inactiveDate;
    }

    public Integer getCreateEmployeeNumber() {
        return createEmployeeNumber;
    }

    public void setCreateEmployeeNumber(Integer createEmployeeNumber) {
        this.createEmployeeNumber = createEmployeeNumber;
    }

    @JsonSerialize(using= DateIsoSerializer.class)
    public Date getCreateDateTime() {
        return createDateTime;
    }

    @JsonDeserialize(using=DateIsoDeSerializer.class)
    public void setCreateDateTime(Date createDateTime) {
        this.createDateTime = createDateTime;
    }

    public Integer getUpdateEmployeeNumber() {
        return updateEmployeeNumber;
    }

    public void setUpdateEmployeeNumber(Integer updateEmployeeNumber) {
        this.updateEmployeeNumber = updateEmployeeNumber;
    }

    @JsonSerialize(using= DateIsoSerializer.class)
    public Date getUpdateDateTime() {
        return updateDateTime;
    }

    @JsonDeserialize(using=DateIsoDeSerializer.class)
    public void setUpdateDateTime(Date updateDateTime) {
        this.updateDateTime = updateDateTime;
    }
}
