package com.smilebrands.liberty.finance.model;

import com.fasterxml.jackson.databind.annotation.JsonDeserialize;
import com.fasterxml.jackson.databind.annotation.JsonSerialize;
import com.smilebrands.liberty.model.BaseObject;
import com.smilebrands.liberty.serializer.NewDateIsoDeSerializer;
import com.smilebrands.liberty.serializer.NewDateIsoSerializer;
import org.apache.commons.lang.builder.ToStringBuilder;
import org.apache.commons.lang.builder.ToStringStyle;

import javax.persistence.*;
import java.util.Date;

/**
 * <AUTHOR>
 * @version 01/22/2016
 */
@Entity
@Table(name = "FINANCE_VENDOR_SETTINGS")
@NamedQueries({
    @NamedQuery(name  = "FinanceVendorSetting.getSetting",
                query = "from FinanceVendorSetting s " +
                        "where s.isActive = 1 and s.vendorName = ?1 and s.vendorSettingName = ?2 and s.vendorSettingRegion = ?3 "
    ),
    @NamedQuery(name  = "FinanceVendorSetting.getCareCreditSettings",
                query = "from FinanceVendorSetting s " +
                        "where s.isActive = 1 and s.vendorName = 'CareCredit' and s.vendorSettingRegion = ?1 "
    ),
    @NamedQuery(name  = "FinanceVendorSetting.getCareCreditRegions",
                query = "select distinct s.vendorSettingRegion " +
                        "from FinanceVendorSetting s " +
                        "where s.isActive = 1 and s.vendorName = 'CareCredit' "
    )
})
public class FinanceVendorSetting extends BaseObject {

    @Id
    @Column(name = "VENDOR_SETTING_ID")
    @GeneratedValue(generator = "FinanceVendorSettingSeq")
    @SequenceGenerator(name = "FinanceVendorSettingSeq", sequenceName = "FINANCE_VENDOR_SETTINGS_SEQ", allocationSize = 1)
    private Integer vendorSettingId;

    @Column(name = "VENDOR_NAME")
    private String vendorName;

    @Column(name = "VENDOR_SETTING_NAME")
    private String vendorSettingName;

    @Column(name = "VENDOR_SETTING_DESCRIPTION")
    private String vendorSettingDescription;

    @Column(name = "VENDOR_SETTING_VALUE")
    private String vendorSettingValue;

    @Column(name = "VENDOR_SETTING_REGION")
    private String vendorSettingRegion;

    @Column(name = "IS_ACTIVE")
    private Boolean isActive;

    @Column(name = "CREATE_DATETIME")
    @Temporal(TemporalType.TIMESTAMP)
    private Date createDatetime = new Date();

    @Column(name = "CREATE_EMPLOYEE")
    private String createEmployee;

    @Column(name = "UPDATE_DATETIME")
    @Temporal(TemporalType.TIMESTAMP)
    private Date updateDatetime;

    @Column(name = "UPDATE_EMPLOYEE")
    private String updateEmployee;

    public Integer getVendorSettingId() {
        return vendorSettingId;
    }

    public void setVendorSettingId(Integer vendorSettingId) {
        this.vendorSettingId = vendorSettingId;
    }

    public String getVendorName() {
        return vendorName;
    }

    public void setVendorName(String vendorName) {
        this.vendorName = vendorName;
    }

    public String getVendorSettingName() {
        return vendorSettingName;
    }

    public void setVendorSettingName(String vendorSettingName) {
        this.vendorSettingName = vendorSettingName;
    }

    public String getVendorSettingDescription() {
        return vendorSettingDescription;
    }

    public void setVendorSettingDescription(String vendorSettingDescription) {
        this.vendorSettingDescription = vendorSettingDescription;
    }

    public String getVendorSettingValue() {
        return vendorSettingValue;
    }

    public void setVendorSettingValue(String vendorSettingValue) {
        this.vendorSettingValue = vendorSettingValue;
    }

    public String getVendorSettingRegion() {
        return vendorSettingRegion;
    }

    public void setVendorSettingRegion(String vendorSettingRegion) {
        this.vendorSettingRegion = vendorSettingRegion;
    }

    public Boolean getActive() {
        return isActive;
    }

    public void setActive(Boolean active) {
        isActive = active;
    }

    @JsonSerialize(using = NewDateIsoSerializer.class)
    public Date getCreateDatetime() {
        return createDatetime;
    }

    @JsonDeserialize(using = NewDateIsoDeSerializer.class)
    public void setCreateDatetime(Date createDatetime) {
        this.createDatetime = createDatetime;
    }

    public String getCreateEmployee() {
        return createEmployee;
    }

    public void setCreateEmployee(String createEmployee) {
        this.createEmployee = createEmployee;
    }

    @JsonSerialize(using = NewDateIsoSerializer.class)
    public Date getUpdateDatetime() {
        return updateDatetime;
    }

    @JsonDeserialize(using = NewDateIsoDeSerializer.class)
    public void setUpdateDatetime(Date updateDatetime) {
        this.updateDatetime = updateDatetime;
    }

    public String getUpdateEmployee() {
        return updateEmployee;
    }

    public void setUpdateEmployee(String updateEmployee) {
        this.updateEmployee = updateEmployee;
    }

    @Override
    public String toString() {
        return ToStringBuilder.reflectionToString(this,
                ToStringStyle.MULTI_LINE_STYLE);
    }

}