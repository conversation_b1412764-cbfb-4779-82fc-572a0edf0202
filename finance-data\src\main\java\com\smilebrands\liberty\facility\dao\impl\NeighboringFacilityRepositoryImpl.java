package com.smilebrands.liberty.facility.dao.impl;

import com.smilebrands.liberty.cache.Cacheable;
import com.smilebrands.liberty.dao.SimpleAbstractDao;
import com.smilebrands.liberty.facility.dao.NeighboringFacilityCustom;
import com.smilebrands.liberty.facility.model.NeighboringFacility;

import javax.persistence.Query;
import java.util.List;

/**
 * User: <PERSON><PERSON>
 * Date: 5/10/13
 */
public class NeighboringFacilityRepositoryImpl extends SimpleAbstractDao implements NeighboringFacilityCustom {

    @Override
    @Cacheable(cacheName="FacilityLocationCollections")
    public List<NeighboringFacility> findNeighboringFacilities(Long facilityId, Integer zipCode, Integer distance) {
        return em.createNamedQuery("NeighboringFacility.findNeighboringFacilities")
                .setParameter(1, facilityId)
                .setParameter(2, zipCode)
                .setParameter(3, distance)
                .getResultList();
    }
}
