package com.smilebrands.liberty.facility.model;

import java.io.Serializable;
import javax.persistence.*;

/**
 *
 * <AUTHOR>
 */
@Entity
@Table(name = "FACILITY_AVAIL_SERVICE")
@NamedQueries({
    @NamedQuery(name = "FacilityAvailabilityService.findAll", query = "SELECT f FROM FacilityAvailabilityService f")})
public class FacilityAvailabilityService implements Serializable {

    private static final long serialVersionUID = 1L;
    @Id
    @Basic(optional = false)
    @Column(name = "AVAIL_SERVICE_ID")
    private Long serviceId;

    @Basic(optional = false)
    @Column(name = "SERVICE")
    private String service;
    
    /**
     * @return the serviceId
     */
    public Long getServiceId() {
        return serviceId;
    }

    /**
     * @param serviceId the serviceId to set
     */
    public void setServiceId(Long serviceId) {
        this.serviceId = serviceId;
    }

    /**
     * @return the service
     */
    public String getService() {
        return service;
    }

    /**
     * @param service the service to set
     */
    public void setService(String service) {
        this.service = service;
    }

    @Override
    public int hashCode() {
        int hash = 0;
        hash += (getServiceId() != null ? getServiceId().hashCode() : 0);
        return hash;
    }

    @Override
    public boolean equals(Object object) {
        // TODO: Warning - this method won't work in the case the id fields are not set
        if (!(object instanceof FacilityAvailabilityService)) {
            return false;
        }
        FacilityAvailabilityService other = (FacilityAvailabilityService) object;
        if ((this.getServiceId() == null && other.getServiceId() != null) || (this.getServiceId() != null && !this.serviceId.equals(other.serviceId))) {
            return false;
        }
        return true;
    }

    @Override
    public String toString() {
        return "com.smilebrands.cal.model.jpa.FacilityAvailabilityService[availServiceId=" + getServiceId() + "]";
    }
}
