--------------------------------------------------------
--  DDL for Table PATIENT_ADDRESS
--------------------------------------------------------

  CREATE TABLE "LIBERTY_DATA"."PATIENT_ADDRESS"
   (	"PATIENT_ID" NUMBER(10,0), 
	"ADDRESS_ID" NUMBER(10,0), 
	"ADDRESS_ACTIVE" CHAR(1 BYTE), 
	"ACTIVE_DATE" TIMESTAMP (6), 
	"INACTIVE_DATE" TIMESTAMP (6), 
	"ADDRESS_TYPE" VARCHAR2(10 BYTE), 
	"ADDRESS_LINE1" VARCHAR2(30 BYTE), 
	"ADDRESS_LINE2" VARCHAR2(30 BYTE), 
	"ADDRESS_CITY" VARCHAR2(30 BYTE), 
	"ADDRESS_STATE" VARCHAR2(30 BYTE), 
	"ZIP_CODE" VARCHAR2(30 BYTE), 
	"DATE_ENTERED" TIMESTAMP (6), 
	"EMP_ENTERED" NUMBER(6,0), 
	"DATE_UPDATED" TIMESTAMP (6), 
	"EMP_UPDATED" NUMBER(6,0)
   ) PCTFREE 10 PCTUSED 40 INITRANS 1 MAXTRANS 255 NOCOMPRESS LOGGING
  STORAGE(INITIAL 65536 NEXT 1048576 MINEXTENTS 1 MAXEXTENTS **********
  PCTINCREASE 0 FREELISTS 1 FREELIST GROUPS 1 BUFFER_POOL DEFAULT)
  TABLESPACE "BNDNEW_DATA" ;
