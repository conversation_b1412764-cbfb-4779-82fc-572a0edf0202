--------------------------------------------------------
--  DDL for Table PATIENT_PHONE
--------------------------------------------------------

  CREATE TABLE "LIBERTY_DATA"."PATIENT_PHONE"
   (	"PHONE_ID" NUMBER(10,0), 
	"PATIENT_ID" NUMBER(10,0), 
	"PHONE_ACTIVE" CHAR(1 BYTE), 
	"ACTIVE_DATE" TIMESTAMP (6), 
	"INACTIVE_DATE" TIMESTAMP (6), 
	"PREFERRED" CHAR(1 BYTE), 
	"PHONE_TYPE" VARCHAR2(15 BYTE), 
	"PHONE_EXT" VARCHAR2(10 BYTE), 
	"PHONE_NUMBER" VARCHAR2(14 BYTE), 
	"DATE_ENTERED" TIMESTAMP (6), 
	"EMP_ENTERED" NUMBER(6,0), 
	"DATE_UPDATED" TIMESTAMP (6), 
	"EMP_UPDATED" NUMBER(6,0), 
	"SMS_ALLOWED" CHAR(1 BYTE) DEFAULT 0
   ) PCTFREE 10 PCTUSED 40 INITRANS 1 MAXTRANS 255 NOCOMPRESS LOGGING
  STORAGE(INITIAL 65536 NEXT 1048576 MINEXTENTS 1 MAXEXTENTS **********
  PCTINCREASE 0 FREELISTS 1 FREELIST GROUPS 1 BUFFER_POOL DEFAULT)
  TABLESPACE "BNDNEW_DATA" ;
