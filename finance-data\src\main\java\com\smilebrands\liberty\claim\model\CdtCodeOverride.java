package com.smilebrands.liberty.claim.model;

import com.smilebrands.liberty.model.BaseObject;

import javax.persistence.*;
import java.util.Date;

/**
 * User: <PERSON>
 * Date: 7/31/13
 */
@Entity
@NamedQueries(value = {
        @NamedQuery(name = "CdtCodeOverride.findAllByInsuranceCompanyId",
                query = "SELECT c FROM CdtCodeOverride c " +
                        "WHERE c.insuranceCompanyId = ? " +
                        "AND c.isActive = '1'")
})
@Table(name = "CDT_CODE_OVERRIDE")
@SuppressWarnings("serial")
public class CdtCodeOverride extends BaseObject {

    @Id
    @Column(name = "INS_COMPANY_ID")
    private Integer insuranceCompanyId;

    @Id
    @Column(name = "CDT_CODE")
    protected String cdtCode;

    @Column(name = "OVERRIDE_CDT_CODE")
    protected String overrideCdtCode;

    @Temporal(TemporalType.TIMESTAMP)
    @Column(name = "CREATE_DATETIME")
    private Date creationDatetime = new Date();

    @Column(name = "CREATE_EMPLOYEE")
    private Integer createEmployee = 0;

    @Temporal(TemporalType.TIMESTAMP)
    @Column(name = "UPDATE_TIMESTAMP")
    private Date updateTimestamp;

    @Column(name = "UPDATE_EMPLOYEE")
    private Integer updateEmployee;

    @Column(name = "IS_ACTIVE")
    protected String isActive;

    public Integer getInsuranceCompanyId() {
        return insuranceCompanyId;
    }

    public void setInsuranceCompanyId(Integer insuranceCompanyId) {
        this.insuranceCompanyId = insuranceCompanyId;
    }

    public String getCdtCode() {
        return cdtCode;
    }

    public void setCdtCode(String cdtCode) {
        this.cdtCode = cdtCode;
    }

    public String getOverrideCdtCode() {
        return overrideCdtCode;
    }

    public void setOverrideCdtCode(String overrideCdtCode) {
        this.overrideCdtCode = overrideCdtCode;
    }

    public Date getCreationDatetime() {
        return creationDatetime;
    }

    public void setCreationDatetime(Date creationDatetime) {
        this.creationDatetime = creationDatetime;
    }

    public Integer getCreateEmployee() {
        return createEmployee;
    }

    public void setCreateEmployeeNumber(Integer createEmployee) {
        this.createEmployee = createEmployee;
    }

    public Date getUpdateTimestamp() {
        return updateTimestamp;
    }

    public void setUpdateTimestamp(Date updateTimestamp) {
        this.updateTimestamp = updateTimestamp;
    }

    public Integer getUpdateEmployee() {
        return updateEmployee;
    }

    public void setUpdateEmployee(Integer updateEmployee) {
        this.updateEmployee = updateEmployee;
    }

    public String getActive() {
        return isActive;
    }

    public void setActive(String active) {
        isActive = active;
    }

}
