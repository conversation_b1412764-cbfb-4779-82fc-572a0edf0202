--------------------------------------------------------
--  DDL for Table INSURANCE_PLAN_TYPE
--------------------------------------------------------

  CREATE TABLE "LIBERTY_DATA"."INSURANCE_PLAN_TYPE"
   (	"PLAN_TYPE" VARCHAR2(2 BYTE), 
	"PLAN_TYPE_GROUP" VARCHAR2(5 BYTE), 
	"PLAN_TYPE_DESCRIPTON" VARCHAR2(50 BYTE), 
	"CREATE_EMPLOYEE" NUMBER(10,0), 
	"CREATE_DATETIME" TIMESTAMP (6), 
	"UPDATE_EMPLOYEE" NUMBER(10,0), 
	"UPDATE_DATETIME" TIMESTAMP (6)
   ) PCTFREE 10 PCTUSED 40 INITRANS 1 MAXTRANS 255 NOCOMPRESS LOGGING
  STORAGE(INITIAL 65536 NEXT 1048576 MINEXTENTS 1 MAXEXTENTS 2147483645
  PCTINCREASE 0 FREELISTS 1 FREELIST GROUPS 1 BUFFER_POOL DEFAULT)
  TABLESPACE "BNDNEW_DATA" ;
