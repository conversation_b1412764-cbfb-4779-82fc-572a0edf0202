--------------------------------------------------------
--  DDL for Table LABORATORY
--------------------------------------------------------

  CREATE TABLE "LIBERTY_DATA"."LABORATORY"
   (	"LAB_ID" NUMBER(10,0), 
	"LAB_NAME" VARCHAR2(30 BYTE), 
	"LAB_TYPE" VARCHAR2(30 BYTE), 
	"LAB_ADDRESS_LINE1" VARCHAR2(30 BYTE), 
	"LAB_ADDRESS_LINE2" VARCHAR2(30 BYTE), 
	"LAB_CITY" VARCHAR2(30 BYTE), 
	"LAB_STATE" VARCHAR2(30 BYTE), 
	"LAB_ZIP_CODE" VARCHAR2(30 BYTE), 
	"LAB_PHONE" VARCHAR2(14 BYTE), 
	"ACCOUNT_REPRESENTATIVE" VARCHAR2(60 BYTE), 
	"ACCOUNT_REP_EMAIL" VARCHAR2(50 BYTE), 
	"ACCOUNT_REP_PHONE" VARCHAR2(14 BYTE), 
	"ACTIVE_DATETIME" TIMESTAMP (6), 
	"INACTIVE_DATETIME" TIMESTAMP (6), 
	"CREATE_EMPLOYEE_NUMBER" NUMBER(6,0), 
	"CREATE_DATETIME" TIMESTAMP (6), 
	"UPDATE_EMPLOYEE_NUMBER" NUMBER(6,0), 
	"UPDATE_DATETIME" TIMESTAMP (6)
   ) PCTFREE 10 PCTUSED 40 INITRANS 1 MAXTRANS 255 NOCOMPRESS LOGGING
  STORAGE(INITIAL 65536 NEXT 1048576 MINEXTENTS 1 MAXEXTENTS **********
  PCTINCREASE 0 FREELISTS 1 FREELIST GROUPS 1 BUFFER_POOL DEFAULT)
  TABLESPACE "BNDNEW_DATA" ;
