package com.smilebrands.liberty.facility.model;

import com.smilebrands.liberty.model.BaseObject;
import org.hibernate.annotations.CacheConcurrencyStrategy;

import javax.persistence.*;
import java.util.Calendar;

/**
 * 
 * <AUTHOR>
 * @version  10/22/2010
 */
@Entity
@NamedQueries({
    @NamedQuery(name = "Facility.findAllFacilities", query = "select f from Facility f where f.inactivationDatetime is null"),
    @NamedQuery(name = "Facility.findFacilitiesById", query = "select f from Facility f where f.inactivationDatetime is null AND facilityId=1"),
    @NamedQuery(name = "Facility.findAllFacilitiesByIds", query = "select f from Facility f where facilityId IN (?1)")
})
@org.hibernate.annotations.Cache(usage = CacheConcurrencyStrategy.NONSTRICT_READ_WRITE)
@Table(name = "V_FACILITY")
public class Facility extends BaseObject {

    @Id
    @Column(name = "FACILITY_ID")
    private Integer facilityId;

    @Column(name = "FACILITY_NAME")
    private String facilityName;

    @Column(name = "ADDRESS_LINE1")
    private String addressLine1;

    @Column(name = "ADDRESS_LINE2")
    private String addressLine2;

    @Column(name = "CITY")
    private String city;

    @Column(name = "STATE")
    private String state;

    @Column(name = "ZIPCODE")
    private String zipCode;

    @Column(name = "PHONE_NUMBER")
    private Long phoneNumber;

    @Column(name = "FAX_NUMBER")
    private Long faxNumber;

    @Column(name = "LATITUDE")
    private Double latitude;

    @Column(name = "LONGITUDE")
    private Double longitude;

    @Temporal(TemporalType.TIMESTAMP)
    @Column(name = "CREATION_DATETIME")
    private Calendar creationDatetime;

    @Temporal(TemporalType.TIMESTAMP)
    @Column(name = "INACTIVATION_DATETIME")
    private Calendar inactivationDatetime;

    public void setFacilityId(Integer facilityId) {
        this.facilityId = facilityId;
    }

    public Integer getFacilityId() {
        return this.facilityId;
    }

    public void setFacilityName(String facilityName) {
        this.facilityName = facilityName;
    }

    public String getFacilityName() {
        return this.facilityName;
    }

    public void setCreationDatetime(Calendar creationDatetime) {
        this.creationDatetime = creationDatetime;
    }

    public Calendar getCreationDatetime() {
        return this.creationDatetime;
    }

    public void setInactivationDatetime(Calendar inactivationDatetime) {
        this.inactivationDatetime = inactivationDatetime;
    }

    public Calendar getInactivationDatetime() {
        return this.inactivationDatetime;
    }

    /**
     * @return the addressLine1
     */
    public String getAddressLine1() {
        return addressLine1;
    }

    /**
     * @param addressLine1 the addressLine1 to set
     */
    public void setAddressLine1(String addressLine1) {
        this.addressLine1 = addressLine1;
    }

    /**
     * @return the addressLine2
     */
    public String getAddressLine2() {
        return addressLine2;
    }

    /**
     * @param addressLine2 the addressLine2 to set
     */
    public void setAddressLine2(String addressLine2) {
        this.addressLine2 = addressLine2;
    }

    /**
     * @return the city
     */
    public String getCity() {
        return city;
    }

    /**
     * @param city the city to set
     */
    public void setCity(String city) {
        this.city = city;
    }

    /**
     * @return the state
     */
    public String getState() {
        return state;
    }

    /**
     * @param state the state to set
     */
    public void setState(String state) {
        this.state = state;
    }

    /**
     * @return the zipcode
     */
    public String getZipCode() {
        return zipCode;
    }

    /**
     * @param zipCode the zipcode to set
     */
    public void setZipCode(String zipCode) {
        this.zipCode = zipCode;
    }

    /**
     * @return the phoneNumber
     */
    public Long getPhoneNumber() {
        return phoneNumber;
    }

    /**
     * @param phoneNumber the phoneNumber to set
     */
    public void setPhoneNumber(Long phoneNumber) {
        this.phoneNumber = phoneNumber;
    }

    /**
     * @return the faxNumber
     */
    public Long getFaxNumber() {
        return faxNumber;
    }

    /**
     * @param faxNumber the faxNumber to set
     */
    public void setFaxNumber(Long faxNumber) {
        this.faxNumber = faxNumber;
    }

    /**
     * @return  the latitude
     */
    public Double getLatitude() {
        return latitude;
    }

    /**
     * @param latitude the latitude to set
     */
    public void setLatitude(Double latitude) {
        this.latitude = latitude;
    }

    /**
     * @return  the longitude
     */
    public Double getLongitude() {
        return longitude;
    }

    /**
     * @param longitude the longitude to set
     */
    public void setLongitude(Double longitude) {
        this.longitude = longitude;
    }

    @Override
    public int hashCode() {
        final int prime = 31;
        int result = 1;
        result = (int) (prime * result + ((facilityId == null) ? 0 : facilityId.hashCode()));
        return result;
    }

    public boolean equals(Object obj) {
        if (obj == this) {
            return true;
        }
        if (!(obj instanceof Facility)) {
            return false;
        }
        Facility equalCheck = (Facility) obj;
        if ((facilityId == null && equalCheck.facilityId != null) || (facilityId != null && equalCheck.facilityId == null)) {
            return false;
        }
        if (facilityId != null && !facilityId.equals(equalCheck.facilityId)) {
            return false;
        }
        return true;
    }

    public String getAddressLine(){
        String result = "";
        boolean hasLine1 = false;
        if(this.addressLine1 != null && this.addressLine1.trim().length() > 0){
            result = this.addressLine1;
            hasLine1 = true;
        }
        if(this.addressLine2 != null && this.addressLine2.trim().length() > 0){
            result += (hasLine1 ? ", " : "")  + this.addressLine2;
        }
        return result;
    }
    public String getCityStateAndZip(){
        String result = "";
        boolean  hasCity = false, hasState = false;
        if(this.city != null && this.city.trim().length() > 0){
            result += this.city;
            hasCity = true;
        }
        if(this.state != null && this.state.trim().length() > 0){
            result += (hasCity ? ", " : "") + this.state;
            hasState = true;
        }
        if(this.zipCode != null && this.zipCode.trim().length() > 0){
            result += (hasState ? " " : hasCity ? ", " : "") + this.zipCode;
        }
        return result;
    }

    /** Get Brand from Facility Name. **/
    public String getBrandFromFacilityName() {
        String facilityName =  this.facilityName;
        if (facilityName.toLowerCase().contains("monarch")) {
            return "monarchdental";
        }
        if (facilityName.toLowerCase().contains("castle")) {
            return "castledental";
        }

        return "brightnow";
    }

    /** Get Brand from Facility Name. **/
    public String getFormattedBrandFromFacilityName() {
        String facilityName =  this.facilityName;
        if (facilityName.toLowerCase().contains("monarch")) {
            return "Monarch Dental";
        }
        if (facilityName.toLowerCase().contains("castle")) {
            return "Castle Dental";
        }

        return "Bright Now! Dental";
    }

    /** Format Brand for Social Media. **/
    public String toProperSocialName(String brand) {
        brand = brand != null && brand.trim().length() > 0 ? brand : getBrandFromFacilityName();
        if (brand.contains("brightnow")) {
            return "brightnowdental";
        }
        return brand;
    }

    /** Get Brand Logo From Brand. **/
    public String getBrandLogo(String brand) {
        brand = brand != null && brand.trim().length() > 0 ? brand : getBrandFromFacilityName();
        if (brand.toLowerCase().contains("monarchdental")) {
            return "logo_md.gif";
        }

        if (brand.toLowerCase().contains("castledental")) {
            return "logo_cd.gif";
        }

        return "logo_bn.gif";
    }

    /** Format Phone Number. **/
    public String getProperFormatPhone(String phone) {
        phone = phone != null && phone.trim().length() > 0 ? phone : this.phoneNumber.toString();
        if (phone.length() == 10) {
            return "(" + phone.substring(0,3) + ") " + phone.substring(3,6) + "-" + phone.substring(6);
        }
        return phone;
    }

    /** Dash Phone Number. **/
    public String getDashFormatPhone(String phone) {
        phone = phone != null && phone.trim().length() > 0 ? phone : this.phoneNumber.toString();
        if (phone.length() == 10) {
            return "" + phone.substring(0,3) + "-" + phone.substring(3,6) + "-" + phone.substring(6);
        }
        return phone;
    }
}
