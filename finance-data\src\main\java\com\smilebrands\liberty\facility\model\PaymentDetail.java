package com.smilebrands.liberty.facility.model;

import com.smilebrands.liberty.constants.BatchType;
import com.smilebrands.liberty.constants.DailyReconciliationFundingSourceType;
import com.smilebrands.liberty.constants.FundingSourceType;
import com.smilebrands.liberty.model.BaseObject;
import com.smilebrands.liberty.serializer.DateIsoDeSerializer;
import com.smilebrands.liberty.serializer.DateIsoSerializer;
import org.codehaus.jackson.annotate.JsonIgnore;
import org.codehaus.jackson.map.annotate.JsonDeserialize;
import org.codehaus.jackson.map.annotate.JsonSerialize;

import javax.persistence.*;
import java.math.BigDecimal;
import java.util.Date;

/**
 * <AUTHOR> <PERSON><PERSON>
 *         Smile Brands Inc.
 *         Date: 08/22/13
 */
@Entity
public class PaymentDetail extends BaseObject {

    private Long transactionId;

    private FundingSourceType fundingSourceType;

    private BatchType batchType;

    private Long patientId;

    private String patientFirstName;

    private String patientLastName;

    private BigDecimal officeAmount;

    private BigDecimal otherAmount;

    private Integer employeeId;

    private String employeeFirstName;

    private String employeeLastName;

    private Date voidDatetime;

    public PaymentDetail(Long transactionId, FundingSourceType fundingSourceType, BatchType batchType, Long patientId, String patientFirstName, String patientLastName, BigDecimal officeAmount, BigDecimal otherAmount, Integer employeeId, String employeeFirstName, String employeeLastName, Date voidDatetime) {
        this.transactionId = transactionId;
        this.fundingSourceType = fundingSourceType;
        this.batchType = batchType;
        this.patientId = patientId;
        this.patientFirstName = patientFirstName;
        this.patientLastName = patientLastName;
        this.officeAmount = officeAmount.abs();
        this.otherAmount = otherAmount.abs();
        this.employeeId = employeeId;
        this.employeeFirstName = employeeFirstName;
        this.employeeLastName = employeeLastName;
        this.voidDatetime = voidDatetime;
    }

    public Long getTransactionId() {
        return transactionId;
    }

    public void setTransactionId(Long transactionId) {
        this.transactionId = transactionId;
    }

    public FundingSourceType getFundingSourceType() {
        return fundingSourceType;
    }

    public void setFundingSourceType(FundingSourceType fundingSourceType) {
        this.fundingSourceType = fundingSourceType;
    }

    public BatchType getBatchType() {
        return batchType;
    }

    public void setBatchType(BatchType batchType) {
        this.batchType = batchType;
    }

    public Long getPatientId() {
        return patientId;
    }

    public void setPatientId(Long patientId) {
        this.patientId = patientId;
    }

    public String getPatientFirstName() {
        return patientFirstName;
    }

    public void setPatientFirstName(String patientFirstName) {
        this.patientFirstName = patientFirstName;
    }

    public String getPatientLastName() {
        return patientLastName;
    }

    public void setPatientLastName(String patientLastName) {
        this.patientLastName = patientLastName;
    }

    public BigDecimal getOfficeAmount() {
        return officeAmount;
    }

    public void setOfficeAmount(BigDecimal officeAmount) {
        this.officeAmount = officeAmount;
    }

    public BigDecimal getOtherAmount() {
        return otherAmount;
    }

    public void setOtherAmount(BigDecimal otherAmount) {
        this.otherAmount = otherAmount;
    }

    public Integer getEmployeeId() {
        return employeeId;
    }

    public void setEmployeeId(Integer employeeId) {
        this.employeeId = employeeId;
    }

    public String getEmployeeFirstName() {
        return employeeFirstName;
    }

    public void setEmployeeFirstName(String employeeFirstName) {
        this.employeeFirstName = employeeFirstName;
    }

    public String getEmployeeLastName() {
        return employeeLastName;
    }

    public void setEmployeeLastName(String employeeLastName) {
        this.employeeLastName = employeeLastName;
    }

    @JsonSerialize(using= DateIsoSerializer.class)
    public Date getVoidDatetime() {
        return voidDatetime;
    }

    @JsonDeserialize(using=DateIsoDeSerializer.class)
    public void setVoidDatetime(Date voidDatetime) {
        this.voidDatetime = voidDatetime;
    }
}
