package com.smilebrands.liberty.facility.dao;

import com.smilebrands.liberty.facility.model.FacilityFinanceSummaryReport;
import com.smilebrands.liberty.facility.model.vo.ClosestFacilityVo;
import com.smilebrands.liberty.facility.model.vo.FacilityVo;
import com.smilebrands.liberty.facility.model.vo.FinanceFacilityVo;
import com.smilebrands.liberty.facility.model.vo.QSIClinicFacilityVo;
import org.springframework.dao.DataAccessException;

import java.util.Date;
import java.util.List;

public interface FacilityDao {

    public List<ClosestFacilityVo> getClosestFacilities(Integer zipcode, Integer maxRows) throws DataAccessException;;

    public List<QSIClinicFacilityVo> getQSIClinicFacilities(List<Integer> facilityIds) throws DataAccessException;

    public List<FacilityVo> getFacilityById(Integer facilityId);

    public FacilityVo getFacilityBillingCenter(Integer facilityId);

    public FacilityVo getFacilityRemitAddress(Integer facilityId);

    public List<FacilityFinanceSummaryReport> getPaymentsAdjustmentsFromMonthToDate(Integer facilityId, Date firstDayOfMonth, Date datePosted);

    public FinanceFacilityVo getFacilityForFinance(Integer facilityId);

    public List<FinanceFacilityVo> getAllFacilitiesForFinance();

    public List<FinanceFacilityVo> getAllFacilitiesForFinance(List<Integer> facilityIds);

    public boolean isLibertySupported(Integer facilityId);
}
