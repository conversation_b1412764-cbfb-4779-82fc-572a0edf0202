package com.smilebrands.liberty.claim.model;

import com.smilebrands.liberty.model.BaseObject;
import com.smilebrands.liberty.serializer.DateIsoDeSerializer;
import com.smilebrands.liberty.serializer.DateIsoSerializer;
import org.codehaus.jackson.map.annotate.JsonDeserialize;
import org.codehaus.jackson.map.annotate.JsonSerialize;
import org.hibernate.annotations.Fetch;
import org.hibernate.annotations.FetchMode;
import org.hibernate.annotations.NotFound;
import org.hibernate.annotations.NotFoundAction;

import javax.persistence.*;
import java.util.Date;
import java.util.List;
import java.util.Set;

/**
 * Created by phongpham on 3/3/14.
 */
@Entity

@NamedQueries({
        @NamedQuery(
                name = "AttachmentTransmitRequest.getById",
                query = "select r from AttachmentTransmitRequest r " +
                        " join fetch r.attachmentTransmitHistories h " +
                        " left join fetch h.requiredAttachment cra " +
                        " left join fetch cra.requirement " +
                        " left join fetch h.attachmentResolution " +
                        "where r.attachmentTransmitRequestId = ?"
        )
})

@Table(name = "ATTACHMENT_TRANSMIT_REQUEST")
public class AttachmentTransmitRequest  extends BaseObject {

    @Id
    @Column(name = "ATTACHMENT_TRANSMIT_REQUEST_ID")
    @GeneratedValue(generator = "AttachmentTxRequestSeq")
    @SequenceGenerator(name = "AttachmentTxRequestSeq", sequenceName = "ATTACHMENT_TX_REQUEST_ID_SEQ", allocationSize = 1)
    private Long attachmentTransmitRequestId;

    @Column(name = "CREATED_DATETIME", insertable = true, updatable = false)
    @Temporal(TemporalType.TIMESTAMP)
    private Date createdDatetime = new Date();

    @Column(name = "CREATED_EMPLOYEE_NUMBER")
    private Integer createEmployeeNumber;

    @Column(name = "COMPLETED_DATETIME")
    @Temporal(TemporalType.TIMESTAMP)
    private Date completedDatetime;

    @Column(name = "EMAIL")
    private String email;

    @Column(name = "REQUEST_TYPE")
    private String requestType; //BULK or SINGLE

    @Column(name = "NEA_NUMBER")
    private String neaNumber;

    @Transient
    private List<Claim> claimsToTransmitAttachment;

    @Transient
    private List<String> claimIds;

    @NotFound(action= NotFoundAction.IGNORE)
    @OneToMany(fetch = FetchType.EAGER)
    @Fetch(FetchMode.JOIN)
    @JoinColumn(name = "ATTACHMENT_TRANSMIT_REQUEST_ID", referencedColumnName = "ATTACHMENT_TRANSMIT_REQUEST_ID", updatable = false, insertable = false, nullable = true)
    private Set<AttachmentTransmitHistory> attachmentTransmitHistories;

    public Long getAttachmentTransmitRequestId() {
        return attachmentTransmitRequestId;
    }

    public void setAttachmentTransmitRequestId(Long attachmentTransmitRequestId) {
        this.attachmentTransmitRequestId = attachmentTransmitRequestId;
    }

    @JsonSerialize(using= DateIsoSerializer.class)
    public Date getCreatedDatetime() {
        return createdDatetime;
    }

    @JsonDeserialize(using=DateIsoDeSerializer.class)
    public void setCreatedDatetime(Date createdDatetime) {
        this.createdDatetime = createdDatetime;
    }

    public Integer getCreateEmployeeNumber() {
        return createEmployeeNumber;
    }

    public void setCreateEmployeeNumber(Integer createEmployeeNumber) {
        this.createEmployeeNumber = createEmployeeNumber;
    }

    @JsonSerialize(using= DateIsoSerializer.class)
    public Date getCompletedDatetime() {
        return completedDatetime;
    }

    @JsonDeserialize(using=DateIsoDeSerializer.class)
    public void setCompletedDatetime(Date completedDatetime) {
        this.completedDatetime = completedDatetime;
    }

    public String getEmail() {
        return email;
    }

    public void setEmail(String email) {
        this.email = email;
    }

    public String getRequestType() {
        return requestType;
    }

    public void setRequestType(String requestType) {
        this.requestType = requestType;
    }

    public List<Claim> getClaimsToTransmitAttachment() {
        return claimsToTransmitAttachment;
    }

    public void setClaimsToTransmitAttachment(List<Claim> claimsToTransmitAttachment) {
        this.claimsToTransmitAttachment = claimsToTransmitAttachment;
    }

    public Set<AttachmentTransmitHistory> getAttachmentTransmitHistories() {
        return attachmentTransmitHistories;
    }

    public void setAttachmentTransmitHistories(Set<AttachmentTransmitHistory> attachmentTransmitHistories) {
        this.attachmentTransmitHistories = attachmentTransmitHistories;
    }

    public String getNeaNumber() { return neaNumber; }

    public void setNeaNumber(String neaNumber) { this.neaNumber = neaNumber; }

    public List<String> getClaimIds() { return claimIds; }

    public void setClaimIds(List<String> claimIds) { this.claimIds = claimIds; }
}
