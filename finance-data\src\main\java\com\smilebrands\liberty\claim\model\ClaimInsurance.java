package com.smilebrands.liberty.claim.model;

import com.smilebrands.liberty.constants.ClaimRelationshipType;
import com.smilebrands.liberty.model.BaseObject;
import com.smilebrands.liberty.utility.JsonDateDeSerializer;
import com.smilebrands.liberty.utility.JsonDateSerializer;
import org.codehaus.jackson.map.annotate.JsonDeserialize;
import org.codehaus.jackson.map.annotate.JsonSerialize;

import javax.persistence.*;
import java.util.Date;

/**
 * <AUTHOR> Smile Brands Inc., 2011
 */
@Entity
@Table(name = "CLAIM_INSURANCE")
@Inheritance(strategy = InheritanceType.SINGLE_TABLE)
@DiscriminatorColumn(name="INSURANCE_TYPE", discriminatorType=DiscriminatorType.STRING)
@SuppressWarnings("serial")
public abstract class ClaimInsurance extends BaseObject {

    @Id
    @Column(name = "CLAIM_INS_ID")
    @GeneratedValue(generator = "ClaimInsSeq")
    @SequenceGenerator(name = "ClaimInsSeq", sequenceName = "CLAIM_INS_SEQ", allocationSize = 1)
    protected Long claimInsuranceId;

    @Column(name = "NATIONAL_PAYOR_ID")
    private String nationalPayorId;

    @Column(name = "INS_PLAN_ID")
    private Long insurancePlanId;

    @Column(name = "INS_COMPANY_NAME")
    private String insuranceCompanyName;

    @Column(name = "ADDRESS_STREET")
    private String addressStreet;

    @Column(name = "ADDRESS_CITY")
    private String addressCity;

    @Column(name = "ADDRESS_STATE")
    private String addressState;

    @Column(name = "ZIP_CODE")
    private String addressZipCode;

    @Column(name = "PLAN_NAME")
    private String planName;

    @Column(name = "INSURANCE_PLAN")
    private String insurancePlan;

    @Column(name = "EMPLOYER_NAME")
    private String employerName;

    @Column(name = "GROUP_NUMBER")
    private String groupNumber;

    @Column(name = "POLICY_NUMBER")
    private String policyNumber;

    @Column(name = "MEMBER_ID")
    private String memberId;

    @Column(name = "INSURED_FIRST_NAME")
    private String insuredFirstName;

    @Column(name = "INSURED_MIDDLE_NAME")
    private String insuredMiddleName;

    @Column(name = "INSURED_LAST_NAME")
    private String insuredLastName;

    @Column(name = "INSURED_GENDER")
    private String insuredGender;

    @Column(name ="INSURED_SSN")
    private String insuredSocialSecurityNumber;

    @Temporal(TemporalType.DATE)
    @Column(name = "INSURED_DATE_OF_BIRTH")
    private Date insuredDateOfBirth;

    @Column(name = "INSURED_STREET")
    private String insuredStreet;

    @Column(name = "INSURED_CITY")
    private String insuredCity;

    @Column(name = "INSURED_STATE")
    private String insuredState;

    @Column(name = "INSURED_ZIP_CODE")
    private String insuredZipCode;

    @Column(name = "RELATIONSHIP_TO_INSURED")
    @Enumerated(EnumType.STRING)
    private ClaimRelationshipType relationshipToInsured;

    @Column(name = "INS_COMPANY_ID")
    private Long insuranceCompanyId;

    private transient String country = "USA";


    public Long getClaimInsuranceId() {
        return claimInsuranceId;
    }

    public void setClaimInsuranceId(Long claimInsuranceId) {
        this.claimInsuranceId = claimInsuranceId;
    }

    public String getNationalPayorId() {
        return nationalPayorId;
    }

    public void setNationalPayorId(String nationalPayorId) {
        this.nationalPayorId = nationalPayorId;
    }

    public Long getInsurancePlanId() { return insurancePlanId; }

    public void setInsurancePlanId(Long insurancePlanId) { this.insurancePlanId = insurancePlanId; }

    public String getInsuranceCompanyName() { return insuranceCompanyName; }

    public void setInsuranceCompanyName(String insuranceCompanyName) { this.insuranceCompanyName = insuranceCompanyName; }

    public String getAddressStreet() {
        return addressStreet;
    }

    public void setAddressStreet(String addressStreet) {
        this.addressStreet = addressStreet;
    }

    public String getAddressCity() {
        return addressCity;
    }

    public void setAddressCity(String addressCity) {
        this.addressCity = addressCity;
    }

    public String getAddressState() {
        return addressState;
    }

    public void setAddressState(String addressState) {
        this.addressState = addressState;
    }

    public String getAddressZipCode() {
        return addressZipCode;
    }

    public void setAddressZipCode(String addressZipCode) {
        this.addressZipCode = addressZipCode;
    }

    public String getPlanName() { return planName; }

    public void setPlanName(String planName) { this.planName = planName; }

    public String getInsurancePlan() { return insurancePlan; }

    public void setInsurancePlan(String insurancePlan) { this.insurancePlan = insurancePlan; }

    public String getEmployerName() {
        return employerName;
    }

    public void setEmployerName(String employerName) {
        this.employerName = employerName;
    }

    public String getGroupNumber() {
        return groupNumber;
    }

    public void setGroupNumber(String groupNumber) {
        this.groupNumber = groupNumber;
    }

    public String getPolicyNumber() {
        return policyNumber;
    }

    public void setPolicyNumber(String policyNumber) {
        this.policyNumber = policyNumber;
    }

    public String getMemberId() {
        return memberId;
    }

    public void setMemberId(String memberId) {
        this.memberId = memberId;
    }

    public String getInsuredFirstName() {
        return insuredFirstName;
    }

    public void setInsuredFirstName(String insuredFirstName) {
        this.insuredFirstName = insuredFirstName;
    }

    public String getInsuredMiddleName() {
        return insuredMiddleName;
    }

    public void setInsuredMiddleName(String insuredMiddleName) {
        this.insuredMiddleName = insuredMiddleName;
    }

    public String getInsuredLastName() {
        return insuredLastName;
    }

    public void setInsuredLastName(String insuredLastName) {
        this.insuredLastName = insuredLastName;
    }

    public String getInsuredGender() {
        return insuredGender;
    }

    public void setInsuredGender(String insuredGender) {
        this.insuredGender = insuredGender;
    }

    public String getInsuredSocialSecurityNumber() {
        return insuredSocialSecurityNumber;
    }

    public void setInsuredSocialSecurityNumber(String insuredSocialSecurityNumber) {
        this.insuredSocialSecurityNumber = insuredSocialSecurityNumber;
    }

    @JsonSerialize(using = JsonDateSerializer.class)
    public Date getInsuredDateOfBirth() {
        return insuredDateOfBirth;
    }

    @JsonDeserialize(using = JsonDateDeSerializer.class)
    public void setInsuredDateOfBirth(Date insuredDateOfBirth) {
        this.insuredDateOfBirth = insuredDateOfBirth;
    }

    public String getInsuredStreet() {
        return insuredStreet;
    }

    public void setInsuredStreet(String insuredStreet) {
        this.insuredStreet = insuredStreet;
    }

    public String getInsuredCity() {
        return insuredCity;
    }

    public void setInsuredCity(String insuredCity) {
        this.insuredCity = insuredCity;
    }

    public String getInsuredState() {
        return insuredState;
    }

    public void setInsuredState(String insuredState) {
        this.insuredState = insuredState;
    }

    public String getInsuredZipCode() {
        return insuredZipCode;
    }

    public void setInsuredZipCode(String insuredZipCode) {
        this.insuredZipCode = insuredZipCode;
    }

    public ClaimRelationshipType getRelationshipToInsured() {
        return relationshipToInsured;
    }

    public void setRelationshipToInsured(ClaimRelationshipType relationshipToInsured) {
        this.relationshipToInsured = relationshipToInsured;
    }

    public Long getInsuranceCompanyId() { return insuranceCompanyId; }

    public void setInsuranceCompanyId(Long insuranceCompanyId) { this.insuranceCompanyId = insuranceCompanyId; }

    /**
     * @return the country
     */
    public String getCountry() {
      return country;
    }

    /**
     * @param country the country to set
     */
    public void setCountry(String country) {
      this.country = country;
    }

    @Override
    public boolean equals(Object o) {
        if (this == o) return true;
        if (o == null || getClass() != o.getClass()) return false;

        ClaimInsurance that = (ClaimInsurance) o;

        if (claimInsuranceId != null ? !claimInsuranceId.equals(that.claimInsuranceId) : that.claimInsuranceId != null)
            return false;

        return true;
    }

    @Override
    public int hashCode() {
        return claimInsuranceId != null ? claimInsuranceId.hashCode() : 0;
    }

    public String getSubscriberFullName(boolean lastFirst){
        String result = lastFirst ? this.getInsuredLastName() : this.getInsuredFirstName();
        if(result != null){
            result = result.trim();
            if(lastFirst){
                result += ",";
            }
        }
        String middleName = this.getInsuredMiddleName() != null && this.getInsuredMiddleName().trim().length() > 0 ? this.getInsuredMiddleName().trim() : null;
        if(lastFirst){
            result += (this.getInsuredFirstName() != null ? (" " + this.getInsuredFirstName().trim()) : "");
            if(middleName != null){
                result += " " + middleName.charAt(0);
            }
        }else{
            if(middleName != null){
                result += " " + middleName.charAt(0);
            }
            result += (this.getInsuredLastName() != null ? (" " + this.getInsuredLastName().trim()) : "");
        }
        return result.trim();
    }

    public String getSubscriberCSZ(){
        String result = "";
        if(this.getInsuredCity() != null && this.getInsuredCity().trim().length() > 0){
            result += this.getInsuredCity().trim();
        }
        if(this.getInsuredState() != null && this.getInsuredState().trim().length() > 0){
            result += ", " + this.getInsuredState().trim();
        }
        if(this.getInsuredZipCode() != null && this.getInsuredZipCode().trim().length() > 0){
            result += " " + this.getInsuredZipCode().trim();
        }
        return result;
    }

    public String getInsuranceCSZ(){
        String result = "";
        if(this.getAddressCity() != null && this.getAddressCity().trim().length() > 0){
            result += this.getAddressCity().trim();
        }
        if(this.getAddressState() != null && this.getAddressState().trim().length() > 0){
            result += ", " + this.getAddressState().trim();
        }
        if(this.getAddressZipCode() != null && this.getAddressZipCode().trim().length() > 0){
            result += " " + this.getAddressZipCode().trim();
        }
        return result;
    }

    public String getSubscriberId(){
        if(this.getPolicyNumber() != null && this.getPolicyNumber().trim().length() > 0){
            return this.getPolicyNumber();
        }else if(this.getMemberId() != null && this.getMemberId().trim().length() > 0){
            return this.getMemberId();
        }else if(this.getInsuredSocialSecurityNumber() != null && this.getInsuredSocialSecurityNumber().trim().length() > 0){
            return this.getInsuredSocialSecurityNumber();
        }else{
            return "";
        }
    }

    public String getPlanGroupNumber(){
        /*if(this.getPlanNumber() != null && this.getPlanNumber().trim().length() > 0){
            return this.getPlanNumber();
        }else */if(this.getGroupNumber() != null && this.getGroupNumber().trim().length() > 0){
            return this.getGroupNumber();
        }else{
            return "";
        }
    }
}
