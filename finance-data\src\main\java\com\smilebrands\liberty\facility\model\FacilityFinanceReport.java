package com.smilebrands.liberty.facility.model;

import com.smilebrands.liberty.billing.model.PatientLedger;
import com.smilebrands.liberty.billing.model.PatientPaymentAdjustmentLedger;
import com.smilebrands.liberty.calendar.model.ScheduleEvent;
import com.smilebrands.liberty.constants.BatchType;
import com.smilebrands.liberty.model.BaseObject;
import com.smilebrands.liberty.serializer.DateIsoDeSerializer;
import com.smilebrands.liberty.serializer.DateIsoSerializer;
import com.smilebrands.liberty.treatment.model.Treatment;
import org.codehaus.jackson.map.annotate.JsonDeserialize;
import org.codehaus.jackson.map.annotate.JsonSerialize;

import java.math.BigDecimal;
import java.util.Date;

/**
 * Created with IntelliJ IDEA.
 * User: Phong Pham
 * Date: 1/6/13
 * Time: 5:48 PM
 * To change this template use File | Settings | File Templates.
 */
public class FacilityFinanceReport extends BaseObject {

    private Long patientId;
    private String type;
    private Date startDate;
    private BigDecimal patientAmount;
    private BigDecimal insuranceAmount;
    private BigDecimal totalAmount;

    private ScheduleEvent eventsForTransaction;
    private Treatment treatment;
    private PatientLedger patientLedger;
    private BatchType batchType;
    private PatientPaymentAdjustmentLedger adjustmentLedger;

    public Long getPatientId() {
        return patientId;
    }

    public void setPatientId(Long patientId) {
        this.patientId = patientId;
    }

    public String getType() {
        return type;
    }

    public void setType(String type) {
        this.type = type;
    }

    @JsonSerialize(using= DateIsoSerializer.class)
    public Date getStartDate() {
        return startDate;
    }

    @JsonDeserialize(using=DateIsoDeSerializer.class)
    public void setStartDate(Date startDate) {
        this.startDate = startDate;
    }

    public BigDecimal getPatientAmount() {
        return patientAmount;
    }

    public void setPatientAmount(BigDecimal patientAmount) {
        this.patientAmount = patientAmount;
    }

    public BigDecimal getInsuranceAmount() {
        return insuranceAmount;
    }

    public void setInsuranceAmount(BigDecimal insuranceAmount) {
        this.insuranceAmount = insuranceAmount;
    }

    public BigDecimal getTotalAmount() {
        return totalAmount;
    }

    public void setTotalAmount(BigDecimal totalAmount) {
        this.totalAmount = totalAmount;
    }

    public ScheduleEvent getEventsForTransaction() {
        return eventsForTransaction;
    }

    public void setEventsForTransaction(ScheduleEvent eventsForTransaction) {
        this.eventsForTransaction = eventsForTransaction;
    }

    public Treatment getTreatment() {
        return treatment;
    }

    public void setTreatment(Treatment treatment) {
        this.treatment = treatment;
    }

    public PatientLedger getPatientLedger() {
        return patientLedger;
    }

    public void setPatientLedger(PatientLedger patientLedger) {
        this.patientLedger = patientLedger;
    }

    public PatientPaymentAdjustmentLedger getAdjustmentLedger() {
        return adjustmentLedger;
    }

    public void setAdjustmentLedger(PatientPaymentAdjustmentLedger adjustmentLedger) {
        this.adjustmentLedger = adjustmentLedger;
    }

    public BatchType getBatchType() {
        return batchType;
    }

    public void setBatchType(BatchType batchType) {
        this.batchType = batchType;
    }


    @Override
    public boolean equals(Object o) {
        if (this == o) return true;
        if (o == null || (this.getClass() != o.getClass())) return false;

        FacilityFinanceReport that = (FacilityFinanceReport) o;

        if (!patientId.equals(that.patientId)) return false;
        if (!type.equals(that.type)) return false;
        if (!patientAmount.equals(that.patientAmount)) return false;
        if (!insuranceAmount.equals(that.insuranceAmount)) return false;
        if (!totalAmount.equals(that.totalAmount)) return false;

        if (treatment != null) {
            if (!treatment.getTreatmentId().equals(that.treatment.getTreatmentId())) {
                return false;
            }
        } else {
            if (that.treatment != null) {
                return false;
            }
        }

        if (patientLedger != null) {
            if (!patientLedger.getPatientLedgerId().equals(that.patientLedger.getPatientLedgerId())) {
                return false;
            }
        } else {
            if (that.patientLedger != null) {
                return false;
            }
        }

        if (adjustmentLedger != null) {
            if (!adjustmentLedger.getPaymentAdjustmentCodeId().equals(that.adjustmentLedger.getPaymentAdjustmentCodeId())) {
                return false;
            }
        } else {
            if (that.adjustmentLedger != null) {
                return false;
            }
        }

        return true;
    }

    @Override
    public int hashCode() {
        int result = patientId.hashCode();
        result = 31 * result + type.hashCode();
        result = 31 * result + patientAmount.hashCode();
        result = 31 * result + insuranceAmount.hashCode();
        result = 31 * result + totalAmount.hashCode();
        result = 31 * result + (treatment != null ? treatment.getTreatmentId().hashCode() : 0);
        result = 31 * result + (patientLedger != null ? patientLedger.getPatientLedgerId().hashCode() : 0);
        result = 31 * result + (adjustmentLedger != null ? adjustmentLedger.getPaymentAdjustmentCodeId().hashCode() : 0);
        return result;
    }

}