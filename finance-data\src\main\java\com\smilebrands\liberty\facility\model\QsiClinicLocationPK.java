package com.smilebrands.liberty.facility.model;

import javax.persistence.Basic;
import javax.persistence.Column;
import javax.persistence.Embeddable;
import java.io.Serializable;

/**
 *
 * <AUTHOR>
 */
@Embeddable
public class QsiClinicLocationPK implements Serializable {

	private static final long serialVersionUID = 992826705811988278L;

	@Basic(optional = false)
    @Column(name = "CLINIC_ID")
    private Integer clinicId;

    @Basic(optional = false)
    @Column(name = "FACILITY_ID")
    private Integer facilityId;

    @Basic(optional = false)
    @Column(name = "LOCATION_ID")
    private String locationId;

    public QsiClinicLocationPK() {}

    @Override
    public boolean equals(Object object) {
        // TODO: Warning - this method won't work in the case the id fields are not set
        if (!(object instanceof QsiClinicLocationPK)) {
            return false;
        }
        QsiClinicLocationPK other = (QsiClinicLocationPK) object;
        if ((this.getFacilityId() == null && other.getFacilityId() != null) || (this.getFacilityId() != null && !this.facilityId.equals(other.facilityId))) {
            return false;
        }
        if ((this.getClinicId() == null && other.getClinicId() != null) || (this.getClinicId() != null && !this.clinicId.equals(other.clinicId))) {
            return false;
        }
        if ((this.getLocationId() == null && other.getLocationId() != null) || (this.getLocationId() != null && !this.locationId.equals(other.locationId))) {
            return false;
        }
        return true;
    }

    @Override
    public String toString() {
        return "com.smilebrands.cal.model.jpa.QsiClinicLocationPK[facilityId=" + getFacilityId() + ", clinicId=" + getClinicId() + "]";
    }

    public Integer getClinicId() {
        return clinicId;
    }

    public void setClinicId(Integer clinicId) {
        this.clinicId = clinicId;
    }

    public Integer getFacilityId() {
        return facilityId;
    }

    public void setFacilityId(Integer facilityId) {
        this.facilityId = facilityId;
    }

    public String getLocationId() {
        return locationId;
    }

    public void setLocationId(String locationId) {
        this.locationId = locationId;
    }

    @Override
    public int hashCode() {
        int result = clinicId.hashCode();
        result = 31 * result + facilityId.hashCode();
        result = 31 * result + locationId.hashCode();
        return result;
    }
}




