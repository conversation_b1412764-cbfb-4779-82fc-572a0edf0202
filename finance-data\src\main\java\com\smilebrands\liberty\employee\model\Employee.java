package com.smilebrands.liberty.employee.model;

import com.smilebrands.liberty.facility.model.Facility;
import com.smilebrands.liberty.model.BaseObject;
import javax.persistence.*;
import org.hibernate.annotations.NotFound;
import org.hibernate.annotations.NotFoundAction;

@Entity
@Table(name = "EMPLOYEE")
@NamedQueries({
    @NamedQuery(name = "Employee.findEmployeesByName", query = "SELECT e FROM Employee e WHERE e.employmentStatus = 'Active' AND (Lower(e.firstName) LIKE :firstName OR Lower(e.lastName) LIKE :lastName) ORDER BY e.firstName, e.lastName"),
    @NamedQuery(name = "Employee.findEmployeesByFacilityId", query = "SELECT e FROM Employee e WHERE e.employmentStatus = 'Active' AND e.facility.facilityId = ?1"),
    @NamedQuery(name = "Employee.findEmployeesByTitle", query = "SELECT e FROM Employee e WHERE e.employmentStatus = 'Active' AND e.title = ?1 ORDER BY e.firstName, e.lastName")
})
public class Employee extends BaseObject {

    private static final long serialVersionUID = 1L;

    @Id
    @Basic(optional = false)
    @Column(name="EMPLOYEE_NUMBER")
    private Integer employeeNumber;

    @Column(name="FIRST_NAME")
    private String firstName;

    @Column(name="LAST_NAME")
    private String lastName;

    @Column(name="EMPLOYMENT_STATUS")
    private String employmentStatus;

    @Column(name="EMPLOYMENT_TYPE")
    private String employmentType;

    @Column(name="TITLE")
    private String title;

    @Column(name="EMAIL_ADDRESS")
    private String emailAddress;

    @NotFound(action=NotFoundAction.IGNORE)
    @OneToOne(optional=true)
    @JoinColumn(name="FACILITY_ID", insertable=false, updatable=false)
    private Facility facility;

    @NotFound(action=NotFoundAction.IGNORE)
    @OneToOne(optional=true)
    @JoinColumn(name="MANAGER_EMPLOYEE_NUMBER", insertable=false, updatable=false)
    private Employee manager;

    /**
     * @return the employeeNumber
     */
    public Integer getEmployeeNumber() {
        return employeeNumber;
    }

    /**
     * @param employeeNumber the employeeNumber to set
     */
    public void setEmployeeNumber(Integer employeeNumber) {
        this.employeeNumber = employeeNumber;
    }

    /**
     * @return the firstName
     */
    public String getFirstName() {
        return firstName;
    }

    /**
     * @param firstName the firstName to set
     */
    public void setFirstName(String firstName) {
        this.firstName = firstName;
    }

    /**
     * @return the lastName
     */
    public String getLastName() {
        return lastName;
    }

    /**
     * @param lastName the lastName to set
     */
    public void setLastName(String lastName) {
        this.lastName = lastName;
    }

    /**
     * @return the employmentStatus
     */
    public String getEmploymentStatus() {
        return employmentStatus;
    }

    /**
     * @param employmentStatus the employmentStatus to set
     */
    public void setEmploymentStatus(String employmentStatus) {
        this.employmentStatus = employmentStatus;
    }

    /**
     * @return the employmentType
     */
    public String getEmploymentType() {
        return employmentType;
    }

    /**
     * @param employmentType the employmentType to set
     */
    public void setEmploymentType(String employmentType) {
        this.employmentType = employmentType;
    }

    /**
     * @return the title
     */
    public String getTitle() {
        return title;
    }

    /**
     * @param title the title to set
     */
    public void setTitle(String title) {
        this.title = title;
    }

    @Override
    public int hashCode() {
        int hash = 0;
        hash += (employeeNumber != null ? employeeNumber.hashCode() : 0);
        return hash;
    }

    @Override
    public boolean equals(Object object) {
        // TODO: Warning - this method won't work in the case the id fields are not set
        if (!(object instanceof EmployeeBase)) {
            return false;
        }
        EmployeeBase other = (EmployeeBase) object;
        if ((this.employeeNumber == null && other.getEmployeeNumber() != null) || (this.employeeNumber != null && !this.employeeNumber.equals(other.getEmployeeNumber()))) {
            return false;
        }
        return true;
    }

    /**
     * @return the emailAddress
     */
    public String getEmailAddress() {
        return emailAddress;
    }

    /**
     * @param emailAddress the emailAddress to set
     */
    public void setEmailAddress(String emailAddress) {
        this.emailAddress = emailAddress;
    }
    
    /**
     * @return the facility
     */
    public Facility getFacility() {
        return facility;
    }

    /**
     * @param facility the facility to set
     */
    public void setFacility(Facility facility) {
        this.facility = facility;
    }

    /**
     * @return the manager
     */
    public Employee getManager() {
        return manager;
    }

    /**
     * @param manager the manager to set
     */
    public void setManager(Employee manager) {
        this.manager = manager;
    }
}
