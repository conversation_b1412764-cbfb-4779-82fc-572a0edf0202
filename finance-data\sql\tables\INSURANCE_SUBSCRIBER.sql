--------------------------------------------------------
--  DDL for Table INSURANCE_SUBSCRIBER
--------------------------------------------------------

  CREATE TABLE "LIBERTY_DATA"."INSURANCE_SUBSCRIBER"
   (	"INSURANCE_SUBSCRIBER_ID" NUMBER(10,0), 
	"SUBSCRIBER_FIRST_NAME" VARCHAR2(30 BYTE), 
	"SUBSCRIBER_LAST_NAME" VARCHAR2(30 BYTE), 
	"SUBSCRIBER_DATE_OF_BIRTH" TIMESTAMP (6), 
	"SUBSCRIBER_GENDER" CHAR(1 BYTE), 
	"SUBSCRIBER_ADDRESS_LINE1" VARCHAR2(30 BYTE), 
	"SUBSCRIBER_ADDRESS_LINE2" VARCHAR2(30 BYTE), 
	"SUBSCRIBER_CITY" VARCHAR2(30 BYTE), 
	"SUBSCRIBER_STATE" VARCHAR2(30 BYTE), 
	"SUBSCRIBER_ZIPCODE" VARCHAR2(30 BYTE), 
	"SUBSCRIBER_SSN" VARCHAR2(25 BYTE), 
	"INSURANCE_PLAN" NUMBER(10,0), 
	"QSI_PLAN_ID" NUMBER(10,0), 
	"EMPLOYER_NAME" VARCHAR2(35 BYTE), 
	"INSURANCE_NAME" VARCHAR2(50 BYTE), 
	"GROUP_NUMBER" VARCHAR2(35 BYTE), 
	"MEMBER_NUMBER" VARCHAR2(35 BYTE), 
	"INSURANCE_PHONE_NUMBER" VARCHAR2(14 BYTE), 
	"INSURANCE_PHONE_EXT" VARCHAR2(10 BYTE), 
	"DATE_ENTERED" TIMESTAMP (6), 
	"EMP_ENTERED" NUMBER(6,0), 
	"ACTIVATION_DATE" TIMESTAMP (6), 
	"EXPIRATION_DATE" TIMESTAMP (6), 
	"DATE_UPDATED" TIMESTAMP (6), 
	"EMP_UPDATED" NUMBER(6,0), 
	"INSURANCE_TYPE" VARCHAR2(35 BYTE)
   ) PCTFREE 10 PCTUSED 40 INITRANS 1 MAXTRANS 255 NOCOMPRESS LOGGING
  STORAGE(INITIAL 65536 NEXT 1048576 MINEXTENTS 1 MAXEXTENTS 2147483645
  PCTINCREASE 0 FREELISTS 1 FREELIST GROUPS 1 BUFFER_POOL DEFAULT)
  TABLESPACE "BNDNEW_DATA" ;
