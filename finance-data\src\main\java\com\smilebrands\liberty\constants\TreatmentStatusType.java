package com.smilebrands.liberty.constants;

/**
 * Created with IntelliJ IDEA.
 * User: danta
 * Date: 6/5/13
 * Time: 11:25 AM
 * To change this template use File | Settings | File Templates.
 */
public enum TreatmentStatusType {

    PROPOSED("Proposed") {
        @Override
        public String getDescription() {
            return "Treatment has been recommended by a provider";
        }
    },
    APPROVED("Approved") {
        @Override
        public String getDescription () {
            return "Treatment has been financially approved by insurance.";
        }
    },
    DENIED("Denied") {
        @Override
        public String getDescription () {
            return "Treatment has been financially denied by insurance.";
        }
    },
    ACCEPTED("Accepted") {
        @Override
        public String getDescription() {
            return "Treatment has been accepted by the patient";
        }
    },
    SCHEDULED("Scheduled") {
        @Override
        public String getDescription() {
            return "Treatment has been scheduled";
        }
    },
    RESCHEDULED("Rescheduled") {
        @Override
        public String getDescription() {
            return "Treatment has been re-scheduled";
        }
    },
    COMPLETED("Completed") {
        @Override
        public String getDescription() {
            return "Treatment has been completed";
        }
    },
    INVALID("Invalid") {
        @Override
        public String getDescription () {
            return "Treatment can no longer be executed";
        }
    },
    INCOMPLETED("Incompleted") {
        @Override
        public String getDescription() {
            return "Treatment was formerly scheduled but not completed";
        }
    },
    DISCARDED("Discarded") {
        @Override
        public String getDescription() {
            return "Treatment has been discarded";
        }
    },
    VOIDED("Voided") {
        @Override
        public String getDescription() {
            return "Treatment's associated patient ledger has been voided";
        }
    },
    REFERRED("Referred") {
        @Override
        public String getDescription() {
            return "Treatment has been referred to another provider.";
        }
    };

    private final String status;
    private TreatmentStatusType(String in) {
        this.status = in;
    }

    public String getDescription(){
        return "";
    }

    @Override
    public String toString(){
        return this.status;
    }
}
