package com.smilebrands.liberty.employee.model;

import java.util.List;
import com.smilebrands.liberty.model.BaseObject;
import com.smilebrands.liberty.provider.model.ServiceProvider;
import org.hibernate.annotations.CacheConcurrencyStrategy;
import javax.persistence.*;

/**
 *
 * <AUTHOR> Jun 9, 2011
 */
@Entity
@Table(name = "EMPLOYEE")
@NamedQueries({
    @NamedQuery(name = "EmployeeBase.findEmployeesByName",
            query = "SELECT e FROM EmployeeBase e WHERE e.employmentStatus = 'Active' AND (Lower(e.firstName) LIKE :firstName OR Lower(e.lastName) LIKE :lastName) ORDER BY e.firstName, e.lastName")
})
@org.hibernate.annotations.Cache(usage = CacheConcurrencyStrategy.NONSTRICT_READ_WRITE)
public class EmployeeBase extends BaseObject {

    private static final long serialVersionUID = 1L;

    @Id
    @Basic(optional = false)
    @Column(name="EMPLOYEE_NUMBER")
    private Integer employeeNumber;

    @Column(name="FIRST_NAME")
    private String firstName;

    @Column(name="LAST_NAME")
    private String lastName;

    @Column(name="EMPLOYMENT_STATUS")
    private String employmentStatus;

    @Column(name="EMPLOYMENT_TYPE")
    private String employmentType;

    @Column(name="TITLE")
    private String title;

    @Column(name="EMAIL_ADDRESS")
    private String emailAddress;

    @Transient
    private String username;

    @Transient
    private List<String> roles;

    @Transient
    private List<Integer> facilityIds;

    @Transient
    private Integer homeFacilityId;

    /*@NotFound(action= NotFoundAction.IGNORE)
    @OneToOne(optional=true)
    @JoinColumn(name="FACILITY_ID", insertable=false, updatable=false)
    private Facility facility;*/

    @Column(name="FACILITY_ID")
    private Integer facility;

    public EmployeeBase() {}

    public EmployeeBase(Integer employeeNumber) {
        this.employeeNumber = employeeNumber;
    }

    /**
     * @return the employeeNumber
     */
    public Integer getEmployeeNumber() {
        return employeeNumber;
    }

    /**
     * @param employeeNumber the employeeNumber to set
     */
    public void setEmployeeNumber(Integer employeeNumber) {
        this.employeeNumber = employeeNumber;
    }

    /**
     * @return the firstName
     */
    public String getFirstName() {
        return firstName;
    }

    /**
     * @param firstName the firstName to set
     */
    public void setFirstName(String firstName) {
        this.firstName = firstName;
    }

    /**
     * @return the lastName
     */
    public String getLastName() {
        return lastName;
    }

    /**
     * @param lastName the lastName to set
     */
    public void setLastName(String lastName) {
        this.lastName = lastName;
    }

    /**
     * @return the employmentStatus
     */
    public String getEmploymentStatus() {
        return employmentStatus;
    }

    /**
     * @param employmentStatus the employmentStatus to set
     */
    public void setEmploymentStatus(String employmentStatus) {
        this.employmentStatus = employmentStatus;
    }

    /**
     * @return the employmentType
     */
    public String getEmploymentType() {
        return employmentType;
    }

    /**
     * @param employmentType the employmentType to set
     */
    public void setEmploymentType(String employmentType) {
        this.employmentType = employmentType;
    }

    /**
     * @return the title
     */
    public String getTitle() {
        return title;
    }

    /**
     * @return the emailAddress
     */
    public String getEmailAddress() {
        return emailAddress;
    }

    /**
     * @param emailAddress the emailAddress to set
     */
    public void setEmailAddress(String emailAddress) {
        this.emailAddress = emailAddress;
    }

    /**
     * @param title the title to set
     */
    public void setTitle(String title) {
        this.title = title;
    }

    @Override
    public int hashCode() {
        int hash = 0;
        hash += (employeeNumber != null ? employeeNumber.hashCode() : 0);
        return hash;
    }

    @Override
    public boolean equals(Object object) {
        // TODO: Warning - this method won't work in the case the id fields are not set
        if (!(object instanceof EmployeeBase)) {
            return false;
        }
        EmployeeBase other = (EmployeeBase) object;
        if ((this.employeeNumber == null && other.getEmployeeNumber() != null) || (this.employeeNumber != null && !this.employeeNumber.equals(other.getEmployeeNumber()))) {
            return false;
        }
        return true;
    }

	public String getUsername() {
		return username;
	}

	public void setUsername(String username) {
		this.username = username;
	}

	public List<String> getRoles() {
		return roles;
	}

	public void setRoles(List<String> roles) {
		this.roles = roles;
	}

	public List<Integer> getFacilityIds() {
		return facilityIds;
	}

	public void setFacilityIds(List<Integer> facilityIds) {
		this.facilityIds = facilityIds;
	}

	public Integer getHomeFacilityId() {
		return homeFacilityId;
	}

	public void setHomeFacilityId(Integer homeFacilityId) {
		this.homeFacilityId = homeFacilityId;
	}

    public Integer getFacility() {
        return facility;
    }

    public void setFacility(Integer facility) {
        this.facility = facility;
    }
    
    @Transient
    private ServiceProvider serviceProvider;

    public ServiceProvider getServiceProvider(){
        return this.serviceProvider;
    }
    public void setServiceProvider(ServiceProvider serviceProvider){
        this.serviceProvider = serviceProvider;
    }
}
